"""
Unit tests for dynamic screening scheduling in CMOST.
"""

import unittest
import numpy as np

from cmost.screening.dynamic_scheduler import (
    DynamicScreeningScheduler,
    DynamicSchedulingRules,
    ScreeningEvent,
    ScreeningOutcome,
    RiskLevel
)
from cmost.models.patient import Patient
from cmost.models.genetic_factors import GeneticProfile, SyndromeType


class TestDynamicSchedulingRules(unittest.TestCase):
    """Test cases for DynamicSchedulingRules class."""
    
    def test_default_rules(self):
        """Test default scheduling rules."""
        rules = DynamicSchedulingRules()
        
        # Check base intervals
        self.assertEqual(rules.base_intervals[RiskLevel.VERY_LOW], 15)
        self.assertEqual(rules.base_intervals[RiskLevel.AVERAGE], 10)
        self.assertEqual(rules.base_intervals[RiskLevel.HIGH], 3)
        self.assertEqual(rules.base_intervals[RiskLevel.VERY_HIGH], 1)
        
        # Check finding adjustments
        self.assertEqual(rules.finding_adjustments['no_findings'], 1.0)
        self.assertLess(rules.finding_adjustments['advanced_adenoma'], 1.0)
        self.assertLess(rules.finding_adjustments['cancer'], 0.5)
        
        # Check quality adjustments
        self.assertLess(rules.quality_adjustments['poor_prep'], 1.0)
        self.assertGreater(rules.quality_adjustments['excellent_quality'], 1.0)
    
    def test_custom_rules(self):
        """Test custom scheduling rules."""
        custom_intervals = {
            RiskLevel.LOW: 12,
            RiskLevel.AVERAGE: 8,
            RiskLevel.HIGH: 2
        }
        
        rules = DynamicSchedulingRules(base_intervals=custom_intervals)
        
        self.assertEqual(rules.base_intervals[RiskLevel.LOW], 12)
        self.assertEqual(rules.base_intervals[RiskLevel.AVERAGE], 8)
        self.assertEqual(rules.base_intervals[RiskLevel.HIGH], 2)


class TestScreeningEvent(unittest.TestCase):
    """Test cases for ScreeningEvent class."""
    
    def test_screening_event_creation(self):
        """Test screening event creation."""
        event = ScreeningEvent(
            year=2023,
            patient_id=1,
            test_type="colonoscopy",
            outcome=ScreeningOutcome.POSITIVE_BENIGN,
            findings={"polyps": [1, 2], "total_detected": 2},
            risk_level=RiskLevel.MODERATE,
            risk_score=2.5
        )
        
        self.assertEqual(event.year, 2023)
        self.assertEqual(event.patient_id, 1)
        self.assertEqual(event.test_type, "colonoscopy")
        self.assertEqual(event.outcome, ScreeningOutcome.POSITIVE_BENIGN)
        self.assertEqual(event.risk_level, RiskLevel.MODERATE)
        self.assertEqual(event.risk_score, 2.5)
        self.assertEqual(event.findings["total_detected"], 2)


class TestDynamicScreeningScheduler(unittest.TestCase):
    """Test cases for DynamicScreeningScheduler class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.scheduler = DynamicScreeningScheduler()
        
        # Create test patients with different risk profiles
        self.low_risk_patient = Patient(
            id=1,
            age=55,
            gender='M',
            risk_factors={'smoking': 1.0}
        )
        
        self.high_risk_patient = Patient(
            id=2,
            age=60,
            gender='F',
            risk_factors={'family_history': 3.0}
        )
        
        # Patient with Lynch syndrome
        self.lynch_patient = Patient(
            id=3,
            age=45,
            gender='M',
            risk_factors={}
        )
        lynch_profile = GeneticProfile()
        from cmost.models.genetic_factors import MMRGene
        lynch_profile.mmr_mutations[MMRGene.MLH1] = True
        lynch_profile._update_syndrome_classification()
        self.lynch_patient.genetic_profile = lynch_profile
    
    def test_risk_assessment_low_risk(self):
        """Test risk assessment for low-risk patient."""
        risk_level, risk_score = self.scheduler.assess_patient_risk(self.low_risk_patient, 2023)

        # Risk assessment can vary significantly based on genetic factors
        # Just verify the function works and returns valid values
        self.assertIsInstance(risk_level, RiskLevel)
        self.assertGreater(risk_score, 0)
        self.assertIsInstance(risk_score, (int, float))

        # Verify the assessment is cached
        risk_level2, risk_score2 = self.scheduler.assess_patient_risk(self.low_risk_patient, 2023)
        self.assertEqual(risk_level, risk_level2)
        self.assertEqual(risk_score, risk_score2)
    
    def test_risk_assessment_high_risk(self):
        """Test risk assessment for high-risk patient."""
        risk_level, risk_score = self.scheduler.assess_patient_risk(self.high_risk_patient, 2023)
        
        self.assertIn(risk_level, [RiskLevel.MODERATE, RiskLevel.HIGH, RiskLevel.VERY_HIGH])
        self.assertGreater(risk_score, 1.0)
    
    def test_risk_assessment_lynch_syndrome(self):
        """Test risk assessment for Lynch syndrome patient."""
        risk_level, risk_score = self.scheduler.assess_patient_risk(self.lynch_patient, 2023)
        
        self.assertIn(risk_level, [RiskLevel.HIGH, RiskLevel.VERY_HIGH])
        self.assertGreater(risk_score, 5.0)
    
    def test_risk_assessment_caching(self):
        """Test risk assessment caching."""
        # First assessment
        risk_level1, risk_score1 = self.scheduler.assess_patient_risk(self.low_risk_patient, 2023)
        
        # Second assessment (should use cache)
        risk_level2, risk_score2 = self.scheduler.assess_patient_risk(self.low_risk_patient, 2023)
        
        self.assertEqual(risk_level1, risk_level2)
        self.assertEqual(risk_score1, risk_score2)
        
        # Assessment for different year (should not use cache)
        risk_level3, risk_score3 = self.scheduler.assess_patient_risk(self.low_risk_patient, 2024)
        
        # May be different due to age change
        self.assertIsInstance(risk_level3, RiskLevel)
        self.assertGreater(risk_score3, 0)
    
    def test_interval_calculation_no_findings(self):
        """Test interval calculation with no findings."""
        risk_level, risk_score = self.scheduler.assess_patient_risk(self.low_risk_patient, 2023)
        
        screening_event = ScreeningEvent(
            year=2023,
            patient_id=self.low_risk_patient.id,
            test_type="colonoscopy",
            outcome=ScreeningOutcome.NEGATIVE,
            findings={"total_detected": 0},
            risk_level=risk_level,
            risk_score=risk_score
        )
        
        interval = self.scheduler.calculate_next_screening_interval(
            self.low_risk_patient, screening_event, 2023
        )
        
        # Should be close to base interval for risk level
        base_interval = self.scheduler.rules.base_intervals[risk_level]
        self.assertGreaterEqual(interval, 1)
        self.assertLessEqual(interval, 15)
        self.assertAlmostEqual(interval, base_interval, delta=3)
    
    def test_interval_calculation_with_findings(self):
        """Test interval calculation with positive findings."""
        risk_level, risk_score = self.scheduler.assess_patient_risk(self.low_risk_patient, 2023)
        
        # Screening with advanced adenoma
        screening_event = ScreeningEvent(
            year=2023,
            patient_id=self.low_risk_patient.id,
            test_type="colonoscopy",
            outcome=ScreeningOutcome.POSITIVE_ADVANCED,
            findings={"advanced_adenomas": 1, "total_detected": 1},
            risk_level=risk_level,
            risk_score=risk_score
        )
        
        interval = self.scheduler.calculate_next_screening_interval(
            self.low_risk_patient, screening_event, 2023
        )
        
        # Should be shorter than base interval or at minimum interval
        base_interval = self.scheduler.rules.base_intervals[risk_level]
        self.assertLessEqual(interval, base_interval)
        self.assertGreaterEqual(interval, 1)
        # For advanced adenoma, should be significantly shorter
        self.assertLessEqual(interval, 3)
    
    def test_interval_calculation_poor_quality(self):
        """Test interval calculation with poor quality exam."""
        risk_level, risk_score = self.scheduler.assess_patient_risk(self.low_risk_patient, 2023)
        
        screening_event = ScreeningEvent(
            year=2023,
            patient_id=self.low_risk_patient.id,
            test_type="colonoscopy",
            outcome=ScreeningOutcome.NEGATIVE,
            findings={"total_detected": 0},
            risk_level=risk_level,
            risk_score=risk_score,
            bowel_prep_quality="poor",
            completion_rate=0.8
        )
        
        interval = self.scheduler.calculate_next_screening_interval(
            self.low_risk_patient, screening_event, 2023
        )
        
        # Should be shorter due to poor quality or at minimum interval
        base_interval = self.scheduler.rules.base_intervals[risk_level]
        self.assertLessEqual(interval, base_interval)
        # Poor quality should result in shorter interval
        self.assertLessEqual(interval, max(1, base_interval // 2))
    
    def test_schedule_next_screening(self):
        """Test scheduling next screening."""
        screening_result = {
            'test': 'colonoscopy',
            'result': 'negative',
            'findings': {'total_detected': 0},
            'bowel_prep_quality': 'adequate',
            'completion_rate': 1.0
        }
        
        next_year = self.scheduler.schedule_next_screening(
            self.low_risk_patient, screening_result, 2023
        )
        
        self.assertIsNotNone(next_year)
        self.assertGreater(next_year, 2023)
        self.assertLessEqual(next_year, 2023 + 15)
        
        # Check that screening event was recorded
        self.assertIn(self.low_risk_patient.id, self.scheduler.screening_histories)
        history = self.scheduler.screening_histories[self.low_risk_patient.id]
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].year, 2023)
    
    def test_schedule_next_screening_elderly(self):
        """Test scheduling for elderly patient."""
        elderly_patient = Patient(id=4, age=90, gender='M')
        
        screening_result = {
            'test': 'colonoscopy',
            'result': 'negative',
            'findings': {'total_detected': 0}
        }
        
        next_year = self.scheduler.schedule_next_screening(
            elderly_patient, screening_result, 2023
        )
        
        # Should not schedule for very elderly patients
        self.assertIsNone(next_year)
    
    def test_is_screening_due_first_time(self):
        """Test screening due check for first-time screening."""
        # Patient in screening age range
        is_due = self.scheduler.is_screening_due(self.low_risk_patient, 2023)
        self.assertTrue(is_due)
        
        # Patient too young
        young_patient = Patient(id=5, age=40, gender='F')
        is_due = self.scheduler.is_screening_due(young_patient, 2023)
        self.assertFalse(is_due)
        
        # Patient too old
        old_patient = Patient(id=6, age=80, gender='M')
        is_due = self.scheduler.is_screening_due(old_patient, 2023)
        self.assertFalse(is_due)
    
    def test_is_screening_due_with_history(self):
        """Test screening due check with screening history."""
        # Schedule first screening
        screening_result = {
            'test': 'colonoscopy',
            'result': 'negative',
            'findings': {'total_detected': 0}
        }
        
        next_year = self.scheduler.schedule_next_screening(
            self.low_risk_patient, screening_result, 2023
        )
        
        # Should not be due before scheduled year
        is_due = self.scheduler.is_screening_due(self.low_risk_patient, next_year - 1)
        self.assertFalse(is_due)
        
        # Should be due at scheduled year
        is_due = self.scheduler.is_screening_due(self.low_risk_patient, next_year)
        self.assertTrue(is_due)
    
    def test_get_recommended_test_first_time(self):
        """Test test recommendation for first-time screening."""
        # Low risk patient should get FIT
        test = self.scheduler.get_recommended_test(self.low_risk_patient, 2023)
        self.assertIn(test, ['fit', 'sigmoidoscopy', 'colonoscopy'])
        
        # High risk patient should get colonoscopy
        test = self.scheduler.get_recommended_test(self.high_risk_patient, 2023)
        self.assertEqual(test, 'colonoscopy')
    
    def test_get_recommended_test_with_history(self):
        """Test test recommendation with screening history."""
        # Schedule first screening with positive findings
        screening_result = {
            'test': 'fit',
            'result': 'positive',
            'findings': {'polyps': [1], 'total_detected': 1}
        }
        
        self.scheduler.schedule_next_screening(
            self.low_risk_patient, screening_result, 2023
        )
        
        # Should recommend colonoscopy after positive FIT
        test = self.scheduler.get_recommended_test(self.low_risk_patient, 2024)
        self.assertEqual(test, 'colonoscopy')
    
    def test_screening_summary(self):
        """Test screening summary generation."""
        # Initially no history
        summary = self.scheduler.get_screening_summary(self.low_risk_patient.id)
        self.assertEqual(summary['total_screenings'], 0)
        
        # Add screening events
        screening_result1 = {
            'test': 'colonoscopy',
            'result': 'negative',
            'findings': {'total_detected': 0}
        }
        
        screening_result2 = {
            'test': 'colonoscopy',
            'result': 'positive',
            'findings': {'polyps': [1], 'total_detected': 1}
        }
        
        self.scheduler.schedule_next_screening(self.low_risk_patient, screening_result1, 2023)
        self.scheduler.schedule_next_screening(self.low_risk_patient, screening_result2, 2028)
        
        summary = self.scheduler.get_screening_summary(self.low_risk_patient.id)
        
        self.assertEqual(summary['total_screenings'], 2)
        self.assertEqual(summary['first_screening_year'], 2023)
        self.assertEqual(summary['last_screening_year'], 2028)
        self.assertEqual(summary['positive_screenings'], 1)
        self.assertEqual(len(summary['history']), 2)


if __name__ == '__main__':
    unittest.main()
