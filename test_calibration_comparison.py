#!/usr/bin/env python3
"""
测试调参对比图功能的脚本

该脚本用于验证新的调参对比图是否能正确显示：
- 不同年龄、性别的腺瘤、癌症的基准值
- 校准后模型计算值及其95% CI
"""

import os
import sys
import tempfile
import matplotlib.pyplot as plt

def test_calibration_comparison_plot():
    """测试调参对比图功能"""
    print("测试调参对比图功能...")
    
    try:
        from cmost.calibration.visualization import CalibrationVisualizer
        
        # 创建测试数据
        test_results = {
            'method': 'test_method',
            'error': 0.05,
            'execution_time': 45.2,
            'parameters': {
                'early_adenoma_rate': 0.025,
                'advanced_adenoma_rate': 0.008,
                'cancer_rate': 0.0005
            },
            'convergence_info': {
                'converged': True
            }
        }
        
        # 模拟基准值数据
        test_benchmarks = {
            'early_adenoma_prevalence': {
                'M': {50: 30.0, 60: 40.0, 70: 45.0},
                'F': {50: 25.0, 60: 35.0, 70: 40.0}
            },
            'advanced_adenoma_prevalence': {
                'M': {50: 8.0, 60: 12.0, 70: 15.0},
                'F': {50: 6.0, 60: 10.0, 70: 13.0}
            },
            'cancer_incidence': {
                'M': {50: 50.0, 60: 100.0, 70: 150.0},
                'F': {50: 40.0, 60: 80.0, 70: 120.0}
            }
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建可视化器
            visualizer = CalibrationVisualizer(temp_dir)
            
            # 测试调参对比图
            plot_path = visualizer.plot_calibration_results_comparison(
                test_results, test_benchmarks
            )
            
            if plot_path and os.path.exists(plot_path):
                print(f"✓ 调参对比图生成成功: {plot_path}")
                
                # 检查图片文件大小
                file_size = os.path.getsize(plot_path)
                if file_size > 1000:  # 至少1KB
                    print(f"✓ 图片文件大小正常: {file_size} bytes")
                else:
                    print(f"⚠ 图片文件可能有问题: {file_size} bytes")
                
                return True
            else:
                print("✗ 调参对比图生成失败")
                return False
                
    except Exception as e:
        print(f"✗ 调参对比图测试失败: {e}")
        return False

def test_visualization_components():
    """测试可视化组件"""
    print("测试可视化组件...")
    
    try:
        import matplotlib
        import numpy as np
        
        # 测试matplotlib后端
        print(f"✓ Matplotlib版本: {matplotlib.__version__}")
        print(f"✓ Matplotlib后端: {matplotlib.get_backend()}")
        
        # 测试基本绘图功能
        fig, ax = plt.subplots(1, 1, figsize=(8, 6))
        
        # 模拟数据
        ages = [50, 60, 70]
        benchmark_values = [30, 40, 45]
        model_values = [32, 38, 43]
        ci_lower = [28, 35, 40]
        ci_upper = [36, 42, 47]
        
        x_pos = np.arange(len(ages))
        width = 0.35
        
        # 绘制柱状图
        bars1 = ax.bar(x_pos - width/2, benchmark_values, width, 
                      label='基准值', color='lightblue', alpha=0.8)
        bars2 = ax.bar(x_pos + width/2, model_values, width,
                      label='校准后模型值', color='lightcoral', alpha=0.8)
        
        # 添加误差线
        ci_errors = [
            [mv - cl for mv, cl in zip(model_values, ci_lower)],
            [cu - mv for mv, cu in zip(model_values, ci_upper)]
        ]
        ax.errorbar(x_pos + width/2, model_values, yerr=ci_errors, 
                   fmt='none', color='red', capsize=5, capthick=2, alpha=0.7)
        
        ax.set_title('测试图表')
        ax.set_xlabel('年龄')
        ax.set_ylabel('率值')
        ax.set_xticks(x_pos)
        ax.set_xticklabels(ages)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存测试图片
        test_path = "test_plot.png"
        plt.savefig(test_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        if os.path.exists(test_path):
            print("✓ 基本绘图功能正常")
            os.remove(test_path)  # 清理测试文件
            return True
        else:
            print("✗ 基本绘图功能异常")
            return False
            
    except Exception as e:
        print(f"✗ 可视化组件测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("测试数据处理功能...")
    
    try:
        from cmost.calibration.visualization import CalibrationVisualizer
        
        # 创建可视化器实例
        visualizer = CalibrationVisualizer()
        
        # 测试数据处理方法
        benchmark_data = {50: 30.0, 60: 40.0, 70: 45.0}
        results = {
            'parameters': {'early_adenoma_rate': 0.025},
            'error': 0.1
        }
        
        # 调用私有方法进行测试
        model_results = visualizer._simulate_model_results(benchmark_data, results)
        
        # 验证结果
        if isinstance(model_results, dict) and len(model_results) == 3:
            print("✓ 数据处理功能正常")
            
            # 检查每个年龄组的结果
            for age, result in model_results.items():
                if 'value' in result and 'ci_lower' in result and 'ci_upper' in result:
                    print(f"  年龄{age}: 值={result['value']:.2f}, CI=[{result['ci_lower']:.2f}, {result['ci_upper']:.2f}]")
                else:
                    print(f"✗ 年龄{age}的结果格式不正确")
                    return False
            
            return True
        else:
            print("✗ 数据处理结果格式不正确")
            return False
            
    except Exception as e:
        print(f"✗ 数据处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试调参对比图功能...\n")
    
    tests = [
        test_visualization_components,
        test_data_processing,
        test_calibration_comparison_plot
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # 添加空行分隔
        except Exception as e:
            print(f"测试执行出错: {e}\n")
    
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        print("\n新的调参对比图功能已准备就绪，包含：")
        print("- 不同年龄、性别的腺瘤患病率对比")
        print("- 癌症发病率的基准值 vs 校准后模型值")
        print("- 校准后模型值的95%置信区间")
        print("- 按性别分组的详细对比分析")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
