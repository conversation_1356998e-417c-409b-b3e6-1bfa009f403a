"""
Statistical utilities for CMOST.
"""
from typing import Dict, List, Optional, Tuple, Any, Union
import numpy as np
import pandas as pd
from ..models.patient import Patient
from ..config.settings import Settings

def calculate_statistics(patients: List[Patient], settings: Settings) -> Dict[str, Any]:
    """Calculate summary statistics from simulation results.
    
    Args:
        patients: List of patient objects
        settings: Simulation settings
        
    Returns:
        Dictionary containing summary statistics
    """
    # Basic demographics
    total_patients = len(patients)
    ages = [patient.age for patient in patients]
    genders = [patient.gender for patient in patients]
    
    # Count patients by gender
    males = sum(1 for g in genders if g == 'M')
    females = sum(1 for g in genders if g == 'F')
    
    # Disease statistics
    patients_with_polyps = sum(1 for p in patients if p.polyps)
    patients_with_cancer = sum(1 for p in patients if p.cancers)
    
    # Polyp and cancer counts
    polyp_counts = [len(p.polyps) for p in patients]
    cancer_counts = [len(p.cancers) for p in patients]
    
    # Age statistics
    age_mean = np.mean(ages)
    age_median = np.median(ages)
    age_std = np.std(ages)
    
    # Risk score statistics
    risk_scores = [p.risk_score for p in patients]
    risk_mean = np.mean(risk_scores)
    risk_median = np.median(risk_scores)
    risk_std = np.std(risk_scores)
    
    # Polyp stage distribution
    polyp_stages = []
    for patient in patients:
        for polyp in patient.polyps:
            polyp_stages.append(polyp.stage)
    
    stage_counts = {}
    for stage in range(1, 7):  # Polyp stages 1-6
        stage_counts[f'stage_{stage}'] = polyp_stages.count(stage)
    
    # Cancer stage distribution
    cancer_stages = []
    for patient in patients:
        for cancer in patient.cancers:
            cancer_stages.append(cancer.stage)
    
    for stage in range(7, 11):  # Cancer stages 7-10
        stage_counts[f'stage_{stage}'] = cancer_stages.count(stage)
    
    # Incidence and mortality rates
    # (These would typically be calculated per 100,000 person-years)
    # This is a simplified calculation
    cancer_incidence = patients_with_cancer / total_patients * 100000
    cancer_deaths = sum(1 for p in patients if p.cause_of_death == 'cancer')
    cancer_mortality = cancer_deaths / total_patients * 100000
    
    # Return compiled statistics
    return {
        'demographics': {
            'total_patients': total_patients,
            'gender_distribution': {
                'male': males / total_patients,
                'female': females / total_patients
            },
            'age_statistics': {
                'mean': age_mean,
                'median': age_median,
                'std': age_std,
                'min': min(ages),
                'max': max(ages)
            },
            'risk_statistics': {
                'mean': risk_mean,
                'median': risk_median,
                'std': risk_std,
                'min': min(risk_scores),
                'max': max(risk_scores)
            }
        },
        'disease': {
            'patients_with_polyps': patients_with_polyps,
            'patients_with_polyps_percent': patients_with_polyps / total_patients * 100,
            'patients_with_cancer': patients_with_cancer,
            'patients_with_cancer_percent': patients_with_cancer / total_patients * 100,
            'mean_polyps_per_patient': np.mean(polyp_counts),
            'mean_cancers_per_patient': np.mean(cancer_counts),
            'stage_distribution': stage_counts
        },
        'rates': {
            'cancer_incidence': cancer_incidence,
            'cancer_mortality': cancer_mortality
        }
    }

def calculate_age_specific_rates(patients: List[Patient], age_groups: List[Tuple[int, int]]) -> Dict[str, Any]:
    """Calculate age-specific incidence and mortality rates.
    
    Args:
        patients: List of patient objects
        age_groups: List of tuples defining age groups (start, end)
        
    Returns:
        Dictionary containing age-specific rates
    """
    results = {}
    
    for start, end in age_groups:
        group_name = f"{start}-{end}"
        
        # Filter patients in this age group
        group_patients = [p for p in patients if start <= p.age <= end]
        total_in_group = len(group_patients)
        
        if total_in_group == 0:
            results[group_name] = {
                'incidence': 0,
                'mortality': 0
            }
            continue
        
        # Calculate incidence and mortality
        cancer_cases = sum(1 for p in group_patients if p.cancers)
        cancer_deaths = sum(1 for p in group_patients if p.cause_of_death == 'cancer')
        
        # Rates per 100,000
        incidence = cancer_cases / total_in_group * 100000
        mortality = cancer_deaths / total_in_group * 100000
        
        results[group_name] = {
            'incidence': incidence,
            'mortality': mortality
        }
    
    return results

def compare_with_benchmarks(simulation_results: Dict[str, Any], benchmarks: Dict[str, Any]) -> Dict[str, Any]:
    """Compare simulation results with benchmark data.
    
    Args:
        simulation_results: Dictionary of simulation results
        benchmarks: Dictionary of benchmark data
        
    Returns:
        Dictionary containing comparison metrics
    """
    comparison = {}
    
    # Compare incidence rates
    if 'incidence' in benchmarks and 'rates' in simulation_results:
        sim_incidence = simulation_results['rates']['cancer_incidence']
        benchmark_incidence = benchmarks['incidence']
        
        comparison['incidence'] = {
            'simulation': sim_incidence,
            'benchmark': benchmark_incidence,
            'difference': sim_incidence - benchmark_incidence,
            'percent_difference': (sim_incidence - benchmark_incidence) / benchmark_incidence * 100
        }
    
    # Compare mortality rates
    if 'mortality' in benchmarks and 'rates' in simulation_results:
        sim_mortality = simulation_results['rates']['cancer_mortality']
        benchmark_mortality = benchmarks['mortality']
        
        comparison['mortality'] = {
            'simulation': sim_mortality,
            'benchmark': benchmark_mortality,
            'difference': sim_mortality - benchmark_mortality,
            'percent_difference': (sim_mortality - benchmark_mortality) / benchmark_mortality * 100
        }
    
    # Compare stage distribution
    if 'stage_distribution' in benchmarks and 'disease' in simulation_results:
        sim_stages = simulation_results['disease']['stage_distribution']
        benchmark_stages = benchmarks['stage_distribution']
        
        stage_comparison = {}
        for stage in benchmark_stages:
            if f'stage_{stage}' in sim_stages:
                sim_value = sim_stages[f'stage_{stage}']
                benchmark_value = benchmark_stages[stage]
                
                stage_comparison[stage] = {
                    'simulation': sim_value,
                    'benchmark': benchmark_value,
                    'difference': sim_value - benchmark_value,
                    'percent_difference': (sim_value - benchmark_value) / benchmark_value * 100 if benchmark_value != 0 else float('inf')
                }
        
        comparison['stage_distribution'] = stage_comparison
    
    return comparison


def calculate_incidence_rates(patients: List[Patient], years_simulated: int) -> Dict[str, float]:
    """Calculate cancer incidence rates.

    Args:
        patients: List of patient objects
        years_simulated: Number of years simulated

    Returns:
        Dict containing incidence rates
    """
    total_patients = len(patients)
    cancer_cases = sum(1 for p in patients if len(p.cancers) > 0)

    # Calculate crude incidence rate per 100,000 person-years
    person_years = total_patients * years_simulated
    crude_rate = (cancer_cases / person_years) * 100000 if person_years > 0 else 0

    return {
        'crude_incidence_rate': crude_rate,
        'cancer_cases': cancer_cases,
        'total_patients': total_patients,
        'person_years': person_years
    }


def calculate_mortality_rates(patients: List[Patient], years_simulated: int) -> Dict[str, float]:
    """Calculate cancer mortality rates.

    Args:
        patients: List of patient objects
        years_simulated: Number of years simulated

    Returns:
        Dict containing mortality rates
    """
    total_patients = len(patients)
    cancer_deaths = sum(1 for p in patients if p.death_cause == 'cancer')

    # Calculate crude mortality rate per 100,000 person-years
    person_years = total_patients * years_simulated
    crude_rate = (cancer_deaths / person_years) * 100000 if person_years > 0 else 0

    return {
        'crude_mortality_rate': crude_rate,
        'cancer_deaths': cancer_deaths,
        'total_patients': total_patients,
        'person_years': person_years
    }


def calculate_screening_metrics(patients: List[Patient]) -> Dict[str, Any]:
    """Calculate screening performance metrics.

    Args:
        patients: List of patient objects

    Returns:
        Dict containing screening metrics
    """
    screened_patients = [p for p in patients if len(p.screening_history) > 0]
    total_screenings = sum(len(p.screening_history) for p in patients)

    # Calculate polyps detected through screening
    polyps_detected = 0
    cancers_detected = 0

    for patient in patients:
        for screening in patient.screening_history:
            findings = screening.get('findings', {})
            polyps_detected += findings.get('polyp_count', 0)
            cancers_detected += findings.get('cancer_count', 0)

    return {
        'screened_patients': len(screened_patients),
        'total_screenings': total_screenings,
        'polyps_detected': polyps_detected,
        'cancers_detected': cancers_detected,
        'screening_participation_rate': len(screened_patients) / len(patients) if patients else 0
    }


def calculate_cost_effectiveness(patients: List[Patient], settings: Settings) -> Dict[str, float]:
    """Calculate cost-effectiveness metrics.

    Args:
        patients: List of patient objects
        settings: Simulation settings

    Returns:
        Dict containing cost-effectiveness metrics
    """
    # Placeholder implementation - would need actual cost data
    total_patients = len(patients)
    screening_costs = 0
    treatment_costs = 0

    # Estimate costs (simplified)
    for patient in patients:
        # Screening costs
        screening_costs += len(patient.screening_history) * 1000  # $1000 per screening

        # Treatment costs
        for treatment in patient.treatment_history:
            if 'surgery' in treatment.get('treatment_type', ''):
                treatment_costs += 50000  # $50,000 for surgery
            if 'chemo' in treatment.get('treatment_type', ''):
                treatment_costs += 100000  # $100,000 for chemotherapy

    total_costs = screening_costs + treatment_costs

    # Calculate life years gained (simplified)
    cancer_deaths = sum(1 for p in patients if p.death_cause == 'cancer')
    life_years_gained = cancer_deaths * 10  # Assume 10 years gained per prevented death

    cost_per_life_year = total_costs / life_years_gained if life_years_gained > 0 else float('inf')

    return {
        'total_costs': total_costs,
        'screening_costs': screening_costs,
        'treatment_costs': treatment_costs,
        'life_years_gained': life_years_gained,
        'cost_per_life_year': cost_per_life_year
    }