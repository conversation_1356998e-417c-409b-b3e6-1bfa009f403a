#!/usr/bin/env python3
"""
测试CMOST增强功能的脚本

该脚本用于验证新添加的功能是否正常工作，包括：
1. 人口参数选择功能
2. 调参结果可视化和日志
3. 筛查工具参数配置
4. 结果输出路径
"""

import os
import sys
import json
import tempfile
from pathlib import Path

def test_population_parameters():
    """测试人口参数功能"""
    print("测试人口参数功能...")
    
    try:
        from cmost.config.population_manager import population_manager
        from cmost.config.settings import Settings
        
        # 测试创建模板
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试JSON模板
            json_template = os.path.join(temp_dir, "population_template.json")
            success = population_manager.create_population_template(json_template, "json")
            assert success, "创建JSON模板失败"
            assert os.path.exists(json_template), "JSON模板文件不存在"
            
            # 测试加载JSON模板
            success = population_manager.load_population_file(json_template)
            assert success, "加载JSON模板失败"
            
            # 测试获取参数
            params = population_manager.get_population_parameters()
            assert isinstance(params, dict), "人口参数应该是字典类型"
            assert 'male_proportion' in params, "缺少男性比例参数"
            
            print("✓ 人口参数功能测试通过")
            
    except Exception as e:
        print(f"✗ 人口参数功能测试失败: {e}")
        return False
    
    return True

def test_calibration_visualization():
    """测试调参可视化功能"""
    print("测试调参可视化功能...")
    
    try:
        from cmost.calibration.visualization import CalibrationVisualizer
        from cmost.calibration.logging_manager import CalibrationLogger
        
        # 创建测试数据
        test_results = {
            'method': 'test_method',
            'error': 0.001,
            'execution_time': 10.5,
            'parameters': {
                'param1': 0.5,
                'param2': 1.2,
                'param3': 0.8
            },
            'convergence_info': {
                'converged': True
            }
        }
        
        test_history = [
            {'iteration': 0, 'error': 0.1, 'parameters': {'param1': 0.1, 'param2': 1.0, 'param3': 0.5}},
            {'iteration': 1, 'error': 0.05, 'parameters': {'param1': 0.3, 'param2': 1.1, 'param3': 0.6}},
            {'iteration': 2, 'error': 0.01, 'parameters': {'param1': 0.4, 'param2': 1.15, 'param3': 0.7}},
            {'iteration': 3, 'error': 0.001, 'parameters': {'param1': 0.5, 'param2': 1.2, 'param3': 0.8}}
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试可视化器
            visualizer = CalibrationVisualizer(temp_dir)
            
            # 测试收敛图
            plot_path = visualizer.plot_convergence_history(test_history)
            assert plot_path and os.path.exists(plot_path), "收敛图生成失败"
            
            # 测试参数对比图
            plot_path = visualizer.plot_parameter_comparison(test_results)
            assert plot_path and os.path.exists(plot_path), "参数对比图生成失败"
            
            # 测试完整报告
            report_path = visualizer.create_calibration_report(test_results, test_history)
            assert report_path and os.path.exists(report_path), "调参报告生成失败"
            
            print("✓ 调参可视化功能测试通过")
            
    except Exception as e:
        print(f"✗ 调参可视化功能测试失败: {e}")
        return False
    
    return True

def test_calibration_logging():
    """测试调参日志功能"""
    print("测试调参日志功能...")
    
    try:
        from cmost.calibration.logging_manager import CalibrationLogger
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建日志管理器
            logger = CalibrationLogger(temp_dir)
            
            # 测试开始会话
            logger.start_calibration_session("test_method", {"param1": 0.1, "param2": 1.0})
            
            # 测试记录迭代
            logger.log_iteration(1, {"param1": 0.2, "param2": 1.1}, 0.05, 1.5)
            logger.log_iteration(2, {"param1": 0.3, "param2": 1.2}, 0.01, 1.2)
            
            # 测试结束会话
            final_result = {
                'error': 0.001,
                'parameters': {"param1": 0.3, "param2": 1.2},
                'converged': True
            }
            logger.end_calibration_session(final_result)
            
            # 测试获取摘要
            summary = logger.get_session_summary()
            assert isinstance(summary, dict), "会话摘要应该是字典类型"
            assert summary['total_iterations'] == 2, "迭代次数不正确"
            
            # 测试导出历史
            export_path = logger.export_history()
            assert os.path.exists(export_path), "导出历史文件不存在"
            
            print("✓ 调参日志功能测试通过")
            
    except Exception as e:
        print(f"✗ 调参日志功能测试失败: {e}")
        return False
    
    return True

def test_output_directory():
    """测试输出目录功能"""
    print("测试输出目录功能...")
    
    try:
        from cmost.ui.output_config_panel import OutputConfigPanel
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        try:
            # 创建输出配置面板
            panel = OutputConfigPanel(root)
            
            # 测试确保输出目录存在
            success = panel.ensure_output_directory()
            assert success, "确保输出目录失败"
            
            # 测试验证配置
            is_valid, error_msg = panel.validate_config()
            # 注意：这里可能会失败，因为没有选择格式和内容，这是正常的
            
            print("✓ 输出目录功能测试通过")
            
        finally:
            root.destroy()
            
    except Exception as e:
        print(f"✗ 输出目录功能测试失败: {e}")
        return False
    
    return True

def test_enhanced_ui_import():
    """测试增强UI导入"""
    print("测试增强UI导入...")
    
    try:
        # 测试导入增强主窗口
        from cmost.ui.enhanced_main_window import EnhancedMainWindow
        
        # 测试导入筛查配置面板
        from cmost.ui.screening_config_panel import ScreeningConfigPanel
        
        # 测试导入输出配置面板
        from cmost.ui.output_config_panel import OutputConfigPanel
        
        print("✓ 增强UI导入测试通过")
        
    except Exception as e:
        print(f"✗ 增强UI导入测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试CMOST增强功能...\n")
    
    tests = [
        test_enhanced_ui_import,
        test_population_parameters,
        test_calibration_visualization,
        test_calibration_logging,
        test_output_directory
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试执行出错: {e}")
        print()
    
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
