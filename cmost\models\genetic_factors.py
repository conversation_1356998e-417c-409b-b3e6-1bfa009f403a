"""
Genetic factors module for CMOST simulation.

This module implements genetic risk factors that influence
colorectal cancer development and progression.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
import numpy as np


class SyndromeType(Enum):
    """Hereditary colorectal cancer syndromes."""
    NONE = "none"
    FAP = "familial_adenomatous_polyposis"
    ATTENUATED_FAP = "attenuated_fap"
    LYNCH = "lynch_syndrome"
    MAP = "mutyh_associated_polyposis"
    SERRATED_POLYPOSIS = "serrated_polyposis_syndrome"
    PEUTZ_JEGHERS = "peutz_jeghers_syndrome"


class MMRGene(Enum):
    """Mismatch repair genes."""
    MLH1 = "mlh1"
    MSH2 = "msh2"
    MSH6 = "msh6"
    PMS2 = "pms2"
    EPCAM = "epcam"


@dataclass
class GeneticProfile:
    """Genetic profile of a patient."""
    
    # Hereditary syndrome
    syndrome: SyndromeType = SyndromeType.NONE
    
    # Specific gene mutations
    apc_mutation: bool = False
    mutyh_mutations: int = 0  # 0, 1, or 2 (biallelic)
    
    # Mismatch repair genes
    mmr_mutations: Dict[MMRGene, bool] = field(default_factory=dict)
    
    # Other high-penetrance genes
    smad4_mutation: bool = False
    bmpr1a_mutation: bool = False
    stk11_mutation: bool = False
    
    # Polygenic risk score (based on common variants)
    polygenic_risk_score: float = 0.0
    
    # Family history details
    family_history_strength: str = "none"  # "none", "weak", "moderate", "strong"
    affected_relatives: int = 0
    early_onset_relatives: int = 0
    
    def __post_init__(self):
        """Initialize derived genetic characteristics."""
        self._update_syndrome_classification()
        self._calculate_penetrance()
    
    def _update_syndrome_classification(self) -> None:
        """Update syndrome classification based on mutations."""
        # Lynch syndrome (MMR mutations)
        if any(self.mmr_mutations.values()):
            self.syndrome = SyndromeType.LYNCH
        
        # FAP (APC mutations)
        elif self.apc_mutation:
            # Determine if classical or attenuated FAP based on mutation location
            # This is simplified - in reality depends on specific mutation
            if np.random.random() < 0.8:  # 80% classical FAP
                self.syndrome = SyndromeType.FAP
            else:
                self.syndrome = SyndromeType.ATTENUATED_FAP
        
        # MAP (biallelic MUTYH mutations)
        elif self.mutyh_mutations >= 2:
            self.syndrome = SyndromeType.MAP
        
        # Peutz-Jeghers syndrome
        elif self.stk11_mutation:
            self.syndrome = SyndromeType.PEUTZ_JEGHERS
    
    def _calculate_penetrance(self) -> None:
        """Calculate lifetime cancer risk based on genetic profile."""
        base_risk = 0.05  # 5% baseline lifetime risk
        
        # High-penetrance syndromes
        if self.syndrome == SyndromeType.FAP:
            self.lifetime_cancer_risk = 0.95  # 95% risk
        elif self.syndrome == SyndromeType.ATTENUATED_FAP:
            self.lifetime_cancer_risk = 0.70  # 70% risk
        elif self.syndrome == SyndromeType.LYNCH:
            # Risk varies by gene
            if MMRGene.MLH1 in self.mmr_mutations or MMRGene.MSH2 in self.mmr_mutations:
                self.lifetime_cancer_risk = 0.80  # 80% risk
            elif MMRGene.MSH6 in self.mmr_mutations:
                self.lifetime_cancer_risk = 0.60  # 60% risk
            elif MMRGene.PMS2 in self.mmr_mutations:
                self.lifetime_cancer_risk = 0.40  # 40% risk
            else:
                self.lifetime_cancer_risk = 0.70  # Average Lynch risk
        elif self.syndrome == SyndromeType.MAP:
            self.lifetime_cancer_risk = 0.60  # 60% risk
        elif self.syndrome == SyndromeType.PEUTZ_JEGHERS:
            self.lifetime_cancer_risk = 0.40  # 40% risk for CRC
        else:
            # Polygenic and family history effects
            risk_multiplier = 1.0
            
            # Polygenic risk score effect
            if self.polygenic_risk_score > 2.0:  # High polygenic risk
                risk_multiplier *= 3.0
            elif self.polygenic_risk_score > 1.0:  # Moderate polygenic risk
                risk_multiplier *= 2.0
            elif self.polygenic_risk_score < -1.0:  # Low polygenic risk
                risk_multiplier *= 0.5
            
            # Family history effect
            if self.family_history_strength == "strong":
                risk_multiplier *= 4.0
            elif self.family_history_strength == "moderate":
                risk_multiplier *= 2.5
            elif self.family_history_strength == "weak":
                risk_multiplier *= 1.5
            
            self.lifetime_cancer_risk = min(0.95, base_risk * risk_multiplier)
    
    def get_age_specific_risk(self, age: int) -> float:
        """Get age-specific cancer risk.
        
        Args:
            age: Patient age
            
        Returns:
            Annual cancer risk at given age
        """
        if self.syndrome == SyndromeType.FAP:
            # FAP has early onset
            if age < 20:
                return 0.001
            elif age < 30:
                return 0.05
            elif age < 40:
                return 0.15
            else:
                return 0.25
        
        elif self.syndrome == SyndromeType.LYNCH:
            # Lynch syndrome has earlier onset than sporadic
            if age < 30:
                return 0.002
            elif age < 40:
                return 0.01
            elif age < 50:
                return 0.03
            elif age < 60:
                return 0.05
            else:
                return 0.08
        
        else:
            # Sporadic risk with genetic modifiers
            base_rates = {
                20: 0.0001, 30: 0.0005, 40: 0.002, 50: 0.008,
                60: 0.02, 70: 0.04, 80: 0.06
            }
            
            # Find closest age
            closest_age = min(base_rates.keys(), key=lambda x: abs(x - age))
            base_risk = base_rates[closest_age]
            
            # Apply genetic modifiers
            if self.polygenic_risk_score > 1.0:
                base_risk *= (1 + self.polygenic_risk_score)
            
            if self.family_history_strength == "strong":
                base_risk *= 3.0
            elif self.family_history_strength == "moderate":
                base_risk *= 2.0
            elif self.family_history_strength == "weak":
                base_risk *= 1.5
            
            return min(0.5, base_risk)
    
    def get_polyp_risk_modifier(self) -> float:
        """Get polyp development risk modifier.
        
        Returns:
            Risk modifier for polyp development
        """
        if self.syndrome == SyndromeType.FAP:
            return 50.0  # Hundreds to thousands of polyps
        elif self.syndrome == SyndromeType.ATTENUATED_FAP:
            return 10.0  # 10-100 polyps
        elif self.syndrome == SyndromeType.MAP:
            return 5.0   # Multiple polyps
        elif self.syndrome == SyndromeType.LYNCH:
            return 2.0   # Slightly increased polyp risk
        else:
            modifier = 1.0
            
            # Polygenic effects
            if self.polygenic_risk_score > 1.0:
                modifier *= (1 + self.polygenic_risk_score * 0.5)
            
            # Family history effects
            if self.family_history_strength == "strong":
                modifier *= 2.0
            elif self.family_history_strength == "moderate":
                modifier *= 1.5
            
            return modifier
    
    def get_screening_recommendations(self) -> Dict[str, Any]:
        """Get screening recommendations based on genetic profile.
        
        Returns:
            Dictionary with screening recommendations
        """
        if self.syndrome == SyndromeType.FAP:
            return {
                'start_age': 12,
                'interval': 1,  # Annual
                'method': 'colonoscopy',
                'special_considerations': 'Consider prophylactic colectomy'
            }
        
        elif self.syndrome == SyndromeType.ATTENUATED_FAP:
            return {
                'start_age': 20,
                'interval': 2,  # Every 2 years
                'method': 'colonoscopy',
                'special_considerations': 'Monitor for extracolonic manifestations'
            }
        
        elif self.syndrome == SyndromeType.LYNCH:
            return {
                'start_age': 25,
                'interval': 2,  # Every 2 years
                'method': 'colonoscopy',
                'special_considerations': 'Screen for extracolonic cancers'
            }
        
        elif self.syndrome == SyndromeType.MAP:
            return {
                'start_age': 25,
                'interval': 2,  # Every 2 years
                'method': 'colonoscopy',
                'special_considerations': 'Monitor polyp burden'
            }
        
        elif self.family_history_strength in ["moderate", "strong"]:
            start_age = 40 if self.family_history_strength == "moderate" else 35
            return {
                'start_age': start_age,
                'interval': 5,  # Every 5 years
                'method': 'colonoscopy',
                'special_considerations': 'Earlier than average-risk screening'
            }
        
        else:
            return {
                'start_age': 50,
                'interval': 10,  # Every 10 years
                'method': 'colonoscopy',
                'special_considerations': 'Average-risk screening'
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert genetic profile to dictionary.
        
        Returns:
            Dictionary representation
        """
        return {
            'syndrome': self.syndrome.value,
            'apc_mutation': self.apc_mutation,
            'mutyh_mutations': self.mutyh_mutations,
            'mmr_mutations': {gene.value: status for gene, status in self.mmr_mutations.items()},
            'smad4_mutation': self.smad4_mutation,
            'bmpr1a_mutation': self.bmpr1a_mutation,
            'stk11_mutation': self.stk11_mutation,
            'polygenic_risk_score': self.polygenic_risk_score,
            'family_history_strength': self.family_history_strength,
            'affected_relatives': self.affected_relatives,
            'early_onset_relatives': self.early_onset_relatives,
            'lifetime_cancer_risk': getattr(self, 'lifetime_cancer_risk', 0.05)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GeneticProfile':
        """Create genetic profile from dictionary.
        
        Args:
            data: Dictionary containing genetic profile data
            
        Returns:
            GeneticProfile instance
        """
        profile = cls(
            syndrome=SyndromeType(data.get('syndrome', SyndromeType.NONE.value)),
            apc_mutation=data.get('apc_mutation', False),
            mutyh_mutations=data.get('mutyh_mutations', 0),
            smad4_mutation=data.get('smad4_mutation', False),
            bmpr1a_mutation=data.get('bmpr1a_mutation', False),
            stk11_mutation=data.get('stk11_mutation', False),
            polygenic_risk_score=data.get('polygenic_risk_score', 0.0),
            family_history_strength=data.get('family_history_strength', 'none'),
            affected_relatives=data.get('affected_relatives', 0),
            early_onset_relatives=data.get('early_onset_relatives', 0)
        )
        
        # Restore MMR mutations
        mmr_data = data.get('mmr_mutations', {})
        for gene_name, status in mmr_data.items():
            try:
                gene = MMRGene(gene_name)
                profile.mmr_mutations[gene] = status
            except ValueError:
                continue  # Skip unknown genes
        
        return profile


class GeneticProfileGenerator:
    """Generator for genetic profiles based on population frequencies."""
    
    def __init__(self):
        """Initialize genetic profile generator."""
        # Population frequencies for genetic variants
        self.frequencies = {
            'fap': 1/10000,  # 1 in 10,000
            'attenuated_fap': 1/20000,  # 1 in 20,000
            'lynch': 1/300,  # 1 in 300
            'map': 1/10000,  # 1 in 10,000
            'family_history_strong': 0.05,  # 5% have strong family history
            'family_history_moderate': 0.15,  # 15% have moderate family history
            'family_history_weak': 0.25  # 25% have weak family history
        }
    
    def generate_profile(self, 
                        family_history: Optional[str] = None,
                        affected_relatives: int = 0) -> GeneticProfile:
        """Generate genetic profile for a patient.
        
        Args:
            family_history: Known family history strength
            affected_relatives: Number of affected relatives
            
        Returns:
            Generated genetic profile
        """
        profile = GeneticProfile()
        
        # Generate high-penetrance mutations
        if np.random.random() < self.frequencies['fap']:
            profile.apc_mutation = True
        
        elif np.random.random() < self.frequencies['lynch']:
            # Randomly assign MMR gene mutation
            genes = list(MMRGene)
            gene_probs = [0.4, 0.35, 0.15, 0.08, 0.02]  # MLH1, MSH2, MSH6, PMS2, EPCAM
            selected_gene = np.random.choice(genes, p=gene_probs)
            profile.mmr_mutations[selected_gene] = True
        
        elif np.random.random() < self.frequencies['map']:
            profile.mutyh_mutations = 2  # Biallelic
        
        # Generate polygenic risk score (normal distribution)
        profile.polygenic_risk_score = np.random.normal(0, 1)
        
        # Generate family history if not provided
        if family_history is None:
            rand = np.random.random()
            if rand < self.frequencies['family_history_strong']:
                profile.family_history_strength = "strong"
                profile.affected_relatives = np.random.poisson(2) + 2
            elif rand < self.frequencies['family_history_strong'] + self.frequencies['family_history_moderate']:
                profile.family_history_strength = "moderate"
                profile.affected_relatives = np.random.poisson(1) + 1
            elif rand < (self.frequencies['family_history_strong'] + 
                        self.frequencies['family_history_moderate'] + 
                        self.frequencies['family_history_weak']):
                profile.family_history_strength = "weak"
                profile.affected_relatives = 1
        else:
            profile.family_history_strength = family_history
            profile.affected_relatives = affected_relatives
        
        return profile
