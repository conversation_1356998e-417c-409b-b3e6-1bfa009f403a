"""
Health economics evaluation module for CMOST simulation.

This module implements comprehensive health economic analysis including
cost-effectiveness analysis, QALY calculations, and budget impact analysis.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import numpy as np
import logging
from datetime import datetime


class CostCategory(Enum):
    """Categories of costs in health economic analysis."""
    SCREENING = "screening"
    DIAGNOSTIC = "diagnostic"
    TREATMENT = "treatment"
    SURVEILLANCE = "surveillance"
    COMPLICATIONS = "complications"
    PRODUCTIVITY_LOSS = "productivity_loss"
    INFORMAL_CARE = "informal_care"


class UtilityState(Enum):
    """Health utility states for QALY calculation."""
    HEALTHY = "healthy"
    POLYP_DETECTED = "polyp_detected"
    CANCER_STAGE_I = "cancer_stage_i"
    CANCER_STAGE_II = "cancer_stage_ii"
    CANCER_STAGE_III = "cancer_stage_iii"
    CANCER_STAGE_IV = "cancer_stage_iv"
    POST_TREATMENT = "post_treatment"
    TERMINAL = "terminal"


@dataclass
class CostData:
    """Cost data for health economic analysis."""
    
    category: CostCategory
    amount: float
    year: int
    patient_id: int
    description: str = ""
    
    # Cost details
    direct_medical: float = 0.0
    direct_non_medical: float = 0.0
    indirect: float = 0.0
    
    # Uncertainty parameters
    standard_error: Optional[float] = None
    confidence_interval: Optional[Tuple[float, float]] = None
    
    def __post_init__(self):
        """Initialize derived cost components."""
        if self.direct_medical == 0.0 and self.direct_non_medical == 0.0 and self.indirect == 0.0:
            # If components not specified, assume all cost is direct medical
            self.direct_medical = self.amount
    
    def get_total_cost(self) -> float:
        """Get total cost across all categories."""
        return self.direct_medical + self.direct_non_medical + self.indirect
    
    def apply_discount(self, discount_rate: float, base_year: int) -> float:
        """Apply discount rate to cost."""
        years_from_base = self.year - base_year
        if years_from_base <= 0:
            return self.amount
        
        discount_factor = 1 / ((1 + discount_rate) ** years_from_base)
        return self.amount * discount_factor


@dataclass
class QualityAdjustedLifeYear:
    """Quality-Adjusted Life Year (QALY) data."""
    
    patient_id: int
    year: int
    utility_state: UtilityState
    utility_value: float
    duration_years: float = 1.0
    
    # Age and gender adjustments
    age: Optional[int] = None
    gender: Optional[str] = None
    
    def calculate_qaly(self, discount_rate: float = 0.03, base_year: int = 0) -> float:
        """Calculate discounted QALY value."""
        qaly = self.utility_value * self.duration_years
        
        years_from_base = self.year - base_year
        if years_from_base <= 0:
            return qaly
        
        discount_factor = 1 / ((1 + discount_rate) ** years_from_base)
        return qaly * discount_factor


@dataclass
class EconomicOutcome:
    """Economic outcome summary for a patient or population."""
    
    patient_id: Optional[int] = None
    
    # Cost outcomes
    total_costs: float = 0.0
    costs_by_category: Dict[CostCategory, float] = field(default_factory=dict)
    discounted_costs: float = 0.0
    
    # Health outcomes
    life_years: float = 0.0
    quality_adjusted_life_years: float = 0.0
    discounted_qalys: float = 0.0
    
    # Cost-effectiveness metrics
    incremental_cost: Optional[float] = None
    incremental_qalys: Optional[float] = None
    icer: Optional[float] = None  # Incremental Cost-Effectiveness Ratio
    
    # Additional metrics
    net_monetary_benefit: Optional[float] = None
    cost_per_life_year: Optional[float] = None


class HealthEconomicsEvaluator:
    """Health economics evaluator for CMOST simulation."""
    
    def __init__(self, 
                 discount_rate: float = 0.03,
                 willingness_to_pay: float = 50000.0,
                 base_year: int = 2023):
        """Initialize health economics evaluator.
        
        Args:
            discount_rate: Annual discount rate for costs and benefits
            willingness_to_pay: Willingness-to-pay threshold per QALY
            base_year: Base year for discounting
        """
        self.discount_rate = discount_rate
        self.willingness_to_pay = willingness_to_pay
        self.base_year = base_year
        self.logger = logging.getLogger("CMOST_HealthEconomics")
        
        # Cost and utility databases
        self.cost_data: List[CostData] = []
        self.qaly_data: List[QualityAdjustedLifeYear] = []
        
        # Standard cost parameters (in USD, 2023 values)
        self.standard_costs = self._initialize_standard_costs()
        
        # Standard utility values
        self.standard_utilities = self._initialize_standard_utilities()
    
    def _initialize_standard_costs(self) -> Dict[str, float]:
        """Initialize standard cost parameters."""
        return {
            # Screening costs
            'colonoscopy': 1000.0,
            'sigmoidoscopy': 300.0,
            'fit_test': 25.0,
            'fobt_test': 15.0,
            'ct_colonography': 500.0,
            'stool_dna_test': 600.0,
            
            # Diagnostic costs
            'biopsy': 200.0,
            'pathology': 150.0,
            'imaging_ct': 800.0,
            'imaging_mri': 1200.0,
            'imaging_pet': 2000.0,
            
            # Treatment costs
            'polypectomy': 500.0,
            'surgery_stage_i': 25000.0,
            'surgery_stage_ii': 30000.0,
            'surgery_stage_iii': 35000.0,
            'surgery_stage_iv': 40000.0,
            'chemotherapy_cycle': 5000.0,
            'radiation_therapy': 15000.0,
            'targeted_therapy': 8000.0,
            'immunotherapy': 12000.0,
            
            # Surveillance costs
            'surveillance_colonoscopy': 1000.0,
            'surveillance_imaging': 800.0,
            'oncology_visit': 300.0,
            
            # Complication costs
            'perforation_treatment': 15000.0,
            'bleeding_treatment': 5000.0,
            'infection_treatment': 3000.0,
            
            # Productivity and informal care
            'productivity_loss_per_day': 200.0,
            'informal_care_per_day': 100.0
        }
    
    def _initialize_standard_utilities(self) -> Dict[UtilityState, float]:
        """Initialize standard utility values."""
        return {
            UtilityState.HEALTHY: 1.0,
            UtilityState.POLYP_DETECTED: 0.95,
            UtilityState.CANCER_STAGE_I: 0.85,
            UtilityState.CANCER_STAGE_II: 0.80,
            UtilityState.CANCER_STAGE_III: 0.70,
            UtilityState.CANCER_STAGE_IV: 0.50,
            UtilityState.POST_TREATMENT: 0.90,
            UtilityState.TERMINAL: 0.20
        }
    
    def add_cost(self, 
                cost_category: CostCategory,
                amount: float,
                year: int,
                patient_id: int,
                description: str = "",
                **kwargs) -> None:
        """Add cost data to the evaluation.
        
        Args:
            cost_category: Category of cost
            amount: Cost amount
            year: Year of cost occurrence
            patient_id: Patient ID
            description: Description of cost
            **kwargs: Additional cost parameters
        """
        cost_data = CostData(
            category=cost_category,
            amount=amount,
            year=year,
            patient_id=patient_id,
            description=description,
            **kwargs
        )
        
        self.cost_data.append(cost_data)
        self.logger.debug(f"Added cost: {cost_category.value} ${amount:.2f} for patient {patient_id}")
    
    def add_qaly(self,
                patient_id: int,
                year: int,
                utility_state: UtilityState,
                duration_years: float = 1.0,
                custom_utility: Optional[float] = None,
                **kwargs) -> None:
        """Add QALY data to the evaluation.
        
        Args:
            patient_id: Patient ID
            year: Year of utility
            utility_state: Health utility state
            duration_years: Duration in years
            custom_utility: Custom utility value (overrides standard)
            **kwargs: Additional QALY parameters
        """
        utility_value = custom_utility or self.standard_utilities[utility_state]
        
        qaly_data = QualityAdjustedLifeYear(
            patient_id=patient_id,
            year=year,
            utility_state=utility_state,
            utility_value=utility_value,
            duration_years=duration_years,
            **kwargs
        )
        
        self.qaly_data.append(qaly_data)
        self.logger.debug(f"Added QALY: {utility_state.value} {utility_value:.3f} for patient {patient_id}")
    
    def calculate_patient_outcomes(self, patient_id: int) -> EconomicOutcome:
        """Calculate economic outcomes for a specific patient.
        
        Args:
            patient_id: Patient ID
            
        Returns:
            Economic outcomes for the patient
        """
        # Filter data for this patient
        patient_costs = [c for c in self.cost_data if c.patient_id == patient_id]
        patient_qalys = [q for q in self.qaly_data if q.patient_id == patient_id]
        
        # Calculate total costs
        total_costs = sum(c.amount for c in patient_costs)
        discounted_costs = sum(c.apply_discount(self.discount_rate, self.base_year) 
                             for c in patient_costs)
        
        # Calculate costs by category
        costs_by_category = {}
        for category in CostCategory:
            category_costs = [c for c in patient_costs if c.category == category]
            costs_by_category[category] = sum(c.amount for c in category_costs)
        
        # Calculate QALYs
        total_qalys = sum(q.utility_value * q.duration_years for q in patient_qalys)
        discounted_qalys = sum(q.calculate_qaly(self.discount_rate, self.base_year) 
                             for q in patient_qalys)
        
        # Calculate life years (assuming each QALY entry represents one year)
        life_years = sum(q.duration_years for q in patient_qalys)
        
        # Calculate cost per life year
        cost_per_life_year = discounted_costs / life_years if life_years > 0 else None
        
        return EconomicOutcome(
            patient_id=patient_id,
            total_costs=total_costs,
            costs_by_category=costs_by_category,
            discounted_costs=discounted_costs,
            life_years=life_years,
            quality_adjusted_life_years=total_qalys,
            discounted_qalys=discounted_qalys,
            cost_per_life_year=cost_per_life_year
        )
    
    def calculate_population_outcomes(self, patient_ids: Optional[List[int]] = None) -> EconomicOutcome:
        """Calculate economic outcomes for a population.
        
        Args:
            patient_ids: List of patient IDs (if None, uses all patients)
            
        Returns:
            Aggregated economic outcomes
        """
        if patient_ids is None:
            # Get all unique patient IDs
            cost_patient_ids = set(c.patient_id for c in self.cost_data)
            qaly_patient_ids = set(q.patient_id for q in self.qaly_data)
            patient_ids = list(cost_patient_ids.union(qaly_patient_ids))
        
        # Calculate outcomes for each patient
        patient_outcomes = [self.calculate_patient_outcomes(pid) for pid in patient_ids]
        
        # Aggregate outcomes
        total_costs = sum(o.total_costs for o in patient_outcomes)
        discounted_costs = sum(o.discounted_costs for o in patient_outcomes)
        total_life_years = sum(o.life_years for o in patient_outcomes)
        total_qalys = sum(o.quality_adjusted_life_years for o in patient_outcomes)
        discounted_qalys = sum(o.discounted_qalys for o in patient_outcomes)
        
        # Aggregate costs by category
        costs_by_category = {}
        for category in CostCategory:
            costs_by_category[category] = sum(
                o.costs_by_category.get(category, 0) for o in patient_outcomes
            )
        
        # Calculate population-level metrics
        cost_per_life_year = discounted_costs / total_life_years if total_life_years > 0 else None
        
        return EconomicOutcome(
            patient_id=None,  # Population-level
            total_costs=total_costs,
            costs_by_category=costs_by_category,
            discounted_costs=discounted_costs,
            life_years=total_life_years,
            quality_adjusted_life_years=total_qalys,
            discounted_qalys=discounted_qalys,
            cost_per_life_year=cost_per_life_year
        )
    
    def compare_strategies(self, 
                          strategy1_outcomes: EconomicOutcome,
                          strategy2_outcomes: EconomicOutcome,
                          strategy1_name: str = "Strategy 1",
                          strategy2_name: str = "Strategy 2") -> Dict[str, Any]:
        """Compare two screening strategies.
        
        Args:
            strategy1_outcomes: Outcomes for strategy 1
            strategy2_outcomes: Outcomes for strategy 2
            strategy1_name: Name of strategy 1
            strategy2_name: Name of strategy 2
            
        Returns:
            Comparison results
        """
        # Calculate incremental outcomes
        incremental_cost = strategy2_outcomes.discounted_costs - strategy1_outcomes.discounted_costs
        incremental_qalys = strategy2_outcomes.discounted_qalys - strategy1_outcomes.discounted_qalys
        
        # Calculate ICER
        icer = None
        if incremental_qalys != 0:
            icer = incremental_cost / incremental_qalys
        
        # Calculate Net Monetary Benefit
        nmb1 = (strategy1_outcomes.discounted_qalys * self.willingness_to_pay - 
                strategy1_outcomes.discounted_costs)
        nmb2 = (strategy2_outcomes.discounted_qalys * self.willingness_to_pay - 
                strategy2_outcomes.discounted_costs)
        incremental_nmb = nmb2 - nmb1
        
        # Determine cost-effectiveness
        cost_effective = None
        if icer is not None:
            if incremental_cost <= 0 and incremental_qalys >= 0:
                cost_effective = f"{strategy2_name} dominates {strategy1_name}"
            elif incremental_cost >= 0 and incremental_qalys <= 0:
                cost_effective = f"{strategy1_name} dominates {strategy2_name}"
            elif icer <= self.willingness_to_pay:
                cost_effective = f"{strategy2_name} is cost-effective"
            else:
                cost_effective = f"{strategy2_name} is not cost-effective"
        
        return {
            "strategy1_name": strategy1_name,
            "strategy2_name": strategy2_name,
            "incremental_cost": incremental_cost,
            "incremental_qalys": incremental_qalys,
            "icer": icer,
            "incremental_nmb": incremental_nmb,
            "cost_effectiveness": cost_effective,
            "willingness_to_pay_threshold": self.willingness_to_pay,
            "strategy1_outcomes": strategy1_outcomes,
            "strategy2_outcomes": strategy2_outcomes
        }
    
    def get_cost_breakdown(self, patient_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """Get detailed cost breakdown.
        
        Args:
            patient_ids: List of patient IDs (if None, uses all patients)
            
        Returns:
            Detailed cost breakdown
        """
        if patient_ids is None:
            relevant_costs = self.cost_data
        else:
            relevant_costs = [c for c in self.cost_data if c.patient_id in patient_ids]
        
        breakdown = {
            "total_costs": sum(c.amount for c in relevant_costs),
            "discounted_costs": sum(c.apply_discount(self.discount_rate, self.base_year) 
                                  for c in relevant_costs),
            "by_category": {},
            "by_year": {},
            "cost_components": {
                "direct_medical": sum(c.direct_medical for c in relevant_costs),
                "direct_non_medical": sum(c.direct_non_medical for c in relevant_costs),
                "indirect": sum(c.indirect for c in relevant_costs)
            }
        }
        
        # Breakdown by category
        for category in CostCategory:
            category_costs = [c for c in relevant_costs if c.category == category]
            breakdown["by_category"][category.value] = {
                "total": sum(c.amount for c in category_costs),
                "count": len(category_costs),
                "average": np.mean([c.amount for c in category_costs]) if category_costs else 0
            }
        
        # Breakdown by year
        years = set(c.year for c in relevant_costs)
        for year in sorted(years):
            year_costs = [c for c in relevant_costs if c.year == year]
            breakdown["by_year"][year] = sum(c.amount for c in year_costs)
        
        return breakdown
