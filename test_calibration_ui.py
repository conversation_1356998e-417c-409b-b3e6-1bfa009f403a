#!/usr/bin/env python3
"""
测试调参UI功能的脚本

该脚本用于验证调参界面的可视化和日志功能是否正常工作。
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_calibration_ui():
    """测试调参UI功能"""
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("CMOST调参UI测试")
        root.geometry("800x600")
        
        # 导入增强主窗口
        from cmost.ui.enhanced_main_window import EnhancedMainWindow
        
        # 创建增强主窗口实例
        app = EnhancedMainWindow(root)
        
        # 设置到调参步骤
        app.show_step(1)  # 调参配置步骤
        
        # 创建测试按钮
        test_frame = ttk.Frame(root)
        test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
        def test_mock_calibration():
            """测试模拟调参"""
            # 模拟调参结果
            mock_results = {
                'method': 'test_method',
                'parameters': {
                    'early_adenoma_rate': 0.025,
                    'advanced_adenoma_rate': 0.008,
                    'cancer_rate': 0.0005
                },
                'error': 0.05,
                'execution_time': 45.2,
                'history': [
                    {'iteration': 0, 'error': 0.5, 'parameters': {'early_adenoma_rate': 0.020, 'advanced_adenoma_rate': 0.010, 'cancer_rate': 0.001}},
                    {'iteration': 1, 'error': 0.3, 'parameters': {'early_adenoma_rate': 0.022, 'advanced_adenoma_rate': 0.009, 'cancer_rate': 0.0008}},
                    {'iteration': 2, 'error': 0.15, 'parameters': {'early_adenoma_rate': 0.024, 'advanced_adenoma_rate': 0.008, 'cancer_rate': 0.0006}},
                    {'iteration': 3, 'error': 0.08, 'parameters': {'early_adenoma_rate': 0.025, 'advanced_adenoma_rate': 0.008, 'cancer_rate': 0.0005}},
                    {'iteration': 4, 'error': 0.05, 'parameters': {'early_adenoma_rate': 0.025, 'advanced_adenoma_rate': 0.008, 'cancer_rate': 0.0005}}
                ],
                'convergence_info': {
                    'converged': True,
                    'iterations': 5,
                    'final_error': 0.05
                }
            }
            
            # 设置调参结果
            app.calibration_results = mock_results
            
            # 显示结果
            app._display_calibration_results()
            
            print("模拟调参结果已设置并显示")
        
        def test_visualization():
            """测试可视化功能"""
            if hasattr(app, 'calibration_results') and app.calibration_results:
                try:
                    app.generate_convergence_plot()
                    print("收敛图生成测试完成")
                except Exception as e:
                    print(f"收敛图生成测试失败: {e}")
                
                try:
                    app.generate_parameter_plot()
                    print("参数对比图生成测试完成")
                except Exception as e:
                    print(f"参数对比图生成测试失败: {e}")
            else:
                print("请先运行模拟调参测试")
        
        def test_logging():
            """测试日志功能"""
            try:
                app.refresh_calibration_log()
                print("日志刷新测试完成")
            except Exception as e:
                print(f"日志刷新测试失败: {e}")
        
        # 添加测试按钮
        ttk.Button(test_frame, text="模拟调参", command=test_mock_calibration).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_frame, text="测试可视化", command=test_visualization).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_frame, text="测试日志", command=test_logging).pack(side=tk.LEFT, padx=5)
        
        # 添加说明标签
        info_label = ttk.Label(test_frame, text="使用下方按钮测试调参功能")
        info_label.pack(side=tk.RIGHT, padx=5)
        
        print("调参UI测试界面已启动")
        print("1. 点击'模拟调参'按钮设置测试数据")
        print("2. 点击'测试可视化'按钮测试图表生成")
        print("3. 点击'测试日志'按钮测试日志功能")
        print("4. 在调参结果区域查看参数结果、可视化对比和调参日志标签页")
        
        # 运行主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保CMOST模块已正确安装")
        return False
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("开始测试CMOST调参UI功能...")
    
    # 检查依赖
    try:
        import matplotlib
        import numpy
        print("✓ 可视化依赖检查通过")
    except ImportError as e:
        print(f"✗ 缺少必要依赖: {e}")
        return False
    
    # 运行测试
    success = test_calibration_ui()
    
    if success:
        print("✓ 调参UI测试完成")
    else:
        print("✗ 调参UI测试失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
