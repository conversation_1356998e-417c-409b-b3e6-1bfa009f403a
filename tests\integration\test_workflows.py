"""
Integration tests for CMOST workflows.
"""

import pytest
import tempfile
import os
from pathlib import Path

from cmost.core.simulation import Simulation
from cmost.core.population import PopulationGenerator
from cmost.config.settings import Settings
from cmost.utils.statistics import calculate_statistics
from cmost.utils.file_io import save_results, load_results


class TestSimulationWorkflow:
    """Test complete simulation workflows."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.settings = Settings()
        self.settings.set('Number_patients', 50)  # Small for testing
        self.settings.set('ModelParameters.male_proportion', 0.5)
        
    def test_complete_simulation_workflow(self):
        """Test complete simulation from start to finish."""
        # Initialize simulation
        sim = Simulation(self.settings)
        
        # Generate population
        sim.initialize_population()
        assert len(sim.patients) == 50
        
        # Run simulation
        results = sim.run(years=5)
        
        # Verify results structure
        assert isinstance(results, dict)
        assert len(sim.patients) == 50
        assert sim.current_year == 5
        
        # Check that some patients have aged
        ages = [p.age for p in sim.patients]
        assert all(age >= 18 + 5 for age in ages)  # All should have aged at least 5 years
        
    def test_population_generation_workflow(self):
        """Test population generation workflow."""
        pop_gen = PopulationGenerator(self.settings)
        
        # Generate population
        patients = pop_gen.generate_population(100)
        
        # Verify population characteristics
        assert len(patients) == 100
        
        # Check gender distribution
        males = sum(1 for p in patients if p.gender == 'M')
        females = sum(1 for p in patients if p.gender == 'F')
        assert males + females == 100
        
        # Check age distribution
        ages = [p.age for p in patients]
        assert all(0 <= age <= 100 for age in ages)
        
        # Check risk factors
        for patient in patients:
            assert hasattr(patient, 'risk_factors')
            assert isinstance(patient.risk_factors, dict)
            assert hasattr(patient, 'individual_risk')
            
    def test_screening_workflow(self):
        """Test screening workflow."""
        # Set up screening
        self.settings.set('Screening.EnableScreening', True)
        self.settings.set('Screening.ScreeningAges', [50, 60, 70])
        
        sim = Simulation(self.settings)
        sim.initialize_population()
        
        # Add some polyps to patients for testing
        for i, patient in enumerate(sim.patients[:10]):
            if patient.age >= 50:  # Only add to screening-eligible patients
                patient.add_polyp(location=1, size=0.5)
        
        # Run simulation with screening
        results = sim.run(years=10)
        
        # Check that some screening occurred
        screened_patients = [p for p in sim.patients if len(p.screening_history) > 0]
        
        # Should have some screened patients (exact number depends on random factors)
        assert len(screened_patients) >= 0  # At least some possibility of screening
        
    def test_statistics_calculation_workflow(self):
        """Test statistics calculation workflow."""
        sim = Simulation(self.settings)
        sim.initialize_population()
        
        # Add some test conditions
        sim.patients[0].death_cause = 'cancer'
        sim.patients[1].death_cause = 'natural'
        sim.patients[2].add_polyp(location=1, size=1.5)  # Advanced polyp
        
        # Calculate statistics
        stats = calculate_statistics(sim.patients, self.settings)
        
        # Verify statistics structure
        assert isinstance(stats, dict)
        assert 'total_patients' in stats
        assert stats['total_patients'] == 50
        
    def test_file_io_workflow(self):
        """Test file I/O workflow."""
        sim = Simulation(self.settings)
        sim.initialize_population()
        results = sim.run(years=2)
        
        # Test saving and loading results
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save results
            result_file = os.path.join(temp_dir, 'test_results.json')
            save_results(results, result_file)
            
            # Verify file exists
            assert os.path.exists(result_file)
            
            # Load results
            loaded_results = load_results(result_file)
            
            # Verify loaded results match original
            assert isinstance(loaded_results, dict)
            # Note: Exact comparison might be difficult due to serialization
            
    def test_calibration_workflow(self):
        """Test calibration workflow (simplified)."""
        from cmost.calibration.benchmark import BenchmarkManager
        
        # Create benchmark manager
        bm = BenchmarkManager()
        
        # Test benchmark creation
        template = bm.create_benchmark_template()
        assert isinstance(template, dict)
        
        # Test benchmark loading (with mock data)
        mock_benchmarks = {
            'cancer_incidence': {'M': {50: 10.0, 60: 20.0}, 'F': {50: 8.0, 60: 16.0}},
            'cancer_mortality': {'M': {50: 2.0, 60: 5.0}, 'F': {50: 1.5, 60: 4.0}}
        }
        
        bm.benchmarks = mock_benchmarks
        
        # Test benchmark access
        male_incidence = bm.get_cancer_incidence('M', 55)
        assert isinstance(male_incidence, float)
        assert male_incidence > 0
        
    def test_multi_year_simulation_consistency(self):
        """Test that multi-year simulations are consistent."""
        sim = Simulation(self.settings)
        sim.initialize_population()
        
        # Record initial state
        initial_ages = [p.age for p in sim.patients]
        initial_patient_count = len(sim.patients)
        
        # Run simulation
        results = sim.run(years=10)
        
        # Check consistency
        final_ages = [p.age for p in sim.patients if p.is_alive(sim.current_year)]
        
        # Patients should have aged (those still alive)
        for i, patient in enumerate(sim.patients):
            if patient.is_alive(sim.current_year):
                expected_age = initial_ages[i] + 10
                assert patient.age == expected_age
                
        # Some patients might have died, but total should not increase
        assert len(sim.patients) == initial_patient_count
        
    def test_risk_factor_impact(self):
        """Test that risk factors impact simulation outcomes."""
        # Create two simulations with different risk profiles
        settings_low_risk = Settings()
        settings_low_risk.set('Number_patients', 100)
        
        settings_high_risk = Settings()
        settings_high_risk.set('Number_patients', 100)
        
        # Run simulations (simplified test)
        sim_low = Simulation(settings_low_risk)
        sim_low.initialize_population()
        
        sim_high = Simulation(settings_high_risk)
        sim_high.initialize_population()
        
        # Manually adjust risk factors for testing
        for patient in sim_high.patients:
            patient.risk_factors['smoking'] = 2.0  # High risk
            patient.individual_risk = patient._calculate_individual_risk()
            
        # Both simulations should complete without error
        results_low = sim_low.run(years=5)
        results_high = sim_high.run(years=5)
        
        assert isinstance(results_low, dict)
        assert isinstance(results_high, dict)


if __name__ == '__main__':
    pytest.main([__file__])
