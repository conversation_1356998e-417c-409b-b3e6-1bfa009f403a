"""
Unit tests for CMOST simulation engine.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch

from cmost.core.simulation import Simulation
from cmost.config.settings import Settings
from cmost.models.patient import Patient


class TestSimulation:
    """Test cases for Simulation class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.settings = Settings()
        self.settings.set('Number_patients', 100)
        self.settings.set('ModelParameters.male_proportion', 0.5)
        
    def test_simulation_initialization(self):
        """Test simulation initialization."""
        sim = Simulation(self.settings)
        
        assert sim.settings == self.settings
        assert sim.patients == []
        assert sim.results == {}
        assert sim.current_year == 0
        
    def test_population_initialization(self):
        """Test population initialization."""
        sim = Simulation(self.settings)
        sim.initialize_population()
        
        # Check population size
        assert len(sim.patients) == 100
        
        # Check gender distribution (should be approximately 50/50)
        males = sum(1 for p in sim.patients if p.gender == 'M')
        females = sum(1 for p in sim.patients if p.gender == 'F')
        
        # Allow some variance due to randomness
        assert 30 <= males <= 70
        assert 30 <= females <= 70
        assert males + females == 100
        
    def test_age_distribution_sampling(self):
        """Test age distribution sampling."""
        sim = Simulation(self.settings)
        
        # Test multiple age samples
        ages = [sim._sample_age_distribution('M') for _ in range(100)]
        
        # All ages should be within reasonable range
        assert all(18 <= age <= 100 for age in ages)
        
        # Should have some variance
        assert len(set(ages)) > 10  # At least 10 different ages
        
    def test_risk_factor_assignment(self):
        """Test risk factor assignment."""
        sim = Simulation(self.settings)
        
        # Mock risk factor distributions
        sim.settings.risk_factor_distributions = {
            'smoking': Mock(sample=Mock(return_value=1.5)),
            'family_history': Mock(sample=Mock(return_value=2.0))
        }
        
        risk_factors = sim._assign_risk_factors()
        
        assert 'smoking' in risk_factors
        assert 'family_history' in risk_factors
        assert risk_factors['smoking'] == 1.5
        assert risk_factors['family_history'] == 2.0
        
    def test_simulation_run(self):
        """Test simulation run."""
        sim = Simulation(self.settings)
        
        # Mock progression model
        mock_progression = Mock()
        sim.progression_model = mock_progression
        
        # Run simulation for 1 year
        results = sim.run(years=1)
        
        assert sim.current_year == 1
        assert isinstance(results, dict)
        
    def test_screening_year_detection(self):
        """Test screening year detection."""
        sim = Simulation(self.settings)
        
        # Set up screening settings
        sim.settings.screening = {
            'enabled': True,
            'start_year': 5,
            'interval': 2
        }
        
        # Test different years
        sim.current_year = 4
        assert not sim._is_screening_year()
        
        sim.current_year = 5
        assert sim._is_screening_year()
        
        sim.current_year = 6
        assert not sim._is_screening_year()
        
        sim.current_year = 7
        assert sim._is_screening_year()
        
    def test_natural_death_check(self):
        """Test natural death checking."""
        sim = Simulation(self.settings)
        
        # Create test patient
        patient = Patient(id=1, age=80, gender='M')
        patient.natural_death_year = 2025
        
        # Test before death year
        sim.current_year = 2024
        assert not sim._check_natural_death(patient)
        
        # Test at death year
        sim.current_year = 2025
        assert sim._check_natural_death(patient)
        
        # Test after death year
        sim.current_year = 2026
        assert sim._check_natural_death(patient)
        
    def test_polyp_generation(self):
        """Test new polyp generation."""
        sim = Simulation(self.settings)
        
        # Create test patient
        patient = Patient(id=1, age=60, gender='M', risk_factors={'smoking': 1.5})
        
        # Mock polyp generation
        with patch.object(sim, '_generate_new_polyps') as mock_gen:
            sim._generate_new_polyps(patient)
            mock_gen.assert_called_once_with(patient)
            
    def test_summary_statistics(self):
        """Test summary statistics generation."""
        sim = Simulation(self.settings)
        
        # Create some test patients
        sim.patients = [
            Patient(id=1, age=50, gender='M'),
            Patient(id=2, age=60, gender='F'),
            Patient(id=3, age=70, gender='M')
        ]
        
        # Add some test data
        sim.patients[0].death_cause = 'cancer'
        sim.patients[1].death_cause = 'natural'
        
        summary = sim.get_summary_statistics()
        
        assert isinstance(summary, dict)
        assert 'total_patients' in summary
        
    @patch('numpy.random.random')
    def test_screening_participation(self, mock_random):
        """Test screening participation."""
        sim = Simulation(self.settings)
        
        # Set up screening
        sim.settings.screening = {
            'test_type': 'colonoscopy',
            'participation_rate': 0.6,
            'age_range': (50, 75)
        }
        
        # Create test patients
        sim.patients = [
            Patient(id=1, age=55, gender='M'),  # Eligible
            Patient(id=2, age=45, gender='F'),  # Too young
            Patient(id=3, age=80, gender='M'),  # Too old
        ]
        
        # Mock random to control participation
        mock_random.side_effect = [0.5, 0.7, 0.3]  # First participates, others don't
        
        with patch.object(sim, '_perform_colonoscopy') as mock_colonoscopy:
            sim._perform_screening()
            
            # Only first patient should get screening
            assert mock_colonoscopy.call_count == 1
            mock_colonoscopy.assert_called_with(sim.patients[0])


if __name__ == '__main__':
    pytest.main([__file__])
