"""
Screening strategy manager for CMOST simulation.

This module implements multi-tool screening strategies and combinations
for colorectal cancer screening.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import numpy as np
import logging

from ..models.patient import Patient


class ScreeningTest(Enum):
    """Available screening tests."""
    COLONOSCOPY = "colonoscopy"
    SIGMOIDOSCOPY = "sigmoidoscopy"
    FIT = "fit"
    FOBT = "fobt"
    CT_COLONOGRAPHY = "ct_colonography"
    STOOL_DNA = "stool_dna"
    CAPSULE_ENDOSCOPY = "capsule_endoscopy"


class StrategyType(Enum):
    """Types of screening strategies."""
    SINGLE_TEST = "single_test"
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    RISK_STRATIFIED = "risk_stratified"
    ADAPTIVE = "adaptive"


@dataclass
class TestCharacteristics:
    """Characteristics of a screening test."""
    
    name: str
    sensitivity: Dict[str, float] = field(default_factory=dict)
    specificity: float = 0.90
    cost: float = 100.0
    complications_rate: float = 0.001
    patient_burden: float = 1.0  # Relative burden (1.0 = reference)
    
    # Test-specific parameters
    requires_bowel_prep: bool = False
    is_invasive: bool = False
    detection_range: str = "whole_colon"  # "whole_colon", "distal", "proximal"
    
    def __post_init__(self):
        """Initialize default sensitivity values."""
        if not self.sensitivity:
            # Default sensitivity values
            if self.name == "colonoscopy":
                self.sensitivity = {
                    'small_adenoma': 0.85,
                    'advanced_adenoma': 0.95,
                    'serrated_lesion': 0.75,
                    'cancer': 0.95
                }
                self.specificity = 0.90
                self.cost = 1000.0
                self.complications_rate = 0.002
                self.requires_bowel_prep = True
                self.is_invasive = True
                
            elif self.name == "fit":
                self.sensitivity = {
                    'small_adenoma': 0.10,
                    'advanced_adenoma': 0.40,
                    'serrated_lesion': 0.05,
                    'cancer': 0.75
                }
                self.specificity = 0.95
                self.cost = 25.0
                self.complications_rate = 0.0
                
            elif self.name == "sigmoidoscopy":
                self.sensitivity = {
                    'small_adenoma': 0.80,
                    'advanced_adenoma': 0.90,
                    'serrated_lesion': 0.70,
                    'cancer': 0.90
                }
                self.specificity = 0.88
                self.cost = 300.0
                self.complications_rate = 0.001
                self.is_invasive = True
                self.detection_range = "distal"


@dataclass
class ScreeningStrategy:
    """Definition of a screening strategy."""
    
    name: str
    strategy_type: StrategyType
    tests: List[ScreeningTest] = field(default_factory=list)
    
    # Strategy parameters
    primary_test: Optional[ScreeningTest] = None
    secondary_tests: List[ScreeningTest] = field(default_factory=list)
    
    # Scheduling parameters
    intervals: Dict[ScreeningTest, int] = field(default_factory=dict)
    age_ranges: Dict[ScreeningTest, Tuple[int, int]] = field(default_factory=dict)
    
    # Decision rules
    positive_threshold: float = 0.5
    follow_up_rules: Dict[str, Any] = field(default_factory=dict)
    
    # Risk stratification (if applicable)
    risk_thresholds: Dict[str, float] = field(default_factory=dict)
    risk_based_tests: Dict[str, ScreeningTest] = field(default_factory=dict)


class ScreeningStrategyManager:
    """Manager for screening strategies and test combinations."""
    
    def __init__(self):
        """Initialize screening strategy manager."""
        self.logger = logging.getLogger("CMOST_ScreeningStrategy")
        
        # Initialize test characteristics
        self.test_characteristics = {
            ScreeningTest.COLONOSCOPY: TestCharacteristics("colonoscopy"),
            ScreeningTest.FIT: TestCharacteristics("fit"),
            ScreeningTest.SIGMOIDOSCOPY: TestCharacteristics("sigmoidoscopy"),
            ScreeningTest.FOBT: TestCharacteristics("fobt"),
            ScreeningTest.CT_COLONOGRAPHY: TestCharacteristics("ct_colonography"),
            ScreeningTest.STOOL_DNA: TestCharacteristics("stool_dna")
        }
        
        # Initialize predefined strategies
        self.strategies = self._initialize_predefined_strategies()
    
    def _initialize_predefined_strategies(self) -> Dict[str, ScreeningStrategy]:
        """Initialize predefined screening strategies."""
        strategies = {}
        
        # Single test strategies
        strategies["colonoscopy_only"] = ScreeningStrategy(
            name="Colonoscopy Only",
            strategy_type=StrategyType.SINGLE_TEST,
            primary_test=ScreeningTest.COLONOSCOPY,
            intervals={ScreeningTest.COLONOSCOPY: 10},
            age_ranges={ScreeningTest.COLONOSCOPY: (50, 75)}
        )
        
        strategies["fit_only"] = ScreeningStrategy(
            name="FIT Only",
            strategy_type=StrategyType.SINGLE_TEST,
            primary_test=ScreeningTest.FIT,
            intervals={ScreeningTest.FIT: 1},
            age_ranges={ScreeningTest.FIT: (50, 75)}
        )
        
        # Sequential strategies
        strategies["fit_colonoscopy"] = ScreeningStrategy(
            name="FIT with Colonoscopy Follow-up",
            strategy_type=StrategyType.SEQUENTIAL,
            primary_test=ScreeningTest.FIT,
            secondary_tests=[ScreeningTest.COLONOSCOPY],
            intervals={ScreeningTest.FIT: 1, ScreeningTest.COLONOSCOPY: 10},
            age_ranges={ScreeningTest.FIT: (50, 75), ScreeningTest.COLONOSCOPY: (50, 75)},
            follow_up_rules={
                "fit_positive": "colonoscopy",
                "colonoscopy_interval": 10
            }
        )
        
        strategies["sigmoidoscopy_fit"] = ScreeningStrategy(
            name="Sigmoidoscopy + FIT Combination",
            strategy_type=StrategyType.PARALLEL,
            tests=[ScreeningTest.SIGMOIDOSCOPY, ScreeningTest.FIT],
            intervals={ScreeningTest.SIGMOIDOSCOPY: 5, ScreeningTest.FIT: 1},
            age_ranges={ScreeningTest.SIGMOIDOSCOPY: (50, 75), ScreeningTest.FIT: (50, 75)}
        )
        
        # Risk-stratified strategy
        strategies["risk_stratified"] = ScreeningStrategy(
            name="Risk-Stratified Screening",
            strategy_type=StrategyType.RISK_STRATIFIED,
            risk_thresholds={"low": 1.0, "moderate": 2.0, "high": 5.0},
            risk_based_tests={
                "low": ScreeningTest.FIT,
                "moderate": ScreeningTest.SIGMOIDOSCOPY,
                "high": ScreeningTest.COLONOSCOPY
            },
            intervals={
                ScreeningTest.FIT: 2,
                ScreeningTest.SIGMOIDOSCOPY: 5,
                ScreeningTest.COLONOSCOPY: 5
            }
        )
        
        return strategies
    
    def get_strategy(self, strategy_name: str) -> Optional[ScreeningStrategy]:
        """Get a screening strategy by name.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            ScreeningStrategy or None if not found
        """
        return self.strategies.get(strategy_name)
    
    def add_custom_strategy(self, strategy: ScreeningStrategy) -> None:
        """Add a custom screening strategy.
        
        Args:
            strategy: Custom screening strategy
        """
        self.strategies[strategy.name.lower().replace(" ", "_")] = strategy
        self.logger.info(f"Added custom strategy: {strategy.name}")
    
    def apply_strategy(self, 
                      strategy_name: str, 
                      patient: Patient, 
                      current_year: int,
                      last_screening: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Apply a screening strategy to a patient.
        
        Args:
            strategy_name: Name of the screening strategy
            patient: Patient to screen
            current_year: Current simulation year
            last_screening: Information about last screening
            
        Returns:
            Dictionary with screening results
        """
        strategy = self.get_strategy(strategy_name)
        if not strategy:
            raise ValueError(f"Unknown screening strategy: {strategy_name}")
        
        self.logger.debug(f"Applying strategy {strategy.name} to patient {patient.id}")
        
        if strategy.strategy_type == StrategyType.SINGLE_TEST:
            return self._apply_single_test_strategy(strategy, patient, current_year)
        
        elif strategy.strategy_type == StrategyType.SEQUENTIAL:
            return self._apply_sequential_strategy(strategy, patient, current_year, last_screening)
        
        elif strategy.strategy_type == StrategyType.PARALLEL:
            return self._apply_parallel_strategy(strategy, patient, current_year)
        
        elif strategy.strategy_type == StrategyType.RISK_STRATIFIED:
            return self._apply_risk_stratified_strategy(strategy, patient, current_year)
        
        else:
            raise ValueError(f"Unsupported strategy type: {strategy.strategy_type}")
    
    def _apply_single_test_strategy(self, 
                                   strategy: ScreeningStrategy, 
                                   patient: Patient, 
                                   current_year: int) -> Dict[str, Any]:
        """Apply single test screening strategy.
        
        Args:
            strategy: Screening strategy
            patient: Patient to screen
            current_year: Current simulation year
            
        Returns:
            Screening results
        """
        test = strategy.primary_test
        age_range = strategy.age_ranges.get(test, (50, 75))
        
        # Check if patient is eligible
        if not (age_range[0] <= patient.age <= age_range[1]):
            return {"eligible": False, "reason": "age_out_of_range"}
        
        # Perform the test
        return self._perform_test(test, patient, current_year)
    
    def _apply_sequential_strategy(self, 
                                  strategy: ScreeningStrategy, 
                                  patient: Patient, 
                                  current_year: int,
                                  last_screening: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Apply sequential screening strategy.
        
        Args:
            strategy: Screening strategy
            patient: Patient to screen
            current_year: Current simulation year
            last_screening: Last screening information
            
        Returns:
            Screening results
        """
        # Start with primary test
        primary_result = self._apply_single_test_strategy(strategy, patient, current_year)
        
        if not primary_result.get("eligible", True):
            return primary_result
        
        results = {"primary_test": primary_result}
        
        # Check if follow-up is needed
        if (primary_result.get("result") == "positive" and 
            strategy.secondary_tests and 
            "fit_positive" in strategy.follow_up_rules):
            
            # Perform follow-up test
            follow_up_test = strategy.secondary_tests[0]
            follow_up_result = self._perform_test(follow_up_test, patient, current_year)
            results["follow_up_test"] = follow_up_result
        
        return results
    
    def _apply_parallel_strategy(self, 
                                strategy: ScreeningStrategy, 
                                patient: Patient, 
                                current_year: int) -> Dict[str, Any]:
        """Apply parallel screening strategy.
        
        Args:
            strategy: Screening strategy
            patient: Patient to screen
            current_year: Current simulation year
            
        Returns:
            Screening results
        """
        results = {"tests": {}}
        
        for test in strategy.tests:
            age_range = strategy.age_ranges.get(test, (50, 75))
            
            if age_range[0] <= patient.age <= age_range[1]:
                test_result = self._perform_test(test, patient, current_year)
                results["tests"][test.value] = test_result
        
        # Combine results (positive if any test is positive)
        any_positive = any(
            result.get("result") == "positive" 
            for result in results["tests"].values()
        )
        results["combined_result"] = "positive" if any_positive else "negative"
        
        return results
    
    def _apply_risk_stratified_strategy(self, 
                                       strategy: ScreeningStrategy, 
                                       patient: Patient, 
                                       current_year: int) -> Dict[str, Any]:
        """Apply risk-stratified screening strategy.
        
        Args:
            strategy: Screening strategy
            patient: Patient to screen
            current_year: Current simulation year
            
        Returns:
            Screening results
        """
        # Determine patient risk level
        risk_score = patient.individual_risk
        
        risk_level = "low"
        for level, threshold in strategy.risk_thresholds.items():
            if risk_score >= threshold:
                risk_level = level
        
        # Select appropriate test
        selected_test = strategy.risk_based_tests.get(risk_level, ScreeningTest.FIT)
        
        # Perform the test
        result = self._perform_test(selected_test, patient, current_year)
        result["risk_level"] = risk_level
        result["risk_score"] = risk_score
        
        return result
    
    def _perform_test(self, 
                     test: ScreeningTest, 
                     patient: Patient, 
                     current_year: int) -> Dict[str, Any]:
        """Perform a specific screening test.
        
        Args:
            test: Screening test to perform
            patient: Patient to test
            current_year: Current simulation year
            
        Returns:
            Test results
        """
        characteristics = self.test_characteristics[test]
        
        result = {
            "test": test.value,
            "year": current_year,
            "patient_id": patient.id,
            "result": "negative",
            "findings": {},
            "cost": characteristics.cost,
            "complications": False
        }
        
        # Check for complications
        if np.random.random() < characteristics.complications_rate:
            result["complications"] = True
        
        # Simulate test performance based on patient's lesions
        detected_lesions = self._detect_lesions(patient, characteristics)
        
        if detected_lesions["total_detected"] > 0:
            result["result"] = "positive"
            result["findings"] = detected_lesions
        
        return result
    
    def _detect_lesions(self, patient: Patient, characteristics: TestCharacteristics) -> Dict[str, Any]:
        """Simulate lesion detection based on test characteristics.
        
        Args:
            patient: Patient being tested
            characteristics: Test characteristics
            
        Returns:
            Detection results
        """
        detected = {
            "polyps": [],
            "serrated_lesions": [],
            "cancers": [],
            "total_detected": 0
        }
        
        # Check polyps
        for polyp in patient.polyps:
            if self._is_lesion_detectable(polyp, characteristics):
                lesion_type = self._classify_polyp_for_detection(polyp)
                sensitivity = characteristics.sensitivity.get(lesion_type, 0.5)
                
                if np.random.random() < sensitivity:
                    detected["polyps"].append(polyp.id)
                    detected["total_detected"] += 1
        
        # Check serrated lesions
        for lesion in patient.serrated_lesions:
            if self._is_lesion_detectable(lesion, characteristics):
                sensitivity = characteristics.sensitivity.get("serrated_lesion", 0.3)
                
                if np.random.random() < sensitivity:
                    detected["serrated_lesions"].append(lesion.id)
                    detected["total_detected"] += 1
        
        # Check cancers
        for cancer in patient.cancers:
            if self._is_lesion_detectable(cancer, characteristics):
                sensitivity = characteristics.sensitivity.get("cancer", 0.8)
                
                if np.random.random() < sensitivity:
                    detected["cancers"].append(cancer.id)
                    detected["total_detected"] += 1
        
        return detected
    
    def _is_lesion_detectable(self, lesion, characteristics: TestCharacteristics) -> bool:
        """Check if a lesion is detectable by the test.
        
        Args:
            lesion: Lesion to check
            characteristics: Test characteristics
            
        Returns:
            True if lesion is in detectable range
        """
        if characteristics.detection_range == "whole_colon":
            return True
        elif characteristics.detection_range == "distal":
            return lesion.location <= 3  # Rectum, sigmoid, descending
        elif characteristics.detection_range == "proximal":
            return lesion.location >= 4  # Transverse, ascending, cecum
        
        return True
    
    def _classify_polyp_for_detection(self, polyp) -> str:
        """Classify polyp for detection sensitivity.
        
        Args:
            polyp: Polyp to classify
            
        Returns:
            Classification string
        """
        if polyp.size >= 1.0 or polyp.stage >= 4:
            return "advanced_adenoma"
        else:
            return "small_adenoma"
