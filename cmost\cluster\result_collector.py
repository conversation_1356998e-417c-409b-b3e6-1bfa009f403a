"""
Result collection module for CMOST cluster simulations.

This module provides functionality for collecting, aggregating, and processing
results from multiple simulation jobs running on computing clusters.
"""

import os
import logging
import json
import glob
import datetime
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path

from ..config.settings import settings
from ..utils.file_io import load_simulation_results, save_simulation_results


class ResultCollector:
    """
    Collects and processes results from cluster simulation jobs.
    
    This class provides methods for gathering results from multiple simulation
    jobs, aggregating them, and performing analysis on the combined data.
    """
    
    def __init__(self, base_dir: Optional[str] = None):
        """
        Initialize the result collector.
        
        Args:
            base_dir: Base directory for result files (defaults to settings path)
        """
        self.logger = logging.getLogger("CMOST_ResultCollector")
        
        # Set up logging if not already configured
        if not self.logger.handlers:
            self.logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        # Set base directory
        if base_dir is None:
            self.base_dir = settings.get("ResultsPath", os.path.join(os.getcwd(), "Results"))
        else:
            self.base_dir = base_dir
            
        # Create results directory if it doesn't exist
        self.results_dir = os.path.join(self.base_dir, "Results")
        os.makedirs(self.results_dir, exist_ok=True)
        
        self.logger.info(f"Result collector initialized with base directory: {self.base_dir}")
    
    def collect_results(self, pattern: str = "*_Results.json") -> List[Dict[str, Any]]:
        """
        Collect results from all matching result files.
        
        Args:
            pattern: Glob pattern to match result files
            
        Returns:
            List of result dictionaries
        """
        results = []
        result_files = glob.glob(os.path.join(self.results_dir, pattern))
        
        self.logger.info(f"Found {len(result_files)} result files matching pattern: {pattern}")
        
        for file_path in result_files:
            try:
                result = load_simulation_results(file_path)
                if result:
                    # Add file information
                    result["_file_path"] = file_path
                    result["_file_name"] = os.path.basename(file_path)
                    result["_collection_time"] = datetime.datetime.now().isoformat()
                    results.append(result)
            except Exception as e:
                self.logger.error(f"Error loading results from {file_path}: {str(e)}")
        
        self.logger.info(f"Successfully loaded {len(results)} result sets")
        return results
    
    def collect_parameter_sweep_results(self, prefix: str) -> pd.DataFrame:
        """
        Collect and organize results from a parameter sweep.
        
        Args:
            prefix: Prefix used for parameter sweep job names
            
        Returns:
            DataFrame with parameter sweep results
        """
        # Collect all results with the given prefix
        pattern = f"{prefix}*_Results.json"
        results = self.collect_results(pattern)
        
        if not results:
            self.logger.warning(f"No results found for parameter sweep with prefix: {prefix}")
            return pd.DataFrame()
        
        # Extract parameters and key metrics
        data = []
        for result in results:
            # Extract job name and parameters
            job_name = result.get("_file_name", "").replace("_Results.json", "")
            
            # Create a row with basic information
            row = {
                "job_name": job_name,
                "num_patients": result.get("NumberPatients", 0)
            }
            
            # Add parameters (assuming they're stored in the settings section)
            settings = result.get("Settings", {})
            for key, value in settings.items():
                if key not in row:
                    row[f"param_{key}"] = value
            
            # Add key metrics
            metrics = result.get("Variable", [])
            if isinstance(metrics, list) and len(metrics) > 0:
                for i, value in enumerate(metrics):
                    row[f"metric_{i}"] = value
            
            # Add summary variables
            summary = result.get("Summary", {})
            for key, value in summary.items():
                if key not in row:
                    row[f"summary_{key}"] = value
            
            data.append(row)
        
        # Create DataFrame
        df = pd.DataFrame(data)
        self.logger.info(f"Created parameter sweep results DataFrame with {len(df)} rows and {len(df.columns)} columns")
        
        return df
    
    def aggregate_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Aggregate multiple result sets into a single summary.
        
        Args:
            results: List of result dictionaries
            
        Returns:
            Dictionary with aggregated results
        """
        if not results:
            self.logger.warning("No results to aggregate")
            return {}
        
        # Initialize aggregated results
        aggregated = {
            "num_simulations": len(results),
            "aggregation_time": datetime.datetime.now().isoformat(),
            "metrics": {},
            "summary": {}
        }
        
        # Extract and aggregate metrics
        metrics_data = {}
        for result in results:
            metrics = result.get("Variable", [])
            if isinstance(metrics, list):
                for i, value in enumerate(metrics):
                    if i not in metrics_data:
                        metrics_data[i] = []
                    if isinstance(value, (int, float)):
                        metrics_data[i].append(value)
        
        # Calculate statistics for metrics
        for metric_id, values in metrics_data.items():
            if values:
                aggregated["metrics"][f"metric_{metric_id}"] = {
                    "mean": np.mean(values),
                    "median": np.median(values),
                    "std": np.std(values),
                    "min": np.min(values),
                    "max": np.max(values),
                    "count": len(values)
                }
        
        # Extract and aggregate summary variables
        summary_data = {}
        for result in results:
            summary = result.get("Summary", {})
            for key, value in summary.items():
                if key not in summary_data:
                    summary_data[key] = []
                if isinstance(value, (int, float)):
                    summary_data[key].append(value)
        
        # Calculate statistics for summary variables
        for var_name, values in summary_data.items():
            if values:
                aggregated["summary"][var_name] = {
                    "mean": np.mean(values),
                    "median": np.median(values),
                    "std": np.std(values),
                    "min": np.min(values),
                    "max": np.max(values),
                    "count": len(values)
                }
        
        self.logger.info(f"Aggregated {len(results)} result sets")
        return aggregated
    
    def save_aggregated_results(self, aggregated_results: Dict[str, Any], file_name: str) -> str:
        """
        Save aggregated results to a file.
        
        Args:
            aggregated_results: Dictionary with aggregated results
            file_name: Name for the output file
            
        Returns:
            Path to the saved file
        """
        # Ensure file has .json extension
        if not file_name.endswith(".json"):
            file_name += ".json"
        
        file_path = os.path.join(self.results_dir, file_name)
        
        try:
            with open(file_path, 'w') as f:
                json.dump(aggregated_results, f, indent=2)
            
            self.logger.info(f"Saved aggregated results to {file_path}")
            return file_path
        except Exception as e:
            self.logger.error(f"Error saving aggregated results: {str(e)}")
            return ""
    
    def export_to_csv(self, results: List[Dict[str, Any]], file_name: str) -> str:
        """
        Export results to CSV format.
        
        Args:
            results: List of result dictionaries
            file_name: Name for the output file
            
        Returns:
            Path to the saved file
        """
        # Ensure file has .csv extension
        if not file_name.endswith(".csv"):
            file_name += ".csv"
        
        file_path = os.path.join(self.results_dir, file_name)
        
        try:
            # Convert results to DataFrame
            data = []
            for result in results:
                # Extract basic information
                row = {
                    "file_name": result.get("_file_name", ""),
                    "num_patients": result.get("NumberPatients", 0)
                }
                
                # Add metrics
                metrics = result.get("Variable", [])
                if isinstance(metrics, list):
                    for i, value in enumerate(metrics):
                        row[f"metric_{i}"] = value
                
                # Add summary variables
                summary = result.get("Summary", {})
                for key, value in summary.items():
                    if key not in row:
                        row[key] = value
                
                data.append(row)
            
            # Create and save DataFrame
            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False)
            
            self.logger.info(f"Exported {len(results)} result sets to CSV: {file_path}")
            return file_path
        except Exception as e:
            self.logger.error(f"Error exporting results to CSV: {str(e)}")
            return ""
    
    def compare_results(self, results: List[Dict[str, Any]], 
                       reference_result: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Compare multiple result sets against a reference or against each other.
        
        Args:
            results: List of result dictionaries to compare
            reference_result: Optional reference result for comparison
            
        Returns:
            Dictionary with comparison results
        """
        if not results:
            self.logger.warning("No results to compare")
            return {}
        
        # If no reference is provided, use the first result as reference
        if reference_result is None and results:
            reference_result = results[0]
            results = results[1:]
        
        if not reference_result:
            self.logger.warning("No reference result for comparison")
            return {}
        
        # Initialize comparison results
        comparison = {
            "reference": {
                "file_name": reference_result.get("_file_name", "Reference"),
                "num_patients": reference_result.get("NumberPatients", 0)
            },
            "comparisons": [],
            "metrics_comparison": {},
            "summary_comparison": {}
        }
        
        # Extract reference metrics and summary
        ref_metrics = reference_result.get("Variable", [])
        ref_summary = reference_result.get("Summary", {})
        
        # Compare each result against the reference
        for result in results:
            result_comparison = {
                "file_name": result.get("_file_name", ""),
                "num_patients": result.get("NumberPatients", 0),
                "metrics_diff": {},
                "summary_diff": {}
            }
            
            # Compare metrics
            metrics = result.get("Variable", [])
            if isinstance(metrics, list) and isinstance(ref_metrics, list):
                for i in range(min(len(metrics), len(ref_metrics))):
                    if isinstance(metrics[i], (int, float)) and isinstance(ref_metrics[i], (int, float)):
                        abs_diff = metrics[i] - ref_metrics[i]
                        rel_diff = abs_diff / ref_metrics[i] if ref_metrics[i] != 0 else float('inf')
                        
                        result_comparison["metrics_diff"][f"metric_{i}"] = {
                            "reference": ref_metrics[i],
                            "value": metrics[i],
                            "abs_diff": abs_diff,
                            "rel_diff": rel_diff
                        }
            
            # Compare summary variables
            summary = result.get("Summary", {})
            for key, ref_value in ref_summary.items():
                if key in summary:
                    value = summary[key]
                    if isinstance(value, (int, float)) and isinstance(ref_value, (int, float)):
                        abs_diff = value - ref_value
                        rel_diff = abs_diff / ref_value if ref_value != 0 else float('inf')
                        
                        result_comparison["summary_diff"][key] = {
                            "reference": ref_value,
                            "value": value,
                            "abs_diff": abs_diff,
                            "rel_diff": rel_diff
                        }
            
            comparison["comparisons"].append(result_comparison)
        
        # Aggregate comparison statistics across all results
        for i, ref_value in enumerate(ref_metrics):
            if isinstance(ref_value, (int, float)):
                metric_key = f"metric_{i}"
                comparison["metrics_comparison"][metric_key] = self._calculate_comparison_stats(
                    [comp["metrics_diff"].get(metric_key, {}).get("value") 
                     for comp in comparison["comparisons"] 
                     if metric_key in comp["metrics_diff"]],
                    ref_value
                )
        
        for key, ref_value in ref_summary.items():
            if isinstance(ref_value, (int, float)):
                comparison["summary_comparison"][key] = self._calculate_comparison_stats(
                    [comp["summary_diff"].get(key, {}).get("value") 
                     for comp in comparison["comparisons"] 
                     if key in comp["summary_diff"]],
                    ref_value
                )
        
        self.logger.info(f"Compared {len(results)} result sets against reference")
        return comparison
    
    def _calculate_comparison_stats(self, values: List[float], reference: float) -> Dict[str, float]:
        """
        Calculate comparison statistics for a set of values against a reference.
        
        Args:
            values: List of values to compare
            reference: Reference value
            
        Returns:
            Dictionary with comparison statistics
        """
        values = [v for v in values if isinstance(v, (int, float))]
        
        if not values:
            return {
                "reference": reference,
                "count": 0
            }
        
        abs_diffs = [v - reference for v in values]
        rel_diffs = [diff / reference if reference != 0 else float('inf') for diff in abs_diffs]
        
        return {
            "reference": reference,
            "mean": np.mean(values),
            "median": np.median(values),
            "std": np.std(values),
            "min": np.min(values),
            "max": np.max(values),
            "mean_abs_diff": np.mean(abs_diffs),
            "mean_rel_diff": np.mean([d for d in rel_diffs if not np.isinf(d)]),
            "count": len(values)
        }
    
    def analyze_parameter_sensitivity(self, 
                                     parameter_sweep_results: pd.DataFrame,
                                     parameter_columns: List[str],
                                     metric_columns: List[str]) -> Dict[str, Any]:
        """
        Analyze parameter sensitivity from parameter sweep results.
        
        Args:
            parameter_sweep_results: DataFrame with parameter sweep results
            parameter_columns: List of parameter column names
            metric_columns: List of metric column names to analyze
            
        Returns:
            Dictionary with sensitivity analysis results
        """
        if parameter_sweep_results.empty:
            self.logger.warning("No parameter sweep results for sensitivity analysis")
            return {}
        
        # Check if specified columns exist
        missing_params = [col for col in parameter_columns if col not in parameter_sweep_results.columns]
        missing_metrics = [col for col in metric_columns if col not in parameter_sweep_results.columns]
        
        if missing_params:
            self.logger.warning(f"Missing parameter columns: {missing_params}")
            parameter_columns = [col for col in parameter_columns if col in parameter_sweep_results.columns]
        
        if missing_metrics:
            self.logger.warning(f"Missing metric columns: {missing_metrics}")
            metric_columns = [col for col in metric_columns if col in parameter_sweep_results.columns]
        
        if not parameter_columns or not metric_columns:
            self.logger.error("No valid parameter or metric columns for sensitivity analysis")
            return {}
        
        # Initialize sensitivity analysis results
        sensitivity = {
            "parameters": parameter_columns,
            "metrics": metric_columns,
            "analysis_time": datetime.datetime.now().isoformat(),
            "results": {}
        }
        
        # Analyze each parameter's effect on each metric
        for param in parameter_columns:
            param_values = parameter_sweep_results[param].unique()
            
            if len(param_values) <= 1:
                self.logger.warning(f"Parameter {param} has only one value, skipping")
                continue
            
            sensitivity["results"][param] = {}
            
            for metric in metric_columns:
                # Group by parameter value and calculate statistics for the metric
                grouped = parameter_sweep_results.groupby(param)[metric].agg(['mean', 'std', 'min', 'max', 'count'])
                
                # Calculate overall range and variation
                min_mean = grouped['mean'].min()
                max_mean = grouped['mean'].max()
                range_pct = (max_mean - min_mean) / min_mean * 100 if min_mean != 0 else float('inf')
                
                # Convert to dictionary for JSON serialization
                grouped_dict = grouped.reset_index().to_dict(orient='records')
                
                sensitivity["results"][param][metric] = {
                    "grouped_stats": grouped_dict,
                    "min_mean": min_mean,
                    "max_mean": max_mean,
                    "range_pct": range_pct,
                    "correlation": parameter_sweep_results[[param, metric]].corr().iloc[0, 1]
                }
        
        self.logger.info(f"Completed sensitivity analysis for {len(parameter_columns)} parameters and {len(metric_columns)} metrics")
        return sensitivity
    
    def generate_summary_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a comprehensive summary report from multiple result sets.
        
        Args:
            results: List of result dictionaries
            
        Returns:
            Dictionary with summary report
        """
        if not results:
            self.logger.warning("No results for summary report")
            return {}
        
        # Aggregate results
        aggregated = self.aggregate_results(results)
        
        # Create summary report
        report = {
            "title": "CMOST Simulation Summary Report",
            "generation_time": datetime.datetime.now().isoformat(),
            "num_simulations": len(results),
            "aggregated_results": aggregated,
            "key_metrics": {},
            "simulation_settings": {}
        }
        
        # Extract key metrics (assuming they're in the same position across all results)
        metrics_names = {
            "0": "Number of Cancer Deaths",
            "1": "Number of Cancer Cases",
            "2": "Number of Advanced Adenomas",
            "3": "Number of Early Adenomas",
            "4": "Number of Colonoscopies",
            "5": "Number of Polypectomies",
            "6": "Screening Costs",
            "7": "Treatment Costs",
            "8": "Total Costs",
            "9": "Life Years Lost",
            "10": "QALYs Lost"
        }
        
        for metric_id, metric_name in metrics_names.items():
            if f"metric_{metric_id}" in aggregated.get("metrics", {}):
                report["key_metrics"][metric_name] = aggregated["metrics"][f"metric_{metric_id}"]
        
        # Extract common simulation settings
        common_settings = {}
        if results:
            settings = results[0].get("Settings", {})
            for key, value in settings.items():
                # Check if this setting is the same across all results
                if all(result.get("Settings", {}).get(key) == value for result in results):
                    common_settings[key] = value
        
        report["simulation_settings"] = common_settings
        
        self.logger.info(f"Generated summary report for {len(results)} result sets")
        return report