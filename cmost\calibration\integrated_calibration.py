"""
Integrated calibration module that combines multiple machine learning approaches.

This module integrates the existing deep neural network calibration with
the new adaptive calibration methods, automatically selecting the best approach.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Callable
from enum import Enum
import numpy as np
import logging
import time
from datetime import datetime

from .adaptive_calibration import (
    AdaptiveCalibrator,
    CalibrationData,
    ParameterSpace,
    OptimizationMethod
)

# Try to import existing calibration modules, but handle missing dependencies gracefully
try:
    from ..calibration.auto_calibration import AutoCalibration
    DNN_AVAILABLE = True
except ImportError:
    DNN_AVAILABLE = False
    AutoCalibration = None

try:
    from ..calibration.benchmark import BenchmarkManager
    BENCHMARK_AVAILABLE = True
except ImportError:
    BENCHMARK_AVAILABLE = False
    BenchmarkManager = None


class CalibrationMethod(Enum):
    """Available calibration methods."""
    DEEP_NEURAL_NETWORK = "deep_neural_network"
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    BAYESIAN_OPTIMIZATION = "bayesian_optimization"
    AUTO_SELECT = "auto_select"


@dataclass
class CalibrationResult:
    """Result from calibration process."""
    
    method: CalibrationMethod
    parameters: Dict[str, float]
    error: float
    execution_time: float
    validation_metrics: Optional[Dict[str, Any]] = None
    convergence_info: Optional[Dict[str, Any]] = None
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of calibration result."""
        return {
            "method": self.method.value,
            "parameters": dict(self.parameters) if self.parameters else {},
            "error": float(self.error) if self.error != float('inf') else None,
            "execution_time": float(self.execution_time),
            "converged": bool(self.convergence_info.get("converged", False)) if self.convergence_info else False,
            "validation_error": float(self.validation_metrics.get("mean_error", 0)) if self.validation_metrics and self.validation_metrics.get("mean_error") else None
        }


class IntegratedCalibrator:
    """Integrated calibrator that combines multiple ML approaches."""
    
    def __init__(self, 
                 benchmarks: Optional[Dict] = None,
                 parameter_spaces: Optional[List[ParameterSpace]] = None,
                 output_dir: str = "integrated_calibration_results"):
        """Initialize integrated calibrator.
        
        Args:
            benchmarks: Benchmark data for calibration
            parameter_spaces: Parameter spaces for optimization
            output_dir: Output directory for results
        """
        self.benchmarks = benchmarks
        self.parameter_spaces = parameter_spaces or self._create_default_parameter_spaces()
        self.output_dir = output_dir
        self.logger = logging.getLogger("CMOST_IntegratedCalibrator")
        
        # Initialize individual calibrators
        self.dnn_calibrator = None
        self.adaptive_calibrator = None
        
        # Results storage
        self.calibration_results: List[CalibrationResult] = []
        self.best_result: Optional[CalibrationResult] = None
    
    def _create_default_parameter_spaces(self) -> List[ParameterSpace]:
        """Create default parameter spaces for calibration."""
        return [
            # Early adenoma generation parameters
            ParameterSpace("early_mult", 1.0, 10.0, 5.0),
            ParameterSpace("early_width", 0.1, 1.0, 0.5),
            ParameterSpace("early_center", 20.0, 80.0, 50.0),
            
            # Advanced adenoma progression parameters
            ParameterSpace("adv_mult", 0.5, 5.0, 2.0),
            ParameterSpace("adv_width", 0.1, 1.0, 0.5),
            ParameterSpace("adv_center", 20.0, 80.0, 50.0),
            
            # Cancer progression parameters
            ParameterSpace("cancer_mult", 0.1, 2.0, 1.0),
            ParameterSpace("cancer_width", 0.1, 1.0, 0.5),
            ParameterSpace("cancer_center", 20.0, 80.0, 50.0)
        ]
    
    def calibrate_with_dnn(self,
                          n_samples: int = 5000,
                          n_patients: int = 5000,
                          epochs: int = 50) -> CalibrationResult:
        """Calibrate using deep neural network approach.

        Args:
            n_samples: Number of LHS samples
            n_patients: Number of patients per simulation
            epochs: Number of training epochs

        Returns:
            Calibration result
        """
        self.logger.info("Starting DNN calibration...")
        start_time = time.time()

        if not DNN_AVAILABLE:
            self.logger.error("DNN calibration not available - missing dependencies")
            return CalibrationResult(
                method=CalibrationMethod.DEEP_NEURAL_NETWORK,
                parameters={},
                error=float('inf'),
                execution_time=time.time() - start_time,
                convergence_info={"converged": False, "error": "DNN dependencies not available"}
            )

        try:
            # Initialize DNN calibrator
            self.dnn_calibrator = AutoCalibration(
                benchmarks=self.benchmarks,
                output_dir=f"{self.output_dir}/dnn"
            )
            
            # Run calibration pipeline
            parameters = self.dnn_calibrator.run_calibration_pipeline(
                n_samples=n_samples,
                n_patients=n_patients,
                epochs=epochs
            )
            
            # Validate calibration
            validation_metrics = self.dnn_calibrator.validate_calibration(parameters)
            
            # Calculate overall error
            error = self._calculate_overall_error(validation_metrics)
            
            execution_time = time.time() - start_time
            
            result = CalibrationResult(
                method=CalibrationMethod.DEEP_NEURAL_NETWORK,
                parameters=parameters,
                error=error,
                execution_time=execution_time,
                validation_metrics=validation_metrics,
                convergence_info={"converged": True}
            )
            
            self.calibration_results.append(result)
            self.logger.info(f"DNN calibration completed in {execution_time:.2f}s with error {error:.6f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"DNN calibration failed: {e}")
            execution_time = time.time() - start_time
            
            return CalibrationResult(
                method=CalibrationMethod.DEEP_NEURAL_NETWORK,
                parameters={},
                error=float('inf'),
                execution_time=execution_time,
                convergence_info={"converged": False, "error": str(e)}
            )
    
    def calibrate_with_adaptive(self, 
                               method: OptimizationMethod = OptimizationMethod.RANDOM_FOREST,
                               max_iterations: int = 50) -> CalibrationResult:
        """Calibrate using adaptive ML approach.
        
        Args:
            method: Optimization method to use
            max_iterations: Maximum optimization iterations
            
        Returns:
            Calibration result
        """
        self.logger.info(f"Starting adaptive calibration with {method.value}...")
        start_time = time.time()
        
        try:
            # Initialize adaptive calibrator
            self.adaptive_calibrator = AdaptiveCalibrator(
                parameter_spaces=self.parameter_spaces,
                optimization_method=method
            )
            
            # Prepare calibration data from benchmarks
            calibration_data = self._prepare_calibration_data()
            
            # Run calibration
            parameters = self.adaptive_calibrator.calibrate(
                calibration_data=calibration_data,
                max_iterations=max_iterations
            )
            
            # Get calibration summary
            summary = self.adaptive_calibrator.get_calibration_summary()
            error = summary.get("latest_error", float('inf'))
            
            execution_time = time.time() - start_time
            
            result = CalibrationResult(
                method=CalibrationMethod.RANDOM_FOREST if method == OptimizationMethod.RANDOM_FOREST else CalibrationMethod.GRADIENT_BOOSTING,
                parameters=parameters,
                error=error,
                execution_time=execution_time,
                validation_metrics={"calibration_summary": summary},
                convergence_info={"converged": summary.get("converged", False)}
            )
            
            self.calibration_results.append(result)
            self.logger.info(f"Adaptive calibration completed in {execution_time:.2f}s with error {error:.6f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Adaptive calibration failed: {e}")
            execution_time = time.time() - start_time
            
            return CalibrationResult(
                method=CalibrationMethod.RANDOM_FOREST if method == OptimizationMethod.RANDOM_FOREST else CalibrationMethod.GRADIENT_BOOSTING,
                parameters={},
                error=float('inf'),
                execution_time=execution_time,
                convergence_info={"converged": False, "error": str(e)}
            )
    
    def auto_select_best_method(self, 
                               quick_evaluation: bool = True,
                               max_time_per_method: float = 300.0) -> CalibrationResult:
        """Automatically select and run the best calibration method.
        
        Args:
            quick_evaluation: Whether to use quick evaluation parameters
            max_time_per_method: Maximum time per method in seconds
            
        Returns:
            Best calibration result
        """
        self.logger.info("Starting auto-selection of best calibration method...")
        
        methods_to_try = [
            (CalibrationMethod.DEEP_NEURAL_NETWORK, self._run_dnn_quick if quick_evaluation else self._run_dnn_full),
            (CalibrationMethod.RANDOM_FOREST, self._run_rf_quick if quick_evaluation else self._run_rf_full),
        ]
        
        best_result = None
        best_error = float('inf')
        
        for method, run_func in methods_to_try:
            self.logger.info(f"Evaluating {method.value}...")
            
            try:
                # Run with timeout
                result = self._run_with_timeout(run_func, max_time_per_method)
                
                if result and result.error < best_error:
                    best_error = result.error
                    best_result = result
                    self.logger.info(f"New best method: {method.value} with error {best_error:.6f}")
                
            except Exception as e:
                self.logger.warning(f"Method {method.value} failed: {e}")
                continue
        
        if best_result:
            self.best_result = best_result
            self.logger.info(f"Auto-selection completed. Best method: {best_result.method.value}")
            
            # Run full calibration with best method
            if quick_evaluation:
                self.logger.info("Running full calibration with best method...")
                if best_result.method == CalibrationMethod.DEEP_NEURAL_NETWORK:
                    best_result = self.calibrate_with_dnn()
                elif best_result.method == CalibrationMethod.RANDOM_FOREST:
                    best_result = self.calibrate_with_adaptive(OptimizationMethod.RANDOM_FOREST)
                
                self.best_result = best_result
        else:
            self.logger.error("All calibration methods failed")
            best_result = CalibrationResult(
                method=CalibrationMethod.AUTO_SELECT,
                parameters={},
                error=float('inf'),
                execution_time=0.0,
                convergence_info={"converged": False, "error": "All methods failed"}
            )
        
        return best_result
    
    def _run_dnn_quick(self) -> CalibrationResult:
        """Run quick DNN calibration for evaluation."""
        return self.calibrate_with_dnn(n_samples=1000, n_patients=1000, epochs=20)
    
    def _run_dnn_full(self) -> CalibrationResult:
        """Run full DNN calibration."""
        return self.calibrate_with_dnn(n_samples=5000, n_patients=5000, epochs=100)
    
    def _run_rf_quick(self) -> CalibrationResult:
        """Run quick Random Forest calibration for evaluation."""
        return self.calibrate_with_adaptive(OptimizationMethod.RANDOM_FOREST, max_iterations=20)
    
    def _run_rf_full(self) -> CalibrationResult:
        """Run full Random Forest calibration."""
        return self.calibrate_with_adaptive(OptimizationMethod.RANDOM_FOREST, max_iterations=100)
    
    def _run_with_timeout(self, func: Callable, timeout: float) -> Optional[CalibrationResult]:
        """Run function with timeout."""
        import signal
        
        def timeout_handler(signum, frame):
            # Suppress unused parameter warnings
            _ = signum, frame
            raise TimeoutError("Function execution timed out")
        
        # Set timeout
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(int(timeout))
        
        try:
            result = func()
            signal.alarm(0)  # Cancel timeout
            return result
        except TimeoutError:
            self.logger.warning(f"Function timed out after {timeout} seconds")
            signal.alarm(0)  # Cancel timeout
            return None
        except Exception as e:
            signal.alarm(0)  # Cancel timeout
            raise e
    
    def _prepare_calibration_data(self) -> List[CalibrationData]:
        """Prepare calibration data from benchmarks."""
        if not self.benchmarks:
            # Create default benchmarks if BenchmarkManager is available
            if BENCHMARK_AVAILABLE:
                bm = BenchmarkManager()
                bm.load_default_benchmarks()
                self.benchmarks = bm.benchmarks
            else:
                # Use simple default benchmarks
                self.benchmarks = {}
        
        calibration_data = []
        
        # Create calibration data for different targets
        # This is a simplified example - in practice, you would extract
        # observed and simulated values from actual data
        
        # Cancer incidence calibration
        observed_cancer = [50, 80, 120, 180, 250]  # Example values
        simulated_cancer = [45, 85, 115, 175, 260]  # Example values
        
        calibration_data.append(CalibrationData(
            target_name="cancer_incidence",
            observed_values=observed_cancer,
            simulated_values=simulated_cancer
        ))
        
        # Polyp prevalence calibration
        observed_polyps = [10, 15, 25, 35, 45]  # Example values
        simulated_polyps = [12, 14, 27, 33, 47]  # Example values
        
        calibration_data.append(CalibrationData(
            target_name="polyp_prevalence",
            observed_values=observed_polyps,
            simulated_values=simulated_polyps
        ))
        
        return calibration_data
    
    def _calculate_overall_error(self, validation_metrics: Dict[str, Any]) -> float:
        """Calculate overall error from validation metrics."""
        if not validation_metrics:
            return float('inf')
        
        total_error = 0.0
        count = 0
        
        for _, metric_data in validation_metrics.items():
            if isinstance(metric_data, dict) and 'rel_error_pct' in metric_data:
                total_error += metric_data['rel_error_pct']
                count += 1
        
        return total_error / count if count > 0 else float('inf')
    
    def get_comparison_report(self) -> Dict[str, Any]:
        """Get comparison report of all calibration methods tried."""
        if not self.calibration_results:
            return {"message": "No calibration results available"}
        
        # Find best result if not already set
        if not self.best_result and self.calibration_results:
            self.best_result = min(self.calibration_results, key=lambda x: x.error)

        report = {
            "total_methods_tried": len(self.calibration_results),
            "best_method": self.best_result.method.value if self.best_result else None,
            "best_error": self.best_result.error if self.best_result else None,
            "results": []
        }
        
        for result in sorted(self.calibration_results, key=lambda x: x.error):
            report["results"].append({
                "method": result.method.value,
                "error": float(result.error) if result.error != float('inf') else None,
                "execution_time": float(result.execution_time),
                "converged": bool(result.convergence_info.get("converged", False)) if result.convergence_info else False
            })
        
        return report
    
    def save_results(self, filepath: str) -> None:
        """Save calibration results to file."""
        import json
        import os
        
        os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
        
        results_data = {
            "timestamp": datetime.now().isoformat(),
            "best_result": self.best_result.get_summary() if self.best_result else None,
            "all_results": [result.get_summary() for result in self.calibration_results],
            "comparison_report": self.get_comparison_report()
        }
        
        with open(filepath, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        self.logger.info(f"Results saved to {filepath}")


def run_integrated_calibration(
    benchmarks: Optional[Dict] = None,
    method: CalibrationMethod = CalibrationMethod.AUTO_SELECT,
    output_dir: str = "integrated_calibration_results",
    quick_evaluation: bool = False
) -> CalibrationResult:
    """Run integrated calibration with specified method.
    
    Args:
        benchmarks: Benchmark data for calibration
        method: Calibration method to use
        output_dir: Output directory for results
        quick_evaluation: Whether to use quick evaluation parameters
        
    Returns:
        Calibration result
    """
    calibrator = IntegratedCalibrator(
        benchmarks=benchmarks,
        output_dir=output_dir
    )
    
    if method == CalibrationMethod.AUTO_SELECT:
        result = calibrator.auto_select_best_method(quick_evaluation=quick_evaluation)
    elif method == CalibrationMethod.DEEP_NEURAL_NETWORK:
        if quick_evaluation:
            result = calibrator.calibrate_with_dnn(n_samples=1000, n_patients=1000, epochs=20)
        else:
            result = calibrator.calibrate_with_dnn()
    elif method == CalibrationMethod.RANDOM_FOREST:
        max_iter = 20 if quick_evaluation else 100
        result = calibrator.calibrate_with_adaptive(OptimizationMethod.RANDOM_FOREST, max_iter)
    else:
        raise ValueError(f"Unsupported calibration method: {method}")
    
    # Save results
    calibrator.save_results(f"{output_dir}/calibration_results.json")
    
    return result


if __name__ == "__main__":
    # Example usage
    result = run_integrated_calibration(
        method=CalibrationMethod.AUTO_SELECT,
        quick_evaluation=True
    )
    
    print(f"Best calibration method: {result.method.value}")
    print(f"Final error: {result.error:.6f}")
    print(f"Execution time: {result.execution_time:.2f}s")
