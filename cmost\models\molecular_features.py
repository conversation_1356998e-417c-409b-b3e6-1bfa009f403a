"""
Molecular features module for CMOST simulation.

This module implements molecular characteristics that influence
colorectal cancer development and progression.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
import numpy as np


class MSIStatus(Enum):
    """Microsatellite instability status."""
    MSS = "microsatellite_stable"
    MSI_LOW = "msi_low"
    MSI_HIGH = "msi_high"


class CIMPStatus(Enum):
    """CpG Island Methylator Phenotype status."""
    CIMP_NEGATIVE = "cimp_negative"
    CIMP_LOW = "cimp_low"
    CIMP_HIGH = "cimp_high"


class KRASStatus(Enum):
    """KRAS mutation status."""
    WILD_TYPE = "wild_type"
    MUTATED = "mutated"


class BRAFStatus(Enum):
    """BRAF mutation status."""
    WILD_TYPE = "wild_type"
    MUTATED = "mutated"


class PIK3CAStatus(Enum):
    """PIK3CA mutation status."""
    WILD_TYPE = "wild_type"
    MUTATED = "mutated"


@dataclass
class MolecularProfile:
    """Molecular profile of a lesion or cancer."""
    
    # Microsatellite instability
    msi_status: MSIStatus = MSIStatus.MSS
    
    # CpG Island Methylator Phenotype
    cimp_status: CIMPStatus = CIMPStatus.CIMP_NEGATIVE
    
    # Key mutations
    kras_status: KRASStatus = KRASStatus.WILD_TYPE
    braf_status: BRAFStatus = BRAFStatus.WILD_TYPE
    pik3ca_status: PIK3CAStatus = PIK3CAStatus.WILD_TYPE
    
    # TP53 mutation (common in traditional pathway)
    tp53_mutated: bool = False
    
    # APC mutation (early event in traditional pathway)
    apc_mutated: bool = False
    
    # Additional molecular features
    chromosomal_instability: bool = False
    hypermutation: bool = False
    
    # Pathway classification
    pathway: str = "traditional"  # "traditional", "serrated", "mixed"
    
    def __post_init__(self):
        """Initialize derived molecular characteristics."""
        self._update_pathway_classification()
        self._update_molecular_interactions()
    
    def _update_pathway_classification(self) -> None:
        """Update pathway classification based on molecular features."""
        # Serrated pathway characteristics
        if (self.braf_status == BRAFStatus.MUTATED or 
            self.cimp_status == CIMPStatus.CIMP_HIGH):
            self.pathway = "serrated"
        
        # Traditional pathway characteristics
        elif (self.apc_mutated or 
              self.chromosomal_instability or
              self.kras_status == KRASStatus.MUTATED):
            self.pathway = "traditional"
        
        # Mixed features
        elif (self.kras_status == KRASStatus.MUTATED and 
              self.cimp_status != CIMPStatus.CIMP_NEGATIVE):
            self.pathway = "mixed"
    
    def _update_molecular_interactions(self) -> None:
        """Update molecular interactions and dependencies."""
        # BRAF and KRAS are mutually exclusive
        if self.braf_status == BRAFStatus.MUTATED:
            self.kras_status = KRASStatus.WILD_TYPE
        
        # CIMP-high often associated with BRAF mutation
        if self.cimp_status == CIMPStatus.CIMP_HIGH:
            if np.random.random() < 0.6:  # 60% association
                self.braf_status = BRAFStatus.MUTATED
        
        # MSI-high often associated with hypermutation
        if self.msi_status == MSIStatus.MSI_HIGH:
            self.hypermutation = True
    
    def get_progression_modifier(self) -> float:
        """Get progression rate modifier based on molecular profile.
        
        Returns:
            Progression rate modifier (1.0 = normal rate)
        """
        modifier = 1.0
        
        # MSI status effects
        if self.msi_status == MSIStatus.MSI_HIGH:
            modifier *= 0.7  # MSI-high tumors progress slower
        elif self.msi_status == MSIStatus.MSI_LOW:
            modifier *= 0.9
        
        # CIMP effects
        if self.cimp_status == CIMPStatus.CIMP_HIGH:
            modifier *= 1.2  # CIMP-high may progress faster
        
        # Mutation effects
        if self.kras_status == KRASStatus.MUTATED:
            modifier *= 1.3  # KRAS mutations accelerate progression
        
        if self.braf_status == BRAFStatus.MUTATED:
            modifier *= 1.1  # BRAF mutations slightly accelerate
        
        if self.tp53_mutated:
            modifier *= 1.4  # TP53 loss accelerates progression
        
        if self.apc_mutated:
            modifier *= 1.2  # APC loss promotes progression
        
        # Chromosomal instability
        if self.chromosomal_instability:
            modifier *= 1.3
        
        return modifier
    
    def get_treatment_response_modifier(self) -> Dict[str, float]:
        """Get treatment response modifiers based on molecular profile.
        
        Returns:
            Dictionary of treatment response modifiers
        """
        modifiers = {
            'surgery': 1.0,
            'chemotherapy': 1.0,
            'targeted_therapy': 1.0,
            'immunotherapy': 1.0
        }
        
        # MSI-high tumors respond better to immunotherapy
        if self.msi_status == MSIStatus.MSI_HIGH:
            modifiers['immunotherapy'] = 2.5
            modifiers['chemotherapy'] = 0.8  # Less responsive to traditional chemo
        
        # BRAF mutations affect targeted therapy
        if self.braf_status == BRAFStatus.MUTATED:
            modifiers['targeted_therapy'] = 1.8  # BRAF inhibitors
            modifiers['chemotherapy'] = 0.9
        
        # KRAS mutations affect treatment response
        if self.kras_status == KRASStatus.MUTATED:
            modifiers['targeted_therapy'] = 0.3  # Resistant to EGFR inhibitors
            modifiers['chemotherapy'] = 0.9
        
        return modifiers
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert molecular profile to dictionary.
        
        Returns:
            Dictionary representation
        """
        return {
            'msi_status': self.msi_status.value,
            'cimp_status': self.cimp_status.value,
            'kras_status': self.kras_status.value,
            'braf_status': self.braf_status.value,
            'pik3ca_status': self.pik3ca_status.value,
            'tp53_mutated': self.tp53_mutated,
            'apc_mutated': self.apc_mutated,
            'chromosomal_instability': self.chromosomal_instability,
            'hypermutation': self.hypermutation,
            'pathway': self.pathway
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MolecularProfile':
        """Create molecular profile from dictionary.
        
        Args:
            data: Dictionary containing molecular profile data
            
        Returns:
            MolecularProfile instance
        """
        return cls(
            msi_status=MSIStatus(data.get('msi_status', MSIStatus.MSS.value)),
            cimp_status=CIMPStatus(data.get('cimp_status', CIMPStatus.CIMP_NEGATIVE.value)),
            kras_status=KRASStatus(data.get('kras_status', KRASStatus.WILD_TYPE.value)),
            braf_status=BRAFStatus(data.get('braf_status', BRAFStatus.WILD_TYPE.value)),
            pik3ca_status=PIK3CAStatus(data.get('pik3ca_status', PIK3CAStatus.WILD_TYPE.value)),
            tp53_mutated=data.get('tp53_mutated', False),
            apc_mutated=data.get('apc_mutated', False),
            chromosomal_instability=data.get('chromosomal_instability', False),
            hypermutation=data.get('hypermutation', False),
            pathway=data.get('pathway', 'traditional')
        )


class MolecularProfileGenerator:
    """Generator for molecular profiles based on lesion characteristics."""
    
    def __init__(self):
        """Initialize molecular profile generator."""
        # Prevalence rates for different molecular features
        self.prevalence_rates = {
            'msi_high': 0.15,  # 15% of CRCs are MSI-high
            'cimp_high': 0.20,  # 20% are CIMP-high
            'kras_mutation': 0.40,  # 40% have KRAS mutations
            'braf_mutation': 0.10,  # 10% have BRAF mutations
            'pik3ca_mutation': 0.15,  # 15% have PIK3CA mutations
            'tp53_mutation': 0.60,  # 60% have TP53 mutations
            'apc_mutation': 0.80,  # 80% have APC mutations
            'chromosomal_instability': 0.70  # 70% have CIN
        }
    
    def generate_profile(self, 
                        lesion_type: str = "adenoma",
                        stage: int = 1,
                        location: int = 1,
                        patient_age: int = 60,
                        family_history: bool = False) -> MolecularProfile:
        """Generate molecular profile for a lesion.
        
        Args:
            lesion_type: Type of lesion ("adenoma", "serrated", "cancer")
            stage: Stage of the lesion
            location: Anatomical location
            patient_age: Patient age
            family_history: Whether patient has family history
            
        Returns:
            Generated molecular profile
        """
        profile = MolecularProfile()
        
        # Adjust probabilities based on lesion characteristics
        age_factor = min(2.0, patient_age / 50.0)  # Older patients more mutations
        location_factor = 1.3 if location >= 4 else 0.8  # Proximal vs distal
        family_factor = 1.5 if family_history else 1.0
        stage_factor = 1.0 + (stage - 1) * 0.2  # More mutations in advanced stages
        
        # Generate MSI status
        msi_prob = self.prevalence_rates['msi_high'] * location_factor * family_factor
        if np.random.random() < msi_prob:
            profile.msi_status = MSIStatus.MSI_HIGH
        elif np.random.random() < 0.1:  # 10% MSI-low
            profile.msi_status = MSIStatus.MSI_LOW
        
        # Generate CIMP status
        cimp_prob = self.prevalence_rates['cimp_high'] * location_factor
        if lesion_type == "serrated":
            cimp_prob *= 2.0  # Serrated lesions more likely CIMP-high
        
        if np.random.random() < cimp_prob:
            profile.cimp_status = CIMPStatus.CIMP_HIGH
        elif np.random.random() < 0.15:  # 15% CIMP-low
            profile.cimp_status = CIMPStatus.CIMP_LOW
        
        # Generate mutations
        mutations = {
            'kras': self.prevalence_rates['kras_mutation'] * age_factor * stage_factor,
            'braf': self.prevalence_rates['braf_mutation'] * location_factor,
            'pik3ca': self.prevalence_rates['pik3ca_mutation'] * age_factor,
            'tp53': self.prevalence_rates['tp53_mutation'] * stage_factor,
            'apc': self.prevalence_rates['apc_mutation'] * stage_factor
        }
        
        # Apply mutations
        if np.random.random() < mutations['kras']:
            profile.kras_status = KRASStatus.MUTATED
        
        if np.random.random() < mutations['braf']:
            profile.braf_status = BRAFStatus.MUTATED
        
        if np.random.random() < mutations['pik3ca']:
            profile.pik3ca_status = PIK3CAStatus.MUTATED
        
        profile.tp53_mutated = np.random.random() < mutations['tp53']
        profile.apc_mutated = np.random.random() < mutations['apc']
        
        # Chromosomal instability
        cin_prob = self.prevalence_rates['chromosomal_instability'] * age_factor
        if profile.msi_status == MSIStatus.MSI_HIGH:
            cin_prob *= 0.1  # MSI-high tumors rarely have CIN
        
        profile.chromosomal_instability = np.random.random() < cin_prob
        
        return profile
