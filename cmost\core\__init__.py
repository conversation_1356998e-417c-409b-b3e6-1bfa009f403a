"""
Core simulation modules for CMOST (Colorectal Microsimulation Outcomes Screening Tool).

This package contains the core simulation logic for the CMOST model, including:
- Simulation engine
- Disease progression model
- Population generation and management
"""

from .simulation import Simulation
from .progression import ProgressionModel, PolypStage, CancerStage
from .population import PopulationGenerator
from .birth_cohort import BirthCohortSimulation, BirthCohort
from .dual_architecture import DualArchitectureManager, SimulationMode, ArchitectureConfig

__all__ = [
    'Simulation',
    'ProgressionModel',
    'PolypStage',
    'CancerStage',
    'PopulationGenerator',
    'BirthCohortSimulation',
    'BirthCohort',
    'DualArchitectureManager',
    'SimulationMode',
    'ArchitectureConfig'
]