"""
Unit tests for dual architecture functionality in CMOST.
"""

import unittest
from unittest.mock import Mock, patch
import numpy as np

from cmost.core.dual_architecture import (
    DualArchitectureManager, 
    SimulationMode, 
    ArchitectureConfig
)
from cmost.config.settings import Settings


class TestArchitectureConfig(unittest.TestCase):
    """Test cases for ArchitectureConfig class."""
    
    def test_default_config(self):
        """Test default configuration."""
        config = ArchitectureConfig()
        
        self.assertEqual(config.primary_mode, SimulationMode.NATURAL_POPULATION)
        self.assertIsNone(config.secondary_mode)
        self.assertIsNone(config.switch_year)
        self.assertEqual(config.natural_population_size, 100000)
        self.assertEqual(config.annual_birth_cohort_size, 1000)
        self.assertEqual(config.population_split_ratio, 0.7)
    
    def test_custom_config(self):
        """Test custom configuration."""
        config = ArchitectureConfig(
            primary_mode=SimulationMode.BIRTH_COHORT,
            secondary_mode=SimulationMode.NATURAL_POPULATION,
            switch_year=25,
            natural_population_size=50000,
            annual_birth_cohort_size=500
        )
        
        self.assertEqual(config.primary_mode, SimulationMode.BIRTH_COHORT)
        self.assertEqual(config.secondary_mode, SimulationMode.NATURAL_POPULATION)
        self.assertEqual(config.switch_year, 25)
        self.assertEqual(config.natural_population_size, 50000)
        self.assertEqual(config.annual_birth_cohort_size, 500)


class TestDualArchitectureManager(unittest.TestCase):
    """Test cases for DualArchitectureManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.settings = Settings()
        self.config = ArchitectureConfig()
        
        # Mock the simulation classes to avoid complex initialization
        with patch('cmost.core.dual_architecture.Simulation') as mock_sim, \
             patch('cmost.core.dual_architecture.BirthCohortSimulation') as mock_birth:
            self.manager = DualArchitectureManager(self.settings, self.config)
            self.mock_natural_sim = mock_sim.return_value
            self.mock_birth_sim = mock_birth.return_value
    
    def test_initialization_natural_population(self):
        """Test initialization with natural population mode."""
        config = ArchitectureConfig(primary_mode=SimulationMode.NATURAL_POPULATION)
        
        with patch('cmost.core.dual_architecture.Simulation') as mock_sim:
            manager = DualArchitectureManager(self.settings, config)
            mock_sim.assert_called_once_with(self.settings)
            self.assertEqual(manager.current_mode, SimulationMode.NATURAL_POPULATION)
    
    def test_initialization_birth_cohort(self):
        """Test initialization with birth cohort mode."""
        config = ArchitectureConfig(primary_mode=SimulationMode.BIRTH_COHORT)
        
        with patch('cmost.core.dual_architecture.BirthCohortSimulation') as mock_birth:
            manager = DualArchitectureManager(self.settings, config)
            mock_birth.assert_called_once_with(self.settings)
            self.assertEqual(manager.current_mode, SimulationMode.BIRTH_COHORT)
    
    def test_initialization_hybrid(self):
        """Test initialization with hybrid mode."""
        config = ArchitectureConfig(primary_mode=SimulationMode.HYBRID)
        
        with patch('cmost.core.dual_architecture.Simulation') as mock_sim, \
             patch('cmost.core.dual_architecture.BirthCohortSimulation') as mock_birth:
            manager = DualArchitectureManager(self.settings, config)
            mock_sim.assert_called_once_with(self.settings)
            mock_birth.assert_called_once_with(self.settings)
            self.assertEqual(manager.current_mode, SimulationMode.HYBRID)
    
    def test_get_current_mode(self):
        """Test getting current mode."""
        self.assertEqual(self.manager.get_current_mode(), SimulationMode.NATURAL_POPULATION)
    
    def test_manual_mode_switch(self):
        """Test manual mode switching."""
        # Switch to birth cohort mode
        self.manager.switch_mode(SimulationMode.BIRTH_COHORT)
        self.assertEqual(self.manager.current_mode, SimulationMode.BIRTH_COHORT)
        
        # Switch back to natural population
        self.manager.switch_mode(SimulationMode.NATURAL_POPULATION)
        self.assertEqual(self.manager.current_mode, SimulationMode.NATURAL_POPULATION)
    
    def test_switch_to_same_mode(self):
        """Test switching to the same mode (should be no-op)."""
        original_mode = self.manager.current_mode
        self.manager.switch_mode(original_mode)
        self.assertEqual(self.manager.current_mode, original_mode)
    
    def test_single_mode_simulation_natural(self):
        """Test running single mode simulation (natural population)."""
        # Set up mock return values
        self.mock_natural_sim.run.return_value = {
            'total_patients': 1000,
            'cancer_cases': 50,
            'deaths': 100
        }
        
        # Run simulation
        results = self.manager._run_single_mode_simulation(10)
        
        # Verify calls
        self.mock_natural_sim.initialize_population.assert_called_once_with(100000)
        self.mock_natural_sim.run.assert_called_once_with(10)
        
        # Verify results
        self.assertEqual(results['total_patients'], 1000)
        self.assertEqual(results['cancer_cases'], 50)
        self.assertEqual(results['deaths'], 100)
    
    def test_single_mode_simulation_birth_cohort(self):
        """Test running single mode simulation (birth cohort)."""
        # Switch to birth cohort mode and ensure birth cohort simulation is available
        self.manager.current_mode = SimulationMode.BIRTH_COHORT
        self.manager.birth_cohort_simulation = self.mock_birth_sim

        # Set up mock return values
        mock_results = {
            'total_patients': 500,
            'cancer_cases': 25,
            'deaths': 50
        }

        with patch.object(self.manager, '_run_birth_cohort_years', return_value=mock_results):
            results = self.manager._run_single_mode_simulation(10)

        # Verify results
        self.assertEqual(results['total_patients'], 500)
        self.assertEqual(results['cancer_cases'], 25)
        self.assertEqual(results['deaths'], 50)
    
    def test_combine_simulation_results(self):
        """Test combining simulation results."""
        results1 = {
            'total_patients': 1000,
            'cancer_cases': 50,
            'deaths': 100,
            'screenings_performed': 200
        }
        
        results2 = {
            'total_patients': 500,
            'cancer_cases': 25,
            'deaths': 50,
            'screenings_performed': 100
        }
        
        combined = self.manager._combine_simulation_results(results1, results2)
        
        # Check structure
        self.assertIn('phase1_results', combined)
        self.assertIn('phase2_results', combined)
        self.assertIn('combined_metrics', combined)
        self.assertIn('architecture_info', combined)
        
        # Check combined metrics
        metrics = combined['combined_metrics']
        self.assertEqual(metrics['total_patients'], 1500)
        self.assertEqual(metrics['cancer_cases'], 75)
        self.assertEqual(metrics['deaths'], 150)
        self.assertEqual(metrics['screenings_performed'], 300)
        
        # Check architecture info
        arch_info = combined['architecture_info']
        self.assertEqual(arch_info['primary_mode'], 'natural_population')
    
    def test_get_simulation_summary(self):
        """Test getting simulation summary."""
        summary = self.manager.get_simulation_summary()
        
        # Check structure
        self.assertIn('configuration', summary)
        self.assertIn('current_state', summary)
        self.assertIn('results_available', summary)
        
        # Check configuration
        config = summary['configuration']
        self.assertEqual(config['primary_mode'], 'natural_population')
        self.assertIsNone(config['secondary_mode'])
        self.assertEqual(config['natural_population_size'], 100000)
        self.assertEqual(config['annual_birth_cohort_size'], 1000)
        
        # Check current state
        state = summary['current_state']
        self.assertEqual(state['current_mode'], 'natural_population')
        self.assertEqual(state['current_year'], 0)
        
        # Check results availability
        self.assertFalse(summary['results_available'])


class TestSimulationModeEnum(unittest.TestCase):
    """Test cases for SimulationMode enum."""
    
    def test_enum_values(self):
        """Test enum values."""
        self.assertEqual(SimulationMode.NATURAL_POPULATION.value, "natural_population")
        self.assertEqual(SimulationMode.BIRTH_COHORT.value, "birth_cohort")
        self.assertEqual(SimulationMode.HYBRID.value, "hybrid")
    
    def test_enum_comparison(self):
        """Test enum comparison."""
        mode1 = SimulationMode.NATURAL_POPULATION
        mode2 = SimulationMode.NATURAL_POPULATION
        mode3 = SimulationMode.BIRTH_COHORT
        
        self.assertEqual(mode1, mode2)
        self.assertNotEqual(mode1, mode3)


class TestDualArchitectureIntegration(unittest.TestCase):
    """Integration tests for dual architecture functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.settings = Settings()
    
    def test_mode_switching_configuration(self):
        """Test configuration with mode switching."""
        config = ArchitectureConfig(
            primary_mode=SimulationMode.NATURAL_POPULATION,
            secondary_mode=SimulationMode.BIRTH_COHORT,
            switch_year=20
        )
        
        with patch('cmost.core.dual_architecture.Simulation'), \
             patch('cmost.core.dual_architecture.BirthCohortSimulation'):
            manager = DualArchitectureManager(self.settings, config)
            
            # Should initialize both simulation engines
            self.assertIsNotNone(manager.natural_simulation)
            self.assertIsNotNone(manager.birth_cohort_simulation)
            
            # Should start in primary mode
            self.assertEqual(manager.current_mode, SimulationMode.NATURAL_POPULATION)
    
    def test_hybrid_mode_configuration(self):
        """Test hybrid mode configuration."""
        config = ArchitectureConfig(
            primary_mode=SimulationMode.HYBRID,
            population_split_ratio=0.8
        )
        
        with patch('cmost.core.dual_architecture.Simulation'), \
             patch('cmost.core.dual_architecture.BirthCohortSimulation'):
            manager = DualArchitectureManager(self.settings, config)
            
            # Should initialize both simulation engines
            self.assertIsNotNone(manager.natural_simulation)
            self.assertIsNotNone(manager.birth_cohort_simulation)
            
            # Should be in hybrid mode
            self.assertEqual(manager.current_mode, SimulationMode.HYBRID)


if __name__ == '__main__':
    unittest.main()
