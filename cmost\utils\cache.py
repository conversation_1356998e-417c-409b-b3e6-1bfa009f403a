"""
缓存系统模块

提供多层次缓存功能，包括LRU缓存、持久化缓存、分布式缓存等，用于加速CMOST仿真计算。
"""

import os
import time
import pickle
import hashlib
import threading
from typing import Any, Dict, List, Optional, Callable, Union, Tuple
from collections import OrderedDict
from dataclasses import dataclass, field
from pathlib import Path
import logging
import json
import sqlite3
from contextlib import contextmanager
import functools


logger = logging.getLogger(__name__)


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size: int = 0
    max_size: int = 0
    
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / max(1, total)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'hits': self.hits,
            'misses': self.misses,
            'evictions': self.evictions,
            'size': self.size,
            'max_size': self.max_size,
            'hit_rate': self.hit_rate()
        }


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, maxsize: int = 128, ttl: Optional[float] = None):
        self.maxsize = maxsize
        self.ttl = ttl  # 生存时间（秒）
        self.cache = OrderedDict()
        self.timestamps = {}  # 存储时间戳
        self.stats = CacheStats(max_size=maxsize)
        self._lock = threading.RLock()
    
    def _is_expired(self, key: Any) -> bool:
        """检查键是否过期"""
        if self.ttl is None:
            return False
        
        timestamp = self.timestamps.get(key)
        if timestamp is None:
            return True
        
        return time.time() - timestamp > self.ttl
    
    def get(self, key: Any, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            if key in self.cache and not self._is_expired(key):
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                self.stats.hits += 1
                return value
            else:
                if key in self.cache:
                    # 过期的键
                    self._remove_key(key)
                self.stats.misses += 1
                return default
    
    def put(self, key: Any, value: Any):
        """设置缓存值"""
        with self._lock:
            current_time = time.time()
            
            if key in self.cache:
                # 更新现有值
                self.cache.pop(key)
            elif len(self.cache) >= self.maxsize:
                # 删除最久未使用的项
                oldest_key = next(iter(self.cache))
                self._remove_key(oldest_key)
                self.stats.evictions += 1
            
            self.cache[key] = value
            self.timestamps[key] = current_time
            self.stats.size = len(self.cache)
    
    def _remove_key(self, key: Any):
        """移除键"""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
        self.stats.size = len(self.cache)
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.timestamps.clear()
            self.stats = CacheStats(max_size=self.maxsize)
    
    def cleanup_expired(self):
        """清理过期项"""
        if self.ttl is None:
            return
        
        with self._lock:
            expired_keys = [key for key in self.cache if self._is_expired(key)]
            for key in expired_keys:
                self._remove_key(key)
    
    def get_stats(self) -> CacheStats:
        """获取统计信息"""
        return self.stats


class PersistentCache:
    """持久化缓存"""
    
    def __init__(self, cache_dir: Union[str, Path], max_size_mb: float = 100):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_mb = max_size_mb
        self.stats = CacheStats()
        self._lock = threading.RLock()
        
        # 初始化数据库
        self.db_path = self.cache_dir / "cache.db"
        self._init_db()
    
    def _init_db(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache_entries (
                    key_hash TEXT PRIMARY KEY,
                    key_data BLOB,
                    value_path TEXT,
                    created_at REAL,
                    accessed_at REAL,
                    size_bytes INTEGER
                )
            """)
            conn.execute("CREATE INDEX IF NOT EXISTS idx_accessed_at ON cache_entries(accessed_at)")
    
    def _get_key_hash(self, key: Any) -> str:
        """获取键的哈希值"""
        key_bytes = pickle.dumps(key)
        return hashlib.sha256(key_bytes).hexdigest()
    
    def _get_value_path(self, key_hash: str) -> Path:
        """获取值文件路径"""
        return self.cache_dir / f"{key_hash}.pkl"
    
    def get(self, key: Any, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            key_hash = self._get_key_hash(key)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT value_path FROM cache_entries WHERE key_hash = ?",
                    (key_hash,)
                )
                row = cursor.fetchone()
                
                if row:
                    value_path = Path(row[0])
                    if value_path.exists():
                        try:
                            with open(value_path, 'rb') as f:
                                value = pickle.load(f)
                            
                            # 更新访问时间
                            conn.execute(
                                "UPDATE cache_entries SET accessed_at = ? WHERE key_hash = ?",
                                (time.time(), key_hash)
                            )
                            
                            self.stats.hits += 1
                            return value
                        except Exception as e:
                            logger.error(f"读取缓存文件失败: {e}")
                            # 清理损坏的缓存项
                            self._remove_entry(key_hash)
                
                self.stats.misses += 1
                return default
    
    def put(self, key: Any, value: Any):
        """设置缓存值"""
        with self._lock:
            key_hash = self._get_key_hash(key)
            value_path = self._get_value_path(key_hash)
            
            try:
                # 保存值到文件
                with open(value_path, 'wb') as f:
                    pickle.dump(value, f)
                
                file_size = value_path.stat().st_size
                current_time = time.time()
                
                # 更新数据库
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT OR REPLACE INTO cache_entries 
                        (key_hash, key_data, value_path, created_at, accessed_at, size_bytes)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (key_hash, pickle.dumps(key), str(value_path), 
                          current_time, current_time, file_size))
                
                # 检查缓存大小限制
                self._cleanup_if_needed()
                
            except Exception as e:
                logger.error(f"保存缓存失败: {e}")
    
    def _remove_entry(self, key_hash: str):
        """移除缓存项"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT value_path FROM cache_entries WHERE key_hash = ?",
                (key_hash,)
            )
            row = cursor.fetchone()
            
            if row:
                value_path = Path(row[0])
                if value_path.exists():
                    value_path.unlink()
                
                conn.execute("DELETE FROM cache_entries WHERE key_hash = ?", (key_hash,))
    
    def _cleanup_if_needed(self):
        """如果需要则清理缓存"""
        total_size = self._get_total_size()
        max_size_bytes = self.max_size_mb * 1024 * 1024
        
        if total_size > max_size_bytes:
            # 删除最久未访问的项
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT key_hash FROM cache_entries 
                    ORDER BY accessed_at ASC 
                    LIMIT ?
                """, (max(1, len(self) // 4),))  # 删除25%的项
                
                for row in cursor:
                    self._remove_entry(row[0])
    
    def _get_total_size(self) -> int:
        """获取总缓存大小"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT SUM(size_bytes) FROM cache_entries")
            result = cursor.fetchone()
            return result[0] or 0
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            # 删除所有缓存文件
            for file_path in self.cache_dir.glob("*.pkl"):
                file_path.unlink()
            
            # 清空数据库
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM cache_entries")
            
            self.stats = CacheStats()
    
    def __len__(self) -> int:
        """缓存项数量"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM cache_entries")
            return cursor.fetchone()[0]


class MultiLevelCache:
    """多级缓存"""
    
    def __init__(self, 
                 l1_size: int = 128,
                 l2_cache_dir: Optional[Union[str, Path]] = None,
                 l2_size_mb: float = 100):
        # L1: 内存缓存
        self.l1_cache = LRUCache(maxsize=l1_size)
        
        # L2: 持久化缓存
        self.l2_cache = None
        if l2_cache_dir:
            self.l2_cache = PersistentCache(l2_cache_dir, l2_size_mb)
        
        self._lock = threading.RLock()
    
    def get(self, key: Any, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            # 先尝试L1缓存
            value = self.l1_cache.get(key)
            if value is not None:
                return value
            
            # 再尝试L2缓存
            if self.l2_cache:
                value = self.l2_cache.get(key)
                if value is not None:
                    # 提升到L1缓存
                    self.l1_cache.put(key, value)
                    return value
            
            return default
    
    def put(self, key: Any, value: Any):
        """设置缓存值"""
        with self._lock:
            # 同时存储到L1和L2
            self.l1_cache.put(key, value)
            if self.l2_cache:
                self.l2_cache.put(key, value)
    
    def clear(self):
        """清空所有缓存"""
        with self._lock:
            self.l1_cache.clear()
            if self.l2_cache:
                self.l2_cache.clear()
    
    def get_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取统计信息"""
        stats = {'l1': self.l1_cache.get_stats().to_dict()}
        if self.l2_cache:
            stats['l2'] = self.l2_cache.stats.to_dict()
        return stats


# 全局缓存实例
default_cache = MultiLevelCache()


def cached(maxsize: int = 128, ttl: Optional[float] = None, cache_instance: Optional[Any] = None):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        cache = cache_instance or LRUCache(maxsize, ttl)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = (func.__name__, args, tuple(sorted(kwargs.items())))
            
            # 尝试从缓存获取
            result = cache.get(key)
            if result is not None:
                return result
            
            # 计算结果并缓存
            result = func(*args, **kwargs)
            cache.put(key, result)
            return result
        
        wrapper.cache_info = lambda: cache.get_stats().to_dict()
        wrapper.cache_clear = cache.clear
        return wrapper
    
    return decorator


@contextmanager
def cache_context(cache_dir: Optional[Union[str, Path]] = None):
    """缓存上下文管理器"""
    if cache_dir:
        cache = MultiLevelCache(l2_cache_dir=cache_dir)
    else:
        cache = default_cache
    
    try:
        yield cache
    finally:
        # 可以在这里添加清理逻辑
        pass


def preload_cache(cache: Union[LRUCache, MultiLevelCache], 
                 data_loader: Callable,
                 keys: List[Any]):
    """缓存预热"""
    logger.info(f"开始预热缓存，共 {len(keys)} 个键")
    
    for i, key in enumerate(keys):
        try:
            value = data_loader(key)
            cache.put(key, value)
            
            if (i + 1) % 100 == 0:
                logger.info(f"缓存预热进度: {i + 1}/{len(keys)}")
                
        except Exception as e:
            logger.error(f"预热键 {key} 失败: {e}")
    
    logger.info("缓存预热完成")
