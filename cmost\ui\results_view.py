"""
Results view for the CMOST application.

This module implements the results viewer, providing
an interface for analyzing and visualizing simulation results.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import seaborn as sns
from typing import Dict, List, Any, Optional

from ..core.simulation import Simulation
from ..utils.visualization import plot_comparison_with_benchmarks, set_plotting_style
from ..config.settings import get_setting


class ResultsView(ttk.Frame):
    """
    Results viewer for CMOST simulation results.
    
    This viewer provides tools for analyzing and visualizing
    simulation results, including charts, tables, and export options.
    """
    
    def __init__(self, parent, simulation=None):
        """
        Initialize the results viewer.
        
        Args:
            parent: Parent widget
            simulation: Optional Simulation object with results
        """
        super().__init__(parent, padding=10)
        self.parent = parent
        self.simulation = simulation
        
        # Data structures
        self.results_data = {}
        self.benchmark_data = {}
        self.current_view = "overview"
        
        # Load results if simulation is provided
        if simulation:
            self.load_results_from_simulation(simulation)
        
        # Create UI components
        self.create_ui()
        
        # Pack the main frame
        self.pack(fill=tk.BOTH, expand=True)
        
        # Initial view
        self.show_overview()
    
    def create_ui(self):
        """Create the user interface components."""
        # Main layout: sidebar and content area
        self.main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create sidebar
        self.create_sidebar()
        
        # Create content area
        self.content_frame = ttk.Frame(self.main_paned, padding=5)
        self.main_paned.add(self.content_frame, weight=3)
        
        # Create toolbar
        self.create_toolbar()
    
    def create_sidebar(self):
        """Create the sidebar with navigation options."""
        sidebar_frame = ttk.Frame(self.main_paned, padding=5)
        self.main_paned.add(sidebar_frame, weight=1)
        
        # Navigation tree
        ttk.Label(sidebar_frame, text="Results Navigation").pack(anchor=tk.W, pady=(0, 5))
        
        self.nav_tree = ttk.Treeview(sidebar_frame, show="tree", selectmode="browse", height=20)
        self.nav_tree.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Add navigation items
        overview_id = self.nav_tree.insert("", "end", text="Overview", tags=("nav",))
        
        # Demographics section
        demographics_id = self.nav_tree.insert("", "end", text="Demographics", tags=("nav",))
        self.nav_tree.insert(demographics_id, "end", text="Age Distribution", tags=("nav",))
        self.nav_tree.insert(demographics_id, "end", text="Gender Distribution", tags=("nav",))
        self.nav_tree.insert(demographics_id, "end", text="Risk Factors", tags=("nav",))
        
        # Clinical outcomes section
        outcomes_id = self.nav_tree.insert("", "end", text="Clinical Outcomes", tags=("nav",))
        self.nav_tree.insert(outcomes_id, "end", text="Polyp Prevalence", tags=("nav",))
        self.nav_tree.insert(outcomes_id, "end", text="Cancer Incidence", tags=("nav",))
        self.nav_tree.insert(outcomes_id, "end", text="Cancer Mortality", tags=("nav",))
        self.nav_tree.insert(outcomes_id, "end", text="Life Years Lost", tags=("nav",))
        
        # Economic outcomes section
        economics_id = self.nav_tree.insert("", "end", text="Economic Outcomes", tags=("nav",))
        self.nav_tree.insert(economics_id, "end", text="Screening Costs", tags=("nav",))
        self.nav_tree.insert(economics_id, "end", text="Treatment Costs", tags=("nav",))
        self.nav_tree.insert(economics_id, "end", text="Total Costs", tags=("nav",))
        self.nav_tree.insert(economics_id, "end", text="Cost-Effectiveness", tags=("nav",))
        
        # Screening section
        screening_id = self.nav_tree.insert("", "end", text="Screening", tags=("nav",))
        self.nav_tree.insert(screening_id, "end", text="Screening Adherence", tags=("nav",))
        self.nav_tree.insert(screening_id, "end", text="Test Performance", tags=("nav",))
        self.nav_tree.insert(screening_id, "end", text="Colonoscopy Findings", tags=("nav",))
        
        # Raw data section
        data_id = self.nav_tree.insert("", "end", text="Raw Data", tags=("nav",))
        self.nav_tree.insert(data_id, "end", text="Summary Variables", tags=("nav",))
        self.nav_tree.insert(data_id, "end", text="Yearly Data", tags=("nav",))
        self.nav_tree.insert(data_id, "end", text="Benchmarks", tags=("nav",))
        
        # Bind selection event
        self.nav_tree.bind("<<TreeviewSelect>>", self.on_nav_select)
        
        # Expand all items initially
        for item_id in [overview_id, demographics_id, outcomes_id, economics_id, screening_id, data_id]:
            self.nav_tree.item(item_id, open=True)
        
        # Select overview by default
        self.nav_tree.selection_set(overview_id)
    
    def create_toolbar(self):
        """Create the toolbar with action buttons."""
        toolbar_frame = ttk.Frame(self)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Load results button
        load_btn = ttk.Button(toolbar_frame, text="Load Results", command=self.load_results)
        load_btn.pack(side=tk.LEFT, padx=2)
        
        # Export options
        export_menu_btn = ttk.Menubutton(toolbar_frame, text="Export")
        export_menu_btn.pack(side=tk.LEFT, padx=2)
        
        export_menu = tk.Menu(export_menu_btn, tearoff=0)
        export_menu_btn["menu"] = export_menu
        
        export_menu.add_command(label="Export to Excel", command=self.export_to_excel)
        export_menu.add_command(label="Export to CSV", command=self.export_to_csv)
        export_menu.add_command(label="Export Current Chart", command=self.export_chart)
        export_menu.add_command(label="Generate Report", command=self.generate_report)
        
        # Compare button
        compare_btn = ttk.Button(toolbar_frame, text="Compare with Benchmark", command=self.compare_with_benchmark)
        compare_btn.pack(side=tk.LEFT, padx=2)
        
        # Settings button
        settings_btn = ttk.Button(toolbar_frame, text="Display Settings", command=self.show_display_settings)
        settings_btn.pack(side=tk.LEFT, padx=2)
        
        # Help button
        help_btn = ttk.Button(toolbar_frame, text="Help", command=self.show_help)
        help_btn.pack(side=tk.RIGHT, padx=2)
    
    def on_nav_select(self, event):
        """Handle navigation tree selection."""
        selected_id = self.nav_tree.selection()[0]
        selected_text = self.nav_tree.item(selected_id, "text")
        
        # Clear current content
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # Show selected view
        if selected_text == "Overview":
            self.show_overview()
        elif selected_text == "Age Distribution":
            self.show_age_distribution()
        elif selected_text == "Gender Distribution":
            self.show_gender_distribution()
        elif selected_text == "Risk Factors":
            self.show_risk_factors()
        elif selected_text == "Polyp Prevalence":
            self.show_polyp_prevalence()
        elif selected_text == "Cancer Incidence":
            self.show_cancer_incidence()
        elif selected_text == "Cancer Mortality":
            self.show_cancer_mortality()
        elif selected_text == "Life Years Lost":
            self.show_life_years_lost()
        elif selected_text == "Screening Costs":
            self.show_screening_costs()
        elif selected_text == "Treatment Costs":
            self.show_treatment_costs()
        elif selected_text == "Total Costs":
            self.show_total_costs()
        elif selected_text == "Cost-Effectiveness":
            self.show_cost_effectiveness()
        elif selected_text == "Screening Adherence":
            self.show_screening_adherence()
        elif selected_text == "Test Performance":
            self.show_test_performance()
        elif selected_text == "Colonoscopy Findings":
            self.show_colonoscopy_findings()
        elif selected_text == "Summary Variables":
            self.show_summary_variables()
        elif selected_text == "Yearly Data":
            self.show_yearly_data()
        elif selected_text == "Benchmarks":
            self.show_benchmarks()
        else:
            self.show_not_implemented(selected_text)
    
    def show_overview(self):
        """Show the overview page with key results."""
        self.current_view = "overview"
        
        # Create overview frame
        overview_frame = ttk.Frame(self.content_frame)
        overview_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(overview_frame, text="Simulation Results Overview", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Key metrics
        metrics_frame = ttk.LabelFrame(overview_frame, text="Key Metrics", padding=10)
        metrics_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Create a grid of key metrics
        metrics = [
            ("Total Patients", self.get_result_value("NumberPatients", "N/A")),
            ("Cancer Cases", self.get_result_value("Variable", 37, "N/A")),
            ("Cancer Deaths", self.get_result_value("Variable", 13, "N/A")),
            ("Life Years Lost", self.get_result_value("SumDiscYears", "N/A")),
            ("Total Costs", f"${self.get_result_value('SumDiscCosts', 'N/A'):,}" if isinstance(self.get_result_value('SumDiscCosts', 'N/A'), (int, float)) else "N/A"),
            ("Screening Costs", f"${self.get_result_value('Screening', 'sum', 'N/A'):,}" if isinstance(self.get_result_value('Screening', 'sum', 'N/A'), (int, float)) else "N/A"),
        ]
        
        for i, (label, value) in enumerate(metrics):
            row, col = divmod(i, 3)
            ttk.Label(metrics_frame, text=label + ":", font=("Arial", 10, "bold")).grid(row=row, column=col*2, sticky=tk.W, padx=10, pady=5)
            ttk.Label(metrics_frame, text=str(value), font=("Arial", 10)).grid(row=row, column=col*2+1, sticky=tk.W, padx=10, pady=5)
        
        # Charts section
        charts_frame = ttk.Frame(overview_frame)
        charts_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)
        
        # Create two charts side by side
        left_chart_frame = ttk.LabelFrame(charts_frame, text="Cancer Incidence by Age", padding=10)
        left_chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        right_chart_frame = ttk.LabelFrame(charts_frame, text="Costs Breakdown", padding=10)
        right_chart_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # Create the charts
        self.create_incidence_chart(left_chart_frame)
        self.create_costs_chart(right_chart_frame)
        
        # Benchmark comparison
        benchmark_frame = ttk.LabelFrame(overview_frame, text="Benchmark Comparison", padding=10)
        benchmark_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Create a simple table for benchmark comparison
        benchmark_tree = ttk.Treeview(benchmark_frame, columns=("metric", "value", "benchmark", "diff"), show="headings", height=5)
        benchmark_tree.pack(fill=tk.X, expand=True)
        
        benchmark_tree.heading("metric", text="Metric")
        benchmark_tree.heading("value", text="Simulation Value")
        benchmark_tree.heading("benchmark", text="Benchmark")
        benchmark_tree.heading("diff", text="Difference (%)")
        
        benchmark_tree.column("metric", width=200)
        benchmark_tree.column("value", width=150, anchor=tk.E)
        benchmark_tree.column("benchmark", width=150, anchor=tk.E)
        benchmark_tree.column("diff", width=150, anchor=tk.E)
        
        # Add some sample benchmark comparisons
        benchmarks = [
            ("Cancer Incidence (per 100,000)", 50.2, 48.5, 3.5),
            ("Cancer Mortality (per 100,000)", 18.7, 19.2, -2.6),
            ("Advanced Adenoma Prevalence (%)", 7.5, 7.2, 4.2),
            ("Early Adenoma Prevalence (%)", 15.3, 14.8, 3.4),
            ("Cost per Life Year Saved ($)", 24500, 25000, -2.0)
        ]
        
        for metric, value, benchmark, diff in benchmarks:
            benchmark_tree.insert("", "end", values=(metric, f"{value:,.1f}", f"{benchmark:,.1f}", f"{diff:+.1f}%"))
    
    def create_incidence_chart(self, parent_frame):
        """Create a chart showing cancer incidence by age."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(5, 4), dpi=100)
        
        # Sample data - replace with actual data in real implementation
        age_groups = ['40-49', '50-59', '60-69', '70-79', '80+']
        incidence = [12.5, 35.8, 65.2, 75.3, 68.1]
        
        # Create bar chart
        ax.bar(age_groups, incidence, color='#3498db')
        ax.set_xlabel('Age Group')
        ax.set_ylabel('Incidence per 100,000')
        ax.set_title('Cancer Incidence by Age Group')
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Add the plot to the frame
        canvas = FigureCanvasTkAgg(fig, master=parent_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def create_costs_chart(self, parent_frame):
        """Create a chart showing cost breakdown."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(5, 4), dpi=100)
        
        # Sample data - replace with actual data in real implementation
        cost_categories = ['Screening', 'Treatment', 'Follow-up', 'Other']
        costs = [35, 45, 15, 5]
        colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12']
        
        # Create pie chart
        wedges, texts, autotexts = ax.pie(
            costs, 
            labels=cost_categories, 
            colors=colors,
            autopct='%1.1f%%', 
            startangle=90,
            wedgeprops={'edgecolor': 'w', 'linewidth': 1}
        )
        
        # Equal aspect ratio ensures that pie is drawn as a circle
        ax.axis('equal')
        ax.set_title('Cost Breakdown')
        
        # Add the plot to the frame
        canvas = FigureCanvasTkAgg(fig, master=parent_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_age_distribution(self):
        """Show age distribution of the simulated population."""
        self.current_view = "age_distribution"
        self.create_chart_view(
            "Age Distribution of Simulated Population",
            "Age distribution of patients in the simulation.",
            self.create_age_distribution_chart
        )
    
    def create_age_distribution_chart(self, frame):
        """Create age distribution chart."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 5), dpi=100)
        
        # Sample data - replace with actual data in real implementation
        ages = np.arange(20, 90, 5)
        distribution = np.array([0.02, 0.05, 0.08, 0.1, 0.12, 0.15, 0.14, 0.12, 0.09, 0.07, 0.04, 0.02, 0.01, 0.01])
        distribution = distribution / distribution.sum() * 100  # Convert to percentage
        
        # Create bar chart
        ax.bar(ages, distribution, width=4, color='#3498db', alpha=0.7)
        ax.set_xlabel('Age')
        ax.set_ylabel('Percentage (%)')
        ax.set_title('Age Distribution')
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Add the plot to the frame
        canvas = FigureCanvasTkAgg(fig, master=frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add navigation toolbar
        toolbar = NavigationToolbar2Tk(canvas, frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_gender_distribution(self):
        """Show gender distribution of the simulated population."""
        self.current_view = "gender_distribution"
        self.create_chart_view(
            "Gender Distribution of Simulated Population",
            "Gender distribution of patients in the simulation.",
            self.create_gender_distribution_chart
        )
    
    def create_gender_distribution_chart(self, frame):
        """Create gender distribution chart."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 5), dpi=100)
        
        # Sample data - replace with actual data in real implementation
        genders = ['Male', 'Female']
        distribution = [48, 52]  # Percentages
        
        # Create pie chart
        ax.pie(
            distribution, 
            labels=genders, 
            colors=['#3498db', '#e74c3c'],
            autopct='%1.1f%%', 
            startangle=90,
            wedgeprops={'edgecolor': 'w', 'linewidth': 1},
            textprops={'fontsize': 14}
        )
        
        # Equal aspect ratio ensures that pie is drawn as a circle
        ax.axis('equal')
        ax.set_title('Gender Distribution', fontsize=16)
        
        # Add the plot to the frame
        canvas = FigureCanvasTkAgg(fig, master=frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add navigation toolbar
        toolbar = NavigationToolbar2Tk(canvas, frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_not_implemented(self, view_name):
        """Show a placeholder for views not yet implemented."""
        self.current_view = "not_implemented"
        
        frame = ttk.Frame(self.content_frame)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        ttk.Label(
            frame, 
            text=f"The '{view_name}' view is not yet implemented.",
            font=("Arial", 14)
        ).pack(expand=True)
        
        ttk.Label(
            frame, 
            text="This feature will be available in a future update.",
            font=("Arial", 12)
        ).pack(expand=True)
    
    def create_chart_view(self, title, description, chart_function):
        """Create a standard chart view with title, description, and chart."""
        # Create main frame
        frame = ttk.Frame(self.content_frame)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Title and description
        ttk.Label(frame, text=title, font=("Arial", 16, "bold")).pack(pady=(10, 5))
        ttk.Label(frame, text=description, font=("Arial", 10)).pack(pady=(0, 10))
        
        # Chart frame
        chart_frame = ttk.Frame(frame)
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create the chart
        chart_function(chart_frame)
    
    def load_results(self):
        """Load results from a file."""
        file_path = filedialog.askopenfilename(
            title="Load Simulation Results",
            filetypes=[("MATLAB Files", "*.mat"), ("All Files", "*.*")],
            initialdir=get_setting("ResultsPath", "./")
        )
        
        if not file_path:
            return
        
        try:
            # This is a placeholder - actual implementation would depend on file format
            # In a real implementation, you would load the .mat file using scipy.io.loadmat
            # or another appropriate library
            
            messagebox.showinfo("Success", "Results loaded successfully.")
            
            # Refresh the current view
            self.on_nav_select(None)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load results: {str(e)}")
    
    def load_results_from_simulation(self, simulation):
        """Load results directly from a simulation object."""
        try:
            # This is a placeholder - actual implementation would depend on Simulation class
            self.results_data = simulation.get_results()
            self.benchmark_data = simulation.get_benchmarks()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load simulation results: {str(e)}")
    
    def export_to_excel(self):
        """Export results to Excel file."""
        file_path = filedialog.asksaveasfilename(
            title="Export to Excel",
            defaultextension=".xlsx",
            filetypes=[("Excel Files", "*.xlsx"), ("All Files", "*.*")],
            initialdir=get_setting("ResultsPath", "./")
        )
        
        if not file_path:
            return
        
        try:
            # This is a placeholder - actual implementation would depend on data structure
            # In a real implementation, you would create a pandas DataFrame and export to Excel
            
            messagebox.showinfo("Success", f"Results exported to {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export to Excel: {str(e)}")
    
    def export_to_csv(self):
        """Export results to CSV file."""
        file_path = filedialog.asksaveasfilename(
            title="Export to CSV",
            defaultextension=".csv",
            filetypes=[("CSV Files", "*.csv"), ("All Files", "*.*")],
            initialdir=get_setting("ResultsPath", "./")
        )
        
        if not file_path:
            return
        
        try:
            # This is a placeholder - actual implementation would depend on data structure
            # In a real implementation, you would create a pandas DataFrame and export to CSV
            
            messagebox.showinfo("Success", f"Results exported to {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export to CSV: {str(e)}")
    
    def export_chart(self):
        """Export the current chart to an image file."""
        file_path = filedialog.asksaveasfilename(
            title="Export Chart",
            defaultextension=".png",
            filetypes=[
                ("PNG Files", "*.png"), 
                ("JPEG Files", "*.jpg"), 
                ("PDF Files", "*.pdf"),
                ("SVG Files", "*.svg"),
                ("All Files", "*.*")
            ],
            initialdir=get_setting("ResultsPath", "./")
        )
        
        if not file_path:
            return
        
        try:
            # This is a placeholder - actual implementation would depend on the current chart
            # In a real implementation, you would get the current matplotlib figure and save it
            
            messagebox.showinfo("Success", f"Chart exported to {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export chart: {str(e)}")
    
    def generate_report(self):
        """Generate a comprehensive report of the results."""
        file_path = filedialog.asksaveasfilename(
            title="Generate Report",
            defaultextension=".pdf",
            filetypes=[("PDF Files", "*.pdf"), ("HTML Files", "*.html"), ("All Files", "*.*")],
            initialdir=get_setting("ResultsPath", "./")
        )
        
        if not file_path:
            return
        
        try:
            # This is a placeholder - actual implementation would depend on reporting library
            # In a real implementation, you would generate a report using a library like ReportLab
            
            messagebox.showinfo("Success", f"Report generated at {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")
    
    def compare_with_benchmark(self):
        """Compare current results with benchmark data."""
        # This is a placeholder - actual implementation would depend on data structure
        messagebox.showinfo("Information", "Benchmark comparison not implemented yet.")
    
    def show_display_settings(self):
        """Show display settings dialog."""
        # Create a toplevel window for settings
        settings_window = tk.Toplevel(self.parent)
        settings_window.title("Display Settings")
        settings_window.geometry("400x300")
        settings_window.transient(self.parent)
        settings_window.grab_set()
        
        # Create settings UI
        settings_frame = ttk.Frame(settings_window, padding=10)
        settings_frame.pack(fill=tk.BOTH, expand=True)
        
        # Chart type
        ttk.Label(settings_frame, text="Default Chart Type:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        chart_type = ttk.Combobox(
            settings_frame, 
            values=["Bar Chart", "Line Chart", "Pie Chart", "Scatter Plot"],
            width=20,
            state="readonly"
        )
        chart_type.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        chart_type.current(0)
        
        # Color scheme
        ttk.Label(settings_frame, text="Color Scheme:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        color_scheme = ttk.Combobox(
            settings_frame, 
            values=["Default", "Colorblind Friendly", "Pastel", "Dark", "Bright"],
            width=20,
            state="readonly"
        )
        color_scheme.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        color_scheme.current(0)
        
        # Show grid lines
        show_grid = tk.BooleanVar(value=True)
        grid_check = ttk.Checkbutton(
            settings_frame,
            text="Show Grid Lines",
            variable=show_grid
        )
        grid_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Show data labels
        show_labels = tk.BooleanVar(value=True)
        labels_check = ttk.Checkbutton(
            settings_frame,
            text="Show Data Labels",
            variable=show_labels
        )
        labels_check.grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Show legend
        show_legend = tk.BooleanVar(value=True)
        legend_check = ttk.Checkbutton(
            settings_frame,
            text="Show Legend",
            variable=show_legend
        )
        legend_check.grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(settings_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="Apply", command=settings_window.destroy).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=settings_window.destroy).pack(side=tk.LEFT, padx=5)
    
    def show_help(self):
        """Show help information."""
        help_window = tk.Toplevel(self.parent)
        help_window.title("Results Viewer Help")
        help_window.geometry("500x400")
        help_window.transient(self.parent)
        
        help_frame = ttk.Frame(help_window, padding=10)
        help_frame.pack(fill=tk.BOTH, expand=True)
        
        # Help content
        ttk.Label(help_frame, text="Results Viewer Help", font=("Arial", 16, "bold")).pack(pady=(0, 10))
        
        help_text = tk.Text(help_frame, wrap=tk.WORD, height=15)
        help_text.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Add scrollbar to text widget
        scrollbar = ttk.Scrollbar(help_frame, orient="vertical", command=help_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        help_text.configure(yscrollcommand=scrollbar.set)
        
        # Help content
        help_content = """
Results Viewer Help

Navigation:
- Use the tree view on the left to navigate between different result views.
- The Overview page shows a summary of key results.
- Detailed results are organized by category.

Data Manipulation:
- Load Results: Load simulation results from a file.
- Export: Export results to Excel, CSV, or as images.
- Compare with Benchmark: Compare your results with benchmark data.

Charts:
- Click and drag to pan the chart.
- Use the mouse wheel to zoom in and out.
- Right-click for additional options.
- Use the toolbar below charts for saving, zooming, and other functions.

For more information, please refer to the CMOST documentation.
"""
        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)  # Make read-only
        
        # Close button
        ttk.Button(help_frame, text="Close", command=help_window.destroy).pack(pady=10)
    
    def get_result_value(self, key, subkey=None, default=None):
        """
        Get a value from the results data.
        
        Args:
            key: Main key in results dictionary
            subkey: Optional subkey or index
            default: Default value if key not found
            
        Returns:
            The requested value or default if not found
        """
        try:
            if key not in self.results_data:
                return default
            
            if subkey is None:
                return self.results_data[key]
            
            if isinstance(subkey, (int, str)):
                return self.results_data[key][subkey]
            
            if subkey == 'sum' and isinstance(self.results_data[key], (list, np.ndarray)):
                return sum(self.results_data[key])
            
            return default
        except (KeyError, IndexError, TypeError):
            return default
    
    def show_polyp_prevalence(self):
        """Show polyp prevalence data."""
        self.current_view = "polyp_prevalence"
        self.create_chart_view(
            "Polyp Prevalence by Age and Type",
            "Prevalence of early and advanced adenomas by age group.",
            self.create_polyp_prevalence_chart
        )
    
    def create_polyp_prevalence_chart(self, frame):
        """Create polyp prevalence chart."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 5), dpi=100)
        
        # Sample data - replace with actual data in real implementation
        age_groups = ['40-49', '50-59', '60-69', '70-79', '80+']
        early_adenoma = [8.2, 15.3, 21.5, 25.7, 27.1]
        advanced_adenoma = [2.1, 5.7, 9.3, 12.5, 14.2]
        
        x = np.arange(len(age_groups))
        width = 0.35
        
        # Create grouped bar chart
        rects1 = ax.bar(x - width/2, early_adenoma, width, label='Early Adenoma', color='#3498db')
        rects2 = ax.bar(x + width/2, advanced_adenoma, width, label='Advanced Adenoma', color='#e74c3c')
        
        # Add labels and legend
        ax.set_xlabel('Age Group')
        ax.set_ylabel('Prevalence (%)')
        ax.set_title('Polyp Prevalence by Age and Type')
        ax.set_xticks(x)
        ax.set_xticklabels(age_groups)
        ax.legend()
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Add data labels
        def add_labels(rects):
            for rect in rects:
                height = rect.get_height()
                ax.annotate(f'{height:.1f}%',
                            xy=(rect.get_x() + rect.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
        
        add_labels(rects1)
        add_labels(rects2)
        
        # Add the plot to the frame
        canvas = FigureCanvasTkAgg(fig, master=frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add navigation toolbar
        toolbar = NavigationToolbar2Tk(canvas, frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_cancer_incidence(self):
        """Show cancer incidence data."""
        self.current_view = "cancer_incidence"
        self.create_chart_view(
            "Cancer Incidence by Age and Stage",
            "Incidence of colorectal cancer by age group and stage at diagnosis.",
            self.create_cancer_incidence_chart
        )
    
    def create_cancer_incidence_chart(self, frame):
        """Create cancer incidence chart."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 5), dpi=100)
        
        # Sample data - replace with actual data in real implementation
        age_groups = ['40-49', '50-59', '60-69', '70-79', '80+']
        stage_i = [2.1, 5.3, 9.7, 12.1, 10.5]
        stage_ii = [1.8, 4.7, 8.5, 10.8, 9.3]
        stage_iii = [3.2, 7.9, 14.2, 17.5, 15.2]
        stage_iv = [5.4, 17.9, 32.8, 34.9, 33.1]
        
        # Create stacked bar chart
        ax.bar(age_groups, stage_i, label='Stage I', color='#2ecc71')
        ax.bar(age_groups, stage_ii, bottom=stage_i, label='Stage II', color='#3498db')
        ax.bar(age_groups, stage_iii, bottom=np.array(stage_i) + np.array(stage_ii), label='Stage III', color='#f39c12')
        ax.bar(age_groups, stage_iv, bottom=np.array(stage_i) + np.array(stage_ii) + np.array(stage_iii), label='Stage IV', color='#e74c3c')
        
        # Add labels and legend
        ax.set_xlabel('Age Group')
        ax.set_ylabel('Incidence per 100,000')
        ax.set_title('Cancer Incidence by Age and Stage')
        ax.legend(loc='upper left')
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Add the plot to the frame
        canvas = FigureCanvasTkAgg(fig, master=frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add navigation toolbar
        toolbar = NavigationToolbar2Tk(canvas, frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_cancer_mortality(self):
        """Show cancer mortality data."""
        self.current_view = "cancer_mortality"
        self.create_chart_view(
            "Cancer Mortality by Age and Gender",
            "Mortality rates from colorectal cancer by age group and gender.",
            self.create_cancer_mortality_chart
        )
    
    def create_cancer_mortality_chart(self, frame):
        """Create cancer mortality chart."""
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 5), dpi=100)
        
        # Sample data - replace with actual data in real implementation
        age_groups = ['40-49', '50-59', '60-69', '70-79', '80+']
        male_mortality = [3.2, 12.5, 28.7, 42.3, 58.1]
        female_mortality = [2.8, 10.2, 22.5, 35.7, 49.3]
        
        x = np.arange(len(age_groups))
        width = 0.35
        
        # Create grouped bar chart
        rects1 = ax.bar(x - width/2, male_mortality, width, label='Male', color='#3498db')
        rects2 = ax.bar(x + width/2, female_mortality, width, label='Female', color='#e74c3c')
        
        # Add labels and legend
        ax.set_xlabel('Age Group')
        ax.set_ylabel('Mortality per 100,000')
        ax.set_title('Cancer Mortality by Age and Gender')
        ax.set_xticks(x)
        ax.set_xticklabels(age_groups)
        ax.legend()
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Add the plot to the frame
        canvas = FigureCanvasTkAgg(fig, master=frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add navigation toolbar
        toolbar = NavigationToolbar2Tk(canvas, frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_summary_variables(self):
        """Show summary variables in a table."""
        self.current_view = "summary_variables"
        
        # Create main frame
        frame = ttk.Frame(self.content_frame)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Title and description
        ttk.Label(frame, text="Summary Variables", font=("Arial", 16, "bold")).pack(pady=(10, 5))
        ttk.Label(
            frame, 
            text="Key summary variables from the simulation results.",
            font=("Arial", 10)
        ).pack(pady=(0, 10))
        
        # Create table
        table_frame = ttk.Frame(frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create treeview for table
        columns = ("variable", "value", "description")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Configure columns
        tree.heading("variable", text="Variable")
        tree.heading("value", text="Value")
        tree.heading("description", text="Description")
        
        tree.column("variable", width=150)
        tree.column("value", width=100, anchor=tk.E)
        tree.column("description", width=400)
        
        # Sample data - replace with actual data in real implementation
        variables = [
            ("NumberPatients", "100,000", "Total number of patients in the simulation"),
            ("SumDiscYears", "1,523.4", "Sum of discounted life years lost"),
            ("SumDiscCosts", "$2,345,678", "Sum of discounted costs"),
            ("Variable_13", "487", "Number of cancer deaths"),
            ("Variable_37", "1,245", "Number of cancer cases"),
            ("Variable_42", "3,567", "Number of advanced adenomas"),
            ("Variable_43", "12,345", "Number of early adenomas"),
            ("Variable_50", "8,765", "Number of colonoscopies"),
            ("Variable_51", "4,321", "Number of polypectomies"),
            ("Variable_60", "$1,234,567", "Screening costs"),
            ("Variable_61", "$987,654", "Treatment costs"),
            ("Variable_70", "78.5", "Average life expectancy (years)"),
            ("ICER", "$24,567", "Incremental cost-effectiveness ratio"),
            ("NNS", "217", "Number needed to screen"),
            ("NNT", "43", "Number needed to treat")
        ]
        
        # Add data to table
        for var, val, desc in variables:
            tree.insert("", "end", values=(var, val, desc))
    
    def show_yearly_data(self):
        """Show yearly data in a table and chart."""
        self.current_view = "yearly_data"
        
        # Create main frame
        frame = ttk.Frame(self.content_frame)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Title and description
        ttk.Label(frame, text="Yearly Data", font=("Arial", 16, "bold")).pack(pady=(10, 5))
        ttk.Label(
            frame, 
            text="Yearly outcomes from the simulation.",
            font=("Arial", 10)
        ).pack(pady=(0, 10))
        
        # Create paned window for table and chart
        paned = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Table frame
        table_frame = ttk.Frame(paned)
        paned.add(table_frame, weight=1)
        
        # Chart frame
        chart_frame = ttk.Frame(paned)
        paned.add(chart_frame, weight=2)
        
        # Create treeview for table
        columns = ("year", "incidence", "mortality", "costs")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Configure columns
        tree.heading("year", text="Year")
        tree.heading("incidence", text="Incidence")
        tree.heading("mortality", text="Mortality")
        tree.heading("costs", text="Costs ($)")
        
        tree.column("year", width=50, anchor=tk.CENTER)
        tree.column("incidence", width=80, anchor=tk.E)
        tree.column("mortality", width=80, anchor=tk.E)
        tree.column("costs", width=100, anchor=tk.E)
        
        # Sample data - replace with actual data in real implementation
        yearly_data = []
        for year in range(1, 31):
            # Generate some sample data with trends
            incidence = 50 + year * 2 + np.random.normal(0, 5)
            mortality = 20 + year * 0.8 + np.random.normal(0, 3)
            costs = 100000 + year * 5000 + np.random.normal(0, 10000)
            
            yearly_data.append((year, incidence, mortality, costs))
            tree.insert("", "end", values=(
                year, 
                f"{incidence:.1f}", 
                f"{mortality:.1f}", 
                f"{costs:,.0f}"
            ))
        
        # Create chart
        fig, ax = plt.subplots(figsize=(8, 5), dpi=100)
        
        # Extract data for plotting
        years = [data[0] for data in yearly_data]
        incidence = [data[1] for data in yearly_data]
        mortality = [data[2] for data in yearly_data]
        
        # Create line chart
        ax.plot(years, incidence, 'o-', label='Incidence', color='#3498db')
        ax.plot(years, mortality, 'o-', label='Mortality', color='#e74c3c')
        
        # Add labels and legend
        ax.set_xlabel('Year')
        ax.set_ylabel('Rate per 100,000')
        ax.set_title('Cancer Incidence and Mortality Over Time')
        ax.legend()
        ax.grid(linestyle='--', alpha=0.7)
        
        # Add the plot to the frame
        canvas = FigureCanvasTkAgg(fig, master=chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add navigation toolbar
        toolbar = NavigationToolbar2Tk(canvas, chart_frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add chart selection
        chart_select_frame = ttk.Frame(chart_frame)
        chart_select_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(chart_select_frame, text="Chart Type:").pack(side=tk.LEFT, padx=5)
        chart_type = ttk.Combobox(
            chart_select_frame, 
            values=["Incidence & Mortality", "Costs", "Screenings", "All Variables"],
            width=20,
            state="readonly"
        )
        chart_type.pack(side=tk.LEFT, padx=5)
        chart_type.current(0)
