# CMOST 性能优化指南

## 概述

CMOST项目集成了全面的性能优化功能，包括缓存管理、内存优化、并行处理和性能监控等。本文档详细介绍如何使用这些功能来提升仿真性能。

## 核心优化模块

### 1. 性能监控 (`cmost.utils.performance`)

#### 基本使用

```python
from cmost.utils.performance import performance_monitor, profile

# 使用装饰器监控函数性能
@profile("my_function")
def my_simulation_function():
    # 仿真代码
    pass

# 使用上下文管理器监控代码块
with performance_monitor.monitor("simulation_block"):
    # 仿真代码
    pass

# 获取性能指标
metrics = performance_monitor.get_metrics("my_function")
print(f"执行时间: {metrics.execution_time:.2f}s")
print(f"内存使用: {metrics.memory_usage:.2f}MB")
```

#### 性能指标

- `execution_time`: 执行时间（秒）
- `memory_usage`: 内存使用量（MB）
- `cpu_usage`: CPU使用率（%）
- `cache_hits/misses`: 缓存命中/未命中次数
- `function_calls`: 函数调用次数
- `peak_memory`: 峰值内存使用量

### 2. 并行处理 (`cmost.utils.parallel`)

#### 配置并行处理

```python
from cmost.utils.parallel import ParallelConfig, ParallelProcessor

# 创建并行配置
config = ParallelConfig(
    max_workers=8,          # 最大工作进程数
    use_processes=True,     # 使用进程而非线程
    chunk_size=100,         # 数据块大小
    timeout=3600           # 超时时间（秒）
)

# 创建并行处理器
processor = ParallelProcessor(config)
```

#### 并行映射

```python
from cmost.utils.parallel import parallel_map

# 简单并行映射
def process_patient(patient):
    # 处理单个患者
    return patient.simulate_year()

results = parallel_map(process_patient, patients, max_workers=4)
```

#### 仿真并行化

```python
from cmost.utils.parallel import parallel_simulation

# 并行运行多个仿真参数组合
parameter_sets = [
    {'num_patients': 10000, 'screening_strategy': 'colonoscopy'},
    {'num_patients': 10000, 'screening_strategy': 'fit'},
    {'num_patients': 10000, 'screening_strategy': 'sigmoidoscopy'}
]

results = parallel_simulation(
    simulation_func=run_single_simulation,
    parameter_sets=parameter_sets,
    max_workers=3
)
```

### 3. 内存管理 (`cmost.utils.memory`)

#### 对象池使用

```python
from cmost.utils.memory import memory_manager, managed_object

# 创建对象池
memory_manager.create_pool(
    'patient_pool',
    factory=lambda: Patient(),
    max_size=1000,
    initial_size=100
)

# 使用托管对象
with managed_object('patient_pool') as patient:
    # 使用患者对象
    patient.simulate()
```

#### 数组池使用

```python
from cmost.utils.memory import managed_array
import numpy as np

# 使用托管数组
with managed_array((1000, 10), dtype=np.float64) as arr:
    # 使用数组进行计算
    arr[:] = np.random.random((1000, 10))
    result = np.sum(arr)
```

#### 内存监控

```python
from cmost.utils.memory import memory_monitor

# 监控内存使用
with memory_monitor(threshold_mb=500):
    # 如果内存使用超过500MB会触发警告和垃圾回收
    large_simulation()
```

### 4. 缓存系统 (`cmost.utils.cache`)

#### LRU缓存

```python
from cmost.utils.cache import cached

# 缓存函数结果
@cached(maxsize=1000, ttl=3600)  # 缓存1000项，1小时过期
def expensive_calculation(param1, param2):
    # 耗时计算
    return complex_result

# 查看缓存统计
print(expensive_calculation.cache_info())
```

#### 多级缓存

```python
from cmost.utils.cache import MultiLevelCache

# 创建多级缓存
cache = MultiLevelCache(
    l1_size=128,                    # L1内存缓存大小
    l2_cache_dir='./cache',         # L2持久化缓存目录
    l2_size_mb=100                  # L2缓存最大大小
)

# 使用缓存
cache.put('key', expensive_result)
result = cache.get('key')
```

#### 缓存预热

```python
from cmost.utils.cache import preload_cache

def data_loader(key):
    # 加载数据的函数
    return load_data_for_key(key)

# 预热缓存
keys_to_preload = ['key1', 'key2', 'key3']
preload_cache(cache, data_loader, keys_to_preload)
```

## 仿真性能优化配置

### 1. 基本配置

在仿真设置中启用性能优化：

```python
from cmost.config.settings import Settings

settings = Settings()
settings.set('Simulation.EnableMultiprocessing', True)
settings.set('Simulation.NumProcesses', 8)  # 0表示使用所有可用核心
```

### 2. 内存优化配置

```python
# 启用内存优化
settings.set('Performance.EnableMemoryOptimization', True)
settings.set('Performance.MemoryThresholdMB', 1000)
settings.set('Performance.GCInterval', 100)  # 每100次迭代触发垃圾回收
```

### 3. 缓存配置

```python
# 配置缓存
settings.set('Performance.EnableCaching', True)
settings.set('Performance.CacheSize', 1000)
settings.set('Performance.CacheTTL', 3600)  # 缓存生存时间（秒）
settings.set('Performance.PersistentCacheDir', './cache')
```

## 最佳实践

### 1. 大规模仿真优化

对于大规模仿真（>100,000患者）：

```python
# 启用所有优化功能
settings.set('Simulation.EnableMultiprocessing', True)
settings.set('Simulation.NumProcesses', 0)  # 使用所有核心
settings.set('Performance.EnableMemoryOptimization', True)
settings.set('Performance.EnableCaching', True)

# 调整批处理大小
settings.set('Performance.PatientBatchSize', 1000)
settings.set('Performance.MemoryCleanupInterval', 10)
```

### 2. 内存受限环境

对于内存受限的环境：

```python
# 减少内存使用
settings.set('Performance.MemoryThresholdMB', 500)
settings.set('Performance.ObjectPoolSize', 100)
settings.set('Performance.EnableLazyLoading', True)
settings.set('Performance.GCInterval', 50)  # 更频繁的垃圾回收
```

### 3. CPU密集型计算

对于CPU密集型计算：

```python
# 优化CPU使用
settings.set('Simulation.NumProcesses', 16)  # 使用更多进程
settings.set('Performance.ChunkSize', 50)    # 较小的数据块
settings.set('Performance.EnableVectorization', True)
```

## 性能监控和调试

### 1. 启用性能监控

```python
from cmost.utils.performance import performance_monitor

# 运行仿真
simulation.run(years=50)

# 获取性能报告
metrics = performance_monitor.get_all_metrics()
for name, stats in metrics.items():
    print(f"{name}: {stats}")

# 保存性能报告
performance_monitor.save_metrics('performance_report.json')
```

### 2. 内存使用监控

```python
from cmost.utils.memory import memory_manager

# 获取内存使用情况
memory_usage = memory_manager.get_memory_usage()
print(f"物理内存使用: {memory_usage['rss_mb']:.2f}MB")
print(f"内存使用百分比: {memory_usage['percent']:.1f}%")

# 获取对象池统计
pool_stats = memory_manager.get_pool_stats()
for pool_name, stats in pool_stats.items():
    print(f"{pool_name}: 命中率 {stats['hit_rate']:.2%}")
```

### 3. 缓存性能分析

```python
from cmost.utils.cache import default_cache

# 获取缓存统计
cache_stats = default_cache.get_stats()
print(f"L1缓存命中率: {cache_stats['l1']['hit_rate']:.2%}")
if 'l2' in cache_stats:
    print(f"L2缓存命中率: {cache_stats['l2']['hit_rate']:.2%}")
```

## 故障排除

### 1. 内存不足

如果遇到内存不足问题：

1. 减少患者批处理大小
2. 增加垃圾回收频率
3. 启用延迟加载
4. 使用持久化缓存而非内存缓存

### 2. 并行处理问题

如果并行处理出现问题：

1. 检查对象是否可序列化
2. 减少进程数量
3. 使用线程而非进程
4. 检查共享资源访问

### 3. 缓存问题

如果缓存效果不佳：

1. 调整缓存大小
2. 检查缓存键的设计
3. 调整TTL设置
4. 使用缓存预热

## 性能基准测试

运行基准测试来评估优化效果：

```python
from cmost.utils.performance import performance_monitor
import time

# 基准测试函数
def benchmark_simulation():
    with performance_monitor.monitor("benchmark"):
        # 运行标准仿真
        simulation.run(years=10)
    
    metrics = performance_monitor.get_metrics("benchmark")
    return metrics.execution_time, metrics.memory_usage

# 运行基准测试
exec_time, memory_usage = benchmark_simulation()
print(f"基准测试结果: {exec_time:.2f}s, {memory_usage:.2f}MB")
```
