#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CMOST调参测试脚本 - 简化版

专注于测试调参功能与仿真主程序的集成。
"""

import sys
import os
import logging
from pathlib import Path
import numpy as np
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_benchmark_integration():
    """测试基准值与仿真的集成"""
    print("=" * 60)
    print("1. 测试基准值与仿真集成")
    print("=" * 60)
    
    try:
        from cmost.calibration.benchmark import BenchmarkManager, load_benchmarks
        from cmost.core.simulation import Simulation
        from cmost.config.settings import Settings
        
        # 加载基准值
        print("加载基准值...")
        bm = BenchmarkManager()
        
        # 尝试加载CSV文件
        csv_path = "cmost/calibration/default_benchmarks_corrected.csv"
        if os.path.exists(csv_path):
            bm.load_benchmarks(csv_path)
            print("✓ CSV基准值文件加载成功")
        else:
            bm.load_default_benchmarks()
            print("✓ 默认基准值加载成功")
        
        # 创建仿真设置
        print("创建仿真设置...")
        settings = Settings()
        settings.set('Number_patients', 100)  # 使用很小的患者数量进行快速测试
        settings.set('Simulation_years', 5)
        
        # 从基准值中提取一些参数来设置仿真
        early_adenoma_male_50 = bm.benchmarks['early_adenoma_prevalence']['M'].get(50, 30.0)
        cancer_incidence_male_50 = bm.benchmarks['cancer_incidence']['M'].get(50, 50.0)
        
        print(f"使用基准值: 早期腺瘤患病率(男性50岁) = {early_adenoma_male_50}%")
        print(f"使用基准值: 癌症发病率(男性50岁) = {cancer_incidence_male_50}/100,000")
        
        # 将基准值转换为仿真参数（这里是简化的转换）
        settings.set('early_adenoma_rate', early_adenoma_male_50 / 1000.0)  # 简化转换
        settings.set('cancer_rate', cancer_incidence_male_50 / 100000.0)   # 简化转换
        
        print("✓ 基准值成功应用到仿真设置")
        
        # 创建仿真对象
        print("创建仿真对象...")
        simulation = Simulation(settings)
        print("✓ 仿真对象创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 基准值集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_calibration_integration():
    """测试自动校准与仿真的集成"""
    print("\n" + "=" * 60)
    print("2. 测试自动校准与仿真集成")
    print("=" * 60)
    
    try:
        from cmost.calibration.auto_calibration import AutoCalibration
        from cmost.core.simulation import Simulation
        from cmost.config.settings import Settings
        
        # 创建自动校准对象
        print("创建自动校准对象...")
        auto_calibration = AutoCalibration()
        print("✓ 自动校准对象创建成功")
        
        # 模拟校准结果
        print("模拟校准结果...")
        calibrated_params = {
            'early_adenoma_rate': 0.025,
            'advanced_adenoma_rate': 0.008,
            'cancer_rate': 0.0005,
            'preclinical_dwell_time': 3.5
        }
        print("✓ 校准参数生成成功")
        
        # 创建仿真设置并应用校准结果
        print("应用校准结果到仿真...")
        settings = Settings()
        settings.set('Number_patients', 100)
        settings.set('Simulation_years', 5)
        
        for param, value in calibrated_params.items():
            settings.set(param, value)
            print(f"  设置参数 {param} = {value}")
        
        # 创建仿真对象
        print("创建仿真对象...")
        simulation = Simulation(settings)
        print("✓ 校准结果成功应用到仿真")
        
        return True
        
    except Exception as e:
        print(f"✗ 自动校准集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_calibration_workflow():
    """测试集成校准工作流程"""
    print("\n" + "=" * 60)
    print("3. 测试集成校准工作流程")
    print("=" * 60)
    
    try:
        from cmost.calibration.integrated_calibration import IntegratedCalibrator, CalibrationMethod
        from cmost.calibration.benchmark import BenchmarkManager
        from cmost.core.simulation import Simulation
        from cmost.config.settings import Settings
        
        # 准备基准值
        print("准备基准值数据...")
        bm = BenchmarkManager()
        bm.load_default_benchmarks()
        benchmarks = bm.benchmarks
        print("✓ 基准值数据准备完成")
        
        # 创建集成校准器
        print("创建集成校准器...")
        calibrator = IntegratedCalibrator(
            benchmarks=benchmarks,
            output_dir="test_calibration_output"
        )
        print("✓ 集成校准器创建成功")
        
        # 模拟校准过程（不实际运行，只测试接口）
        print("测试校准接口...")
        
        # 测试参数准备
        calibration_data = calibrator._prepare_calibration_data()
        print(f"✓ 校准数据准备完成，包含 {len(calibration_data)} 个数据点")
        
        # 模拟校准结果
        mock_calibration_result = {
            'best_parameters': {
                'early_adenoma_rate': 0.022,
                'advanced_adenoma_rate': 0.006,
                'cancer_rate': 0.0004
            },
            'best_score': 0.85,
            'method_used': 'random_forest'
        }
        
        # 测试结果应用到仿真
        print("测试校准结果应用...")
        settings = Settings()
        settings.set('Number_patients', 100)
        settings.set('Simulation_years', 5)
        
        for param, value in mock_calibration_result['best_parameters'].items():
            settings.set(param, value)
        
        simulation = Simulation(settings)
        print("✓ 校准结果成功应用到仿真")
        
        return True
        
    except Exception as e:
        print(f"✗ 集成校准工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simulation_with_calibrated_parameters():
    """测试使用校准参数运行仿真"""
    print("\n" + "=" * 60)
    print("4. 测试使用校准参数运行仿真")
    print("=" * 60)
    
    try:
        from cmost.core.simulation import Simulation
        from cmost.config.settings import Settings
        
        # 创建包含校准参数的设置
        print("创建校准参数设置...")
        settings = Settings()
        
        # 基本仿真设置
        settings.set('Number_patients', 50)  # 很小的患者数量用于快速测试
        settings.set('Simulation_years', 3)
        
        # 校准后的参数（模拟值）
        calibrated_params = {
            'early_adenoma_rate': 0.020,
            'advanced_adenoma_rate': 0.005,
            'cancer_rate': 0.0003,
            'preclinical_dwell_time': 3.2,
            'screening_sensitivity': 0.85,
            'screening_specificity': 0.95
        }
        
        for param, value in calibrated_params.items():
            settings.set(param, value)
            print(f"  设置校准参数: {param} = {value}")
        
        print("✓ 校准参数设置完成")
        
        # 创建仿真对象
        print("创建仿真对象...")
        simulation = Simulation(settings)
        print("✓ 仿真对象创建成功")
        
        # 测试仿真初始化
        print("测试仿真初始化...")
        # 这里只测试仿真对象的创建和基本属性，不运行完整仿真
        print(f"  患者数量: {simulation.settings.get('Number_patients')}")
        print(f"  仿真年数: {simulation.settings.get('Simulation_years')}")
        print("✓ 仿真初始化成功")
        
        print("✓ 校准参数成功集成到仿真主程序")
        
        return True
        
    except Exception as e:
        print(f"✗ 校准参数仿真测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("CMOST调参与仿真集成测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试结果统计
    test_results = {}
    
    # 1. 测试基准值集成
    test_results['benchmark_integration'] = test_benchmark_integration()
    
    # 2. 测试自动校准集成
    test_results['auto_calibration_integration'] = test_auto_calibration_integration()
    
    # 3. 测试集成校准工作流程
    test_results['integrated_calibration_workflow'] = test_integrated_calibration_workflow()
    
    # 4. 测试校准参数仿真
    test_results['calibrated_simulation'] = test_simulation_with_calibrated_parameters()
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    
    for test_name, result in test_results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print(f"\n总计: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！调参功能与仿真主程序集成正常。")
        print("\n关键发现:")
        print("1. ✓ 基准值可以正确加载并应用到仿真设置")
        print("2. ✓ 自动校准结果可以正确传递给仿真对象")
        print("3. ✓ 集成校准工作流程接口正常")
        print("4. ✓ 校准参数可以成功集成到仿真主程序")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
