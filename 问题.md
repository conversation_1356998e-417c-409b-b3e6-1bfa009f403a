
一、需要将launch_enhanced_ui.py加载到Python main.py --gui界面中（在运行演示后面增加运行模拟），点击则启动launch_enhanced_ui.py

二、launch_enhanced_ui.py运行后

1.第一步中没有选择自然人群或出生队列人口的地方，没有人口参数文件选择的地方，支持excel、json格式（性别比例、不同年龄段人口占比）

2.第二步中调参结果没有可视化，且没有调参日志

![](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml16288\wps2.jpg)

3.第三步中主要筛查工具（除肠镜外）没有阳性后接受诊断性肠镜筛查的依从性录入位置；

启用贯序筛查中没有次要筛查工具的筛查参数（起止年龄）以及筛查工具性能参数（腺瘤灵敏度、癌症灵敏度、特异度和依从性）、以及阳性后接受诊断性肠镜筛查的依从性录入的地方

![](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml16288\wps3.jpg)

4.第四步中结果没有输出到results文件夹

![](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml16288\wps4.jpg)
