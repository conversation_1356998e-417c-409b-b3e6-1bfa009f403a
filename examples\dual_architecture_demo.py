"""
Demonstration of CMOST dual architecture functionality.

This script shows how to use the dual architecture manager to run
simulations with different population models and mode switching.
"""

import logging
from cmost.core.dual_architecture import (
    DualArchitectureManager, 
    SimulationMode, 
    ArchitectureConfig
)
from cmost.config.settings import Settings


def setup_logging():
    """Set up logging for the demo."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def demo_natural_population_simulation():
    """Demonstrate natural population simulation."""
    print("\n" + "="*60)
    print("DEMO 1: Natural Population Simulation")
    print("="*60)
    
    # Create settings
    settings = Settings()
    
    # Configure for natural population simulation
    config = ArchitectureConfig(
        primary_mode=SimulationMode.NATURAL_POPULATION,
        natural_population_size=10000
    )
    
    # Create manager and run simulation
    manager = DualArchitectureManager(settings, config)
    
    print(f"Running {config.primary_mode.value} simulation...")
    print(f"Population size: {config.natural_population_size}")
    
    # Run for 30 years
    results = manager.run_simulation(30)
    
    print("\nResults:")
    print(f"- Mode: {manager.get_current_mode().value}")
    if 'total_patients' in results:
        print(f"- Total patients: {results['total_patients']}")
    if 'cancer_cases' in results:
        print(f"- Cancer cases: {results['cancer_cases']}")
    
    return results


def demo_birth_cohort_simulation():
    """Demonstrate birth cohort simulation."""
    print("\n" + "="*60)
    print("DEMO 2: Birth Cohort Simulation")
    print("="*60)
    
    # Create settings
    settings = Settings()
    
    # Configure for birth cohort simulation
    config = ArchitectureConfig(
        primary_mode=SimulationMode.BIRTH_COHORT,
        annual_birth_cohort_size=1000
    )
    
    # Create manager and run simulation
    manager = DualArchitectureManager(settings, config)
    
    print(f"Running {config.primary_mode.value} simulation...")
    print(f"Annual birth cohort size: {config.annual_birth_cohort_size}")
    
    # Run for 50 years
    results = manager.run_simulation(50)
    
    print("\nResults:")
    print(f"- Mode: {manager.get_current_mode().value}")
    if 'total_patients' in results:
        print(f"- Total patients: {results['total_patients']}")
    if 'cohort_statistics' in results:
        cohort_stats = results['cohort_statistics']
        print(f"- Total cohorts: {cohort_stats.get('total_cohorts', 'N/A')}")
        print(f"- Total alive: {cohort_stats.get('total_alive', 'N/A')}")
    
    return results


def demo_mode_switching_simulation():
    """Demonstrate simulation with mode switching."""
    print("\n" + "="*60)
    print("DEMO 3: Mode Switching Simulation")
    print("="*60)
    
    # Create settings
    settings = Settings()
    
    # Configure for mode switching
    config = ArchitectureConfig(
        primary_mode=SimulationMode.NATURAL_POPULATION,
        secondary_mode=SimulationMode.BIRTH_COHORT,
        switch_year=25,
        natural_population_size=5000,
        annual_birth_cohort_size=500
    )
    
    # Create manager and run simulation
    manager = DualArchitectureManager(settings, config)
    
    print(f"Primary mode: {config.primary_mode.value}")
    print(f"Secondary mode: {config.secondary_mode.value}")
    print(f"Switch year: {config.switch_year}")
    
    # Run for 50 years (will switch at year 25)
    results = manager.run_simulation(50)
    
    print("\nResults:")
    print(f"- Final mode: {manager.get_current_mode().value}")
    
    if 'phase1_results' in results and 'phase2_results' in results:
        print("\nPhase 1 (Natural Population):")
        phase1 = results['phase1_results']
        if 'total_patients' in phase1:
            print(f"  - Total patients: {phase1['total_patients']}")
        
        print("\nPhase 2 (Birth Cohort):")
        phase2 = results['phase2_results']
        if 'total_patients' in phase2:
            print(f"  - Total patients: {phase2['total_patients']}")
        
        print("\nCombined Results:")
        combined = results.get('combined_metrics', {})
        for key, value in combined.items():
            print(f"  - {key}: {value}")
    
    return results


def demo_hybrid_simulation():
    """Demonstrate hybrid simulation."""
    print("\n" + "="*60)
    print("DEMO 4: Hybrid Simulation")
    print("="*60)
    
    # Create settings
    settings = Settings()
    
    # Configure for hybrid simulation
    config = ArchitectureConfig(
        primary_mode=SimulationMode.HYBRID,
        natural_population_size=8000,
        annual_birth_cohort_size=800,
        population_split_ratio=0.6  # 60% natural population, 40% birth cohort
    )
    
    # Create manager and run simulation
    manager = DualArchitectureManager(settings, config)
    
    print(f"Running {config.primary_mode.value} simulation...")
    print(f"Population split ratio: {config.population_split_ratio}")
    
    natural_pop = int(config.natural_population_size * config.population_split_ratio)
    birth_cohort = int(config.annual_birth_cohort_size * (1 - config.population_split_ratio))
    
    print(f"Natural population size: {natural_pop}")
    print(f"Annual birth cohort size: {birth_cohort}")
    
    # Run for 40 years
    results = manager.run_simulation(40)
    
    print("\nResults:")
    print(f"- Mode: {manager.get_current_mode().value}")
    
    if 'phase1_results' in results and 'phase2_results' in results:
        print("\nNatural Population Component:")
        phase1 = results['phase1_results']
        if 'total_patients' in phase1:
            print(f"  - Total patients: {phase1['total_patients']}")
        
        print("\nBirth Cohort Component:")
        phase2 = results['phase2_results']
        if 'total_patients' in phase2:
            print(f"  - Total patients: {phase2['total_patients']}")
        
        print("\nCombined Results:")
        combined = results.get('combined_metrics', {})
        for key, value in combined.items():
            print(f"  - {key}: {value}")
    
    return results


def demo_manual_mode_switching():
    """Demonstrate manual mode switching."""
    print("\n" + "="*60)
    print("DEMO 5: Manual Mode Switching")
    print("="*60)
    
    # Create settings
    settings = Settings()
    
    # Start with natural population
    config = ArchitectureConfig(
        primary_mode=SimulationMode.NATURAL_POPULATION,
        natural_population_size=5000
    )
    
    # Create manager
    manager = DualArchitectureManager(settings, config)
    
    print(f"Initial mode: {manager.get_current_mode().value}")
    
    # Get simulation summary
    summary = manager.get_simulation_summary()
    print("\nSimulation Configuration:")
    for key, value in summary['configuration'].items():
        print(f"  - {key}: {value}")
    
    # Manually switch modes
    print(f"\nSwitching to {SimulationMode.BIRTH_COHORT.value}...")
    manager.switch_mode(SimulationMode.BIRTH_COHORT)
    print(f"Current mode: {manager.get_current_mode().value}")
    
    print(f"\nSwitching to {SimulationMode.HYBRID.value}...")
    manager.switch_mode(SimulationMode.HYBRID)
    print(f"Current mode: {manager.get_current_mode().value}")
    
    print(f"\nSwitching back to {SimulationMode.NATURAL_POPULATION.value}...")
    manager.switch_mode(SimulationMode.NATURAL_POPULATION)
    print(f"Current mode: {manager.get_current_mode().value}")
    
    return manager


def main():
    """Run all demonstrations."""
    print("CMOST Dual Architecture Demonstration")
    print("=====================================")
    
    # Set up logging
    setup_logging()
    
    try:
        # Run demonstrations
        demo_natural_population_simulation()
        demo_birth_cohort_simulation()
        demo_mode_switching_simulation()
        demo_hybrid_simulation()
        demo_manual_mode_switching()
        
        print("\n" + "="*60)
        print("All demonstrations completed successfully!")
        print("="*60)
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
