"""
调参日志管理模块

该模块提供调参过程的详细日志记录功能，包括参数变化、误差变化、执行时间等信息的记录和管理。
"""

import os
import json
import logging
import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import csv


class CalibrationLogger:
    """调参日志管理器"""
    
    def __init__(self, output_dir: str = "calibration_logs", log_level: int = logging.INFO):
        """
        初始化调参日志管理器
        
        Args:
            output_dir: 日志输出目录
            log_level: 日志级别
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建日志文件名（基于时间戳）
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.output_dir / f"calibration_{timestamp}.log"
        self.json_file = self.output_dir / f"calibration_{timestamp}.json"
        self.csv_file = self.output_dir / f"calibration_{timestamp}.csv"
        
        # 设置日志器
        self.logger = self._setup_logger(log_level)
        
        # 调参历史记录
        self.calibration_history = []
        self.current_session = {
            'start_time': datetime.datetime.now().isoformat(),
            'method': None,
            'parameters': {},
            'iterations': [],
            'final_result': None
        }
        
        # CSV文件头
        self.csv_headers = [
            'timestamp', 'iteration', 'method', 'error', 'execution_time',
            'parameters', 'convergence_status', 'notes'
        ]
        
        # 初始化CSV文件
        self._init_csv_file()
        
    def _setup_logger(self, log_level: int) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(f"CalibrationLogger_{id(self)}")
        logger.setLevel(log_level)
        
        # 避免重复添加处理器
        if not logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(log_level)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
        
        return logger
    
    def _init_csv_file(self):
        """初始化CSV文件"""
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(self.csv_headers)
    
    def start_calibration_session(self, method: str, initial_parameters: Dict[str, Any]):
        """
        开始新的调参会话
        
        Args:
            method: 调参方法
            initial_parameters: 初始参数
        """
        self.current_session = {
            'start_time': datetime.datetime.now().isoformat(),
            'method': method,
            'initial_parameters': initial_parameters.copy(),
            'iterations': [],
            'final_result': None
        }
        
        self.logger.info(f"开始调参会话 - 方法: {method}")
        self.logger.info(f"初始参数: {initial_parameters}")
        
        # 记录到CSV
        self._write_csv_row({
            'timestamp': datetime.datetime.now().isoformat(),
            'iteration': 0,
            'method': method,
            'error': None,
            'execution_time': 0,
            'parameters': json.dumps(initial_parameters, ensure_ascii=False),
            'convergence_status': 'started',
            'notes': '调参会话开始'
        })
    
    def log_iteration(self, 
                     iteration: int, 
                     parameters: Dict[str, Any], 
                     error: float, 
                     execution_time: float = 0,
                     convergence_status: str = 'running',
                     notes: str = ''):
        """
        记录调参迭代信息
        
        Args:
            iteration: 迭代次数
            parameters: 当前参数
            error: 当前误差
            execution_time: 执行时间
            convergence_status: 收敛状态
            notes: 备注信息
        """
        iteration_data = {
            'iteration': iteration,
            'parameters': parameters.copy(),
            'error': error,
            'execution_time': execution_time,
            'convergence_status': convergence_status,
            'timestamp': datetime.datetime.now().isoformat(),
            'notes': notes
        }
        
        self.current_session['iterations'].append(iteration_data)
        
        self.logger.info(f"迭代 {iteration}: 误差={error:.6f}, 时间={execution_time:.2f}s")
        self.logger.debug(f"参数: {parameters}")
        
        if notes:
            self.logger.info(f"备注: {notes}")
        
        # 记录到CSV
        self._write_csv_row({
            'timestamp': iteration_data['timestamp'],
            'iteration': iteration,
            'method': self.current_session['method'],
            'error': error,
            'execution_time': execution_time,
            'parameters': json.dumps(parameters, ensure_ascii=False),
            'convergence_status': convergence_status,
            'notes': notes
        })
    
    def log_convergence_check(self, 
                             iteration: int, 
                             error_change: float, 
                             parameter_change: float,
                             tolerance: float):
        """
        记录收敛检查信息
        
        Args:
            iteration: 迭代次数
            error_change: 误差变化
            parameter_change: 参数变化
            tolerance: 容忍度
        """
        converged = abs(error_change) < tolerance and abs(parameter_change) < tolerance
        status = "收敛" if converged else "未收敛"
        
        self.logger.info(f"收敛检查 - 迭代 {iteration}: {status}")
        self.logger.info(f"误差变化: {error_change:.6f}, 参数变化: {parameter_change:.6f}, 容忍度: {tolerance:.6f}")
        
        return converged
    
    def end_calibration_session(self, final_result: Dict[str, Any]):
        """
        结束调参会话
        
        Args:
            final_result: 最终结果
        """
        self.current_session['end_time'] = datetime.datetime.now().isoformat()
        self.current_session['final_result'] = final_result.copy()
        
        # 计算总执行时间
        start_time = datetime.datetime.fromisoformat(self.current_session['start_time'])
        end_time = datetime.datetime.fromisoformat(self.current_session['end_time'])
        total_time = (end_time - start_time).total_seconds()
        
        self.current_session['total_execution_time'] = total_time
        
        self.logger.info("调参会话结束")
        self.logger.info(f"总执行时间: {total_time:.2f}秒")
        self.logger.info(f"最终误差: {final_result.get('error', 'N/A')}")
        self.logger.info(f"最终参数: {final_result.get('parameters', {})}")
        
        # 添加到历史记录
        self.calibration_history.append(self.current_session.copy())
        
        # 保存到JSON文件
        self._save_session_to_json()
        
        # 记录到CSV
        self._write_csv_row({
            'timestamp': self.current_session['end_time'],
            'iteration': len(self.current_session['iterations']),
            'method': self.current_session['method'],
            'error': final_result.get('error'),
            'execution_time': total_time,
            'parameters': json.dumps(final_result.get('parameters', {}), ensure_ascii=False),
            'convergence_status': 'completed',
            'notes': '调参会话结束'
        })
    
    def log_error(self, error_message: str, exception: Optional[Exception] = None):
        """
        记录错误信息
        
        Args:
            error_message: 错误消息
            exception: 异常对象
        """
        self.logger.error(f"调参错误: {error_message}")
        
        if exception:
            self.logger.error(f"异常详情: {str(exception)}")
            import traceback
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
        
        # 记录到CSV
        self._write_csv_row({
            'timestamp': datetime.datetime.now().isoformat(),
            'iteration': None,
            'method': self.current_session.get('method'),
            'error': None,
            'execution_time': None,
            'parameters': None,
            'convergence_status': 'error',
            'notes': f"错误: {error_message}"
        })
    
    def _write_csv_row(self, data: Dict[str, Any]):
        """写入CSV行数据"""
        try:
            with open(self.csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                row = [data.get(header, '') for header in self.csv_headers]
                writer.writerow(row)
        except Exception as e:
            self.logger.error(f"写入CSV文件失败: {e}")
    
    def _save_session_to_json(self):
        """保存会话到JSON文件"""
        try:
            # 如果文件已存在，读取现有数据
            if self.json_file.exists():
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = {'sessions': []}
            
            # 添加当前会话
            existing_data['sessions'].append(self.current_session)
            
            # 保存更新后的数据
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存JSON文件失败: {e}")
    
    def get_session_summary(self) -> Dict[str, Any]:
        """获取当前会话摘要"""
        if not self.current_session['iterations']:
            return {'status': 'no_iterations'}
        
        iterations = self.current_session['iterations']
        errors = [it['error'] for it in iterations if it['error'] is not None]
        
        summary = {
            'method': self.current_session['method'],
            'total_iterations': len(iterations),
            'start_time': self.current_session['start_time'],
            'initial_error': errors[0] if errors else None,
            'final_error': errors[-1] if errors else None,
            'best_error': min(errors) if errors else None,
            'error_improvement': (errors[0] - errors[-1]) if len(errors) > 1 else 0,
            'convergence_achieved': self.current_session.get('final_result', {}).get('converged', False)
        }
        
        return summary
    
    def export_history(self, export_path: Optional[str] = None) -> str:
        """
        导出调参历史
        
        Args:
            export_path: 导出路径
            
        Returns:
            str: 导出文件路径
        """
        if export_path is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            export_path = self.output_dir / f"calibration_history_export_{timestamp}.json"
        
        export_data = {
            'export_time': datetime.datetime.now().isoformat(),
            'total_sessions': len(self.calibration_history),
            'sessions': self.calibration_history
        }
        
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"调参历史已导出: {export_path}")
        return str(export_path)


# 全局日志管理器实例
calibration_logger = CalibrationLogger()
