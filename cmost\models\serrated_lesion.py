"""
Serrated lesion model for CMOST simulation.

This module implements the serrated lesion-cancer pathway,
complementing the traditional adenoma-carcinoma sequence.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import numpy as np
from datetime import datetime

from .cancer import Cancer
from .molecular_features import MolecularProfile, MolecularProfileGenerator


@dataclass
class SerratedLesion:
    """Represents a serrated lesion in the simulation.
    
    Serrated pathway: Normal → Small SSL → Large SSL → Preclinical Cancer → Clinical Cancer
    Stages:
    - 1: Small sessile serrated lesion (SSL) <10mm
    - 2: Large sessile serrated lesion (SSL) ≥10mm  
    - 3: SSL with dysplasia
    - 4: Preclinical cancer (asymptomatic)
    - 5+: Clinical cancer (stages I-IV, mapped to 7-10 for consistency)
    """
    
    id: int
    location: int  # Anatomical location (1-6 representing different colon segments)
    size: float    # Size in cm
    stage: int     # Stage (1-4 for serrated pathway, 5+ for cancer)
    patient_id: int
    patient_gender: str
    
    # Serrated-specific properties
    has_dysplasia: bool = False
    methylation_status: str = "unknown"  # CpG island methylator phenotype
    
    # Tracking information
    discovery_year: Optional[int] = None
    discovery_method: Optional[str] = None
    year_created: int = field(default_factory=lambda: datetime.now().year)

    # Molecular profile
    molecular_profile: Optional[MolecularProfile] = None

    # Additional properties
    properties: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Initialize molecular profile if not provided."""
        if self.molecular_profile is None:
            generator = MolecularProfileGenerator()
            self.molecular_profile = generator.generate_profile(
                lesion_type="serrated",
                stage=self.stage,
                location=self.location,
                patient_age=60  # Default age, should be updated with actual patient age
            )
    
    def progress(self, years: int, progression_model, individual_risk: float, age: Optional[int] = None) -> bool:
        """Progress the serrated lesion by a number of years.
        
        Args:
            years: Number of years to progress
            progression_model: Model that defines progression rates
            individual_risk: Patient's individual risk multiplier
            age: Patient's current age (optional)
            
        Returns:
            bool: True if lesion progressed to cancer, False otherwise
        """
        progressed_to_cancer = False

        # Update molecular profile with patient age if available
        if age is not None and self.molecular_profile:
            generator = MolecularProfileGenerator()
            self.molecular_profile = generator.generate_profile(
                lesion_type="serrated",
                stage=self.stage,
                location=self.location,
                patient_age=age
            )

        # Apply molecular progression modifier
        molecular_modifier = 1.0
        if self.molecular_profile:
            molecular_modifier = self.molecular_profile.get_progression_modifier()

        adjusted_risk = individual_risk * molecular_modifier

        for _ in range(years):
            new_stage, is_cancer = progression_model.progress_serrated_lesion(
                self.stage,
                self.location,
                self.patient_gender,
                adjusted_risk,
                age,
                self.has_dysplasia
            )
            
            # Update size based on stage change
            if new_stage > self.stage:
                self.size = self._calculate_new_size(new_stage)
                
                # Check for dysplasia development
                if new_stage >= 3 and not self.has_dysplasia:
                    self.has_dysplasia = True
                    
            elif new_stage < self.stage and new_stage > 0:
                # Regression (rare in serrated pathway)
                self.size = self.size * 0.9  # Shrink by 10%
            elif new_stage == 0:
                # Lesion disappeared
                self.size = 0
                
            self.stage = new_stage
            
            if is_cancer:
                progressed_to_cancer = True
                break
                
            # If lesion disappeared, stop progression
            if self.stage == 0:
                break
                
        return progressed_to_cancer
        
    def _calculate_new_size(self, new_stage: int) -> float:
        """Calculate new lesion size based on stage progression.
        
        Args:
            new_stage: New lesion stage
            
        Returns:
            float: New size in cm
        """
        # Size ranges by stage (in cm)
        size_ranges = {
            1: (0.3, 0.9),    # Small SSL: 3-9mm
            2: (1.0, 3.0),    # Large SSL: 10-30mm
            3: (1.0, 4.0),    # SSL with dysplasia: 10-40mm
            4: (1.5, 5.0),    # Preclinical cancer: 15-50mm
        }
        
        if new_stage in size_ranges:
            min_size, max_size = size_ranges[new_stage]
            
            # If current size is already in range, increase slightly
            if min_size <= self.size <= max_size:
                return min(self.size * 1.3, max_size)
            else:
                # Otherwise, pick a size in the appropriate range
                return min_size + np.random.random() * (max_size - min_size)
        
        return self.size  # Default: no change
        
    @property
    def is_large(self) -> bool:
        """Check if lesion is large (≥10mm).
        
        Returns:
            bool: True if lesion is large, False otherwise
        """
        return self.size >= 1.0  # 10mm
        
    @property
    def is_advanced(self) -> bool:
        """Check if lesion is advanced (with dysplasia or cancer).
        
        Returns:
            bool: True if lesion is advanced, False otherwise
        """
        return self.stage >= 3 or self.has_dysplasia
        
    @property
    def cancer_risk_level(self) -> str:
        """Get cancer risk level based on lesion characteristics.
        
        Returns:
            str: Risk level ('low', 'moderate', 'high', 'very_high')
        """
        if self.stage >= 4:
            return 'very_high'
        elif self.has_dysplasia:
            return 'high'
        elif self.is_large:
            return 'moderate'
        else:
            return 'low'
            
    def to_cancer(self) -> Cancer:
        """Convert serrated lesion to cancer.
        
        Returns:
            Cancer: Cancer object derived from this lesion
        """
        return Cancer(
            id=self.id,
            location=self.location,
            stage=7,  # Start as Stage I cancer
            patient_id=self.patient_id,
            patient_gender=self.patient_gender,
            source_polyp_id=self.id,
            year_created=datetime.now().year
        )
        
    def get_screening_detectability(self, test_type: str) -> float:
        """Get detectability of this lesion by different screening tests.
        
        Args:
            test_type: Type of screening test
            
        Returns:
            float: Detection probability (0-1)
        """
        # Serrated lesions are generally harder to detect than adenomas
        base_detectability = {
            'colonoscopy': {
                1: 0.60,  # Small SSL harder to see
                2: 0.80,  # Large SSL more visible
                3: 0.85,  # With dysplasia, more obvious
                4: 0.90   # Preclinical cancer
            },
            'sigmoidoscopy': {
                1: 0.50,  # Only if in distal colon
                2: 0.70,
                3: 0.75,
                4: 0.85
            },
            'fit': {
                1: 0.05,  # Very low detection
                2: 0.10,
                3: 0.20,  # Some bleeding with dysplasia
                4: 0.40   # More bleeding with cancer
            }
        }
        
        if test_type not in base_detectability:
            return 0.0
            
        detectability = base_detectability[test_type].get(self.stage, 0.0)
        
        # Adjust for location (sigmoidoscopy only sees distal colon)
        if test_type == 'sigmoidoscopy' and self.location > 3:
            detectability = 0.0
            
        # Adjust for size
        if self.size > 2.0:  # Large lesions easier to detect
            detectability *= 1.2
        elif self.size < 0.5:  # Small lesions harder to detect
            detectability *= 0.8
            
        return min(1.0, detectability)
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert serrated lesion to dictionary for serialization.
        
        Returns:
            Dict[str, Any]: Dictionary representation of serrated lesion
        """
        return {
            'id': self.id,
            'location': self.location,
            'size': self.size,
            'stage': self.stage,
            'patient_id': self.patient_id,
            'patient_gender': self.patient_gender,
            'has_dysplasia': self.has_dysplasia,
            'methylation_status': self.methylation_status,
            'discovery_year': self.discovery_year,
            'discovery_method': self.discovery_method,
            'year_created': self.year_created,
            'properties': self.properties,
            'is_large': self.is_large,
            'is_advanced': self.is_advanced,
            'cancer_risk_level': self.cancer_risk_level
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SerratedLesion':
        """Create serrated lesion from dictionary.
        
        Args:
            data: Dictionary containing lesion data
            
        Returns:
            SerratedLesion: Reconstructed serrated lesion
        """
        lesion = cls(
            id=data['id'],
            location=data['location'],
            size=data['size'],
            stage=data['stage'],
            patient_id=data['patient_id'],
            patient_gender=data['patient_gender'],
            has_dysplasia=data.get('has_dysplasia', False),
            methylation_status=data.get('methylation_status', 'unknown'),
            discovery_year=data.get('discovery_year'),
            discovery_method=data.get('discovery_method'),
            year_created=data.get('year_created', datetime.now().year)
        )
        
        lesion.properties = data.get('properties', {})
        return lesion
        
    def __str__(self) -> str:
        """String representation of serrated lesion."""
        stage_names = {
            1: "Small SSL",
            2: "Large SSL", 
            3: "SSL with dysplasia",
            4: "Preclinical cancer"
        }
        
        stage_name = stage_names.get(self.stage, f"Stage {self.stage}")
        dysplasia_str = " (with dysplasia)" if self.has_dysplasia else ""
        
        return f"SerratedLesion(id={self.id}, {stage_name}, {self.size:.1f}cm, location={self.location}{dysplasia_str})"
