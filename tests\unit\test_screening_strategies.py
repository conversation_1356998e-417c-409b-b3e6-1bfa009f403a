"""
Unit tests for screening strategies in CMOST.
"""

import unittest
from unittest.mock import Mock, patch
import numpy as np

from cmost.screening.strategy_manager import (
    ScreeningStrategyManager,
    ScreeningStrategy,
    ScreeningTest,
    StrategyType,
    TestCharacteristics
)
from cmost.models.patient import Patient
from cmost.models.polyp import Polyp
from cmost.models.serrated_lesion import SerratedLesion
from cmost.models.cancer import Cancer


class TestTestCharacteristics(unittest.TestCase):
    """Test cases for TestCharacteristics class."""
    
    def test_colonoscopy_characteristics(self):
        """Test colonoscopy test characteristics."""
        colonoscopy = TestCharacteristics("colonoscopy")
        
        self.assertTrue(colonoscopy.requires_bowel_prep)
        self.assertTrue(colonoscopy.is_invasive)
        self.assertEqual(colonoscopy.detection_range, "whole_colon")
        self.assertGreater(colonoscopy.cost, 500)
        self.assertGreater(colonoscopy.sensitivity['advanced_adenoma'], 0.9)
    
    def test_fit_characteristics(self):
        """Test FIT test characteristics."""
        fit = TestCharacteristics("fit")
        
        self.assertFalse(fit.requires_bowel_prep)
        self.assertFalse(fit.is_invasive)
        self.assertEqual(fit.detection_range, "whole_colon")
        self.assertLess(fit.cost, 100)
        self.assertLess(fit.sensitivity['small_adenoma'], 0.2)
    
    def test_sigmoidoscopy_characteristics(self):
        """Test sigmoidoscopy test characteristics."""
        sigmoidoscopy = TestCharacteristics("sigmoidoscopy")
        
        self.assertFalse(sigmoidoscopy.requires_bowel_prep)
        self.assertTrue(sigmoidoscopy.is_invasive)
        self.assertEqual(sigmoidoscopy.detection_range, "distal")
        self.assertGreater(sigmoidoscopy.sensitivity['advanced_adenoma'], 0.8)


class TestScreeningStrategy(unittest.TestCase):
    """Test cases for ScreeningStrategy class."""
    
    def test_single_test_strategy(self):
        """Test single test strategy creation."""
        strategy = ScreeningStrategy(
            name="Colonoscopy Only",
            strategy_type=StrategyType.SINGLE_TEST,
            primary_test=ScreeningTest.COLONOSCOPY
        )
        
        self.assertEqual(strategy.strategy_type, StrategyType.SINGLE_TEST)
        self.assertEqual(strategy.primary_test, ScreeningTest.COLONOSCOPY)
    
    def test_sequential_strategy(self):
        """Test sequential strategy creation."""
        strategy = ScreeningStrategy(
            name="FIT with Colonoscopy Follow-up",
            strategy_type=StrategyType.SEQUENTIAL,
            primary_test=ScreeningTest.FIT,
            secondary_tests=[ScreeningTest.COLONOSCOPY]
        )
        
        self.assertEqual(strategy.strategy_type, StrategyType.SEQUENTIAL)
        self.assertEqual(strategy.primary_test, ScreeningTest.FIT)
        self.assertIn(ScreeningTest.COLONOSCOPY, strategy.secondary_tests)
    
    def test_parallel_strategy(self):
        """Test parallel strategy creation."""
        strategy = ScreeningStrategy(
            name="Sigmoidoscopy + FIT",
            strategy_type=StrategyType.PARALLEL,
            tests=[ScreeningTest.SIGMOIDOSCOPY, ScreeningTest.FIT]
        )
        
        self.assertEqual(strategy.strategy_type, StrategyType.PARALLEL)
        self.assertIn(ScreeningTest.SIGMOIDOSCOPY, strategy.tests)
        self.assertIn(ScreeningTest.FIT, strategy.tests)


class TestScreeningStrategyManager(unittest.TestCase):
    """Test cases for ScreeningStrategyManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.manager = ScreeningStrategyManager()
        self.patient = Patient(
            id=1,
            age=55,
            gender='M',
            risk_factors={'smoking': 1.5}
        )
        
        # Add some lesions to the patient
        self.patient.add_polyp(location=3, size=0.8)
        self.patient.add_serrated_lesion(location=5, size=1.2)
    
    def test_manager_initialization(self):
        """Test manager initialization."""
        self.assertIsInstance(self.manager, ScreeningStrategyManager)
        self.assertIn("colonoscopy_only", self.manager.strategies)
        self.assertIn("fit_only", self.manager.strategies)
        self.assertIn("fit_colonoscopy", self.manager.strategies)
    
    def test_get_predefined_strategy(self):
        """Test getting predefined strategies."""
        colonoscopy_strategy = self.manager.get_strategy("colonoscopy_only")
        self.assertIsNotNone(colonoscopy_strategy)
        self.assertEqual(colonoscopy_strategy.strategy_type, StrategyType.SINGLE_TEST)
        
        fit_strategy = self.manager.get_strategy("fit_only")
        self.assertIsNotNone(fit_strategy)
        self.assertEqual(fit_strategy.primary_test, ScreeningTest.FIT)
    
    def test_add_custom_strategy(self):
        """Test adding custom strategy."""
        custom_strategy = ScreeningStrategy(
            name="Custom Test Strategy",
            strategy_type=StrategyType.SINGLE_TEST,
            primary_test=ScreeningTest.CT_COLONOGRAPHY
        )
        
        self.manager.add_custom_strategy(custom_strategy)
        retrieved = self.manager.get_strategy("custom_test_strategy")
        
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.name, "Custom Test Strategy")
    
    def test_single_test_strategy_application(self):
        """Test applying single test strategy."""
        # Set random seed for reproducible results
        np.random.seed(42)
        
        result = self.manager.apply_strategy("colonoscopy_only", self.patient, 2023)
        
        self.assertIn("test", result)
        self.assertEqual(result["test"], "colonoscopy")
        self.assertIn("result", result)
        self.assertIn("findings", result)
    
    def test_sequential_strategy_application(self):
        """Test applying sequential strategy."""
        # Set random seed for reproducible results
        np.random.seed(42)
        
        result = self.manager.apply_strategy("fit_colonoscopy", self.patient, 2023)
        
        self.assertIn("primary_test", result)
        self.assertEqual(result["primary_test"]["test"], "fit")
        
        # If FIT is positive, should have follow-up
        if result["primary_test"]["result"] == "positive":
            self.assertIn("follow_up_test", result)
    
    def test_parallel_strategy_application(self):
        """Test applying parallel strategy."""
        # Set random seed for reproducible results
        np.random.seed(42)
        
        result = self.manager.apply_strategy("sigmoidoscopy_fit", self.patient, 2023)
        
        self.assertIn("tests", result)
        self.assertIn("combined_result", result)
        
        # Should have results for both tests
        tests = result["tests"]
        self.assertIn("sigmoidoscopy", tests)
        self.assertIn("fit", tests)
    
    def test_risk_stratified_strategy_application(self):
        """Test applying risk-stratified strategy."""
        # Set random seed for reproducible results
        np.random.seed(42)
        
        result = self.manager.apply_strategy("risk_stratified", self.patient, 2023)
        
        self.assertIn("risk_level", result)
        self.assertIn("risk_score", result)
        self.assertIn("test", result)
        
        # Test should be appropriate for risk level
        risk_level = result["risk_level"]
        test = result["test"]
        
        if risk_level == "low":
            self.assertEqual(test, "fit")
        elif risk_level == "moderate":
            self.assertEqual(test, "sigmoidoscopy")
        elif risk_level == "high":
            self.assertEqual(test, "colonoscopy")
    
    def test_age_eligibility(self):
        """Test age eligibility for screening."""
        # Young patient (below screening age)
        young_patient = Patient(id=2, age=35, gender='F')
        result = self.manager.apply_strategy("colonoscopy_only", young_patient, 2023)
        
        self.assertFalse(result.get("eligible", True))
        self.assertEqual(result.get("reason"), "age_out_of_range")
        
        # Old patient (above screening age)
        old_patient = Patient(id=3, age=80, gender='M')
        result = self.manager.apply_strategy("colonoscopy_only", old_patient, 2023)
        
        self.assertFalse(result.get("eligible", True))
    
    def test_lesion_detection(self):
        """Test lesion detection in screening."""
        # Set random seed for reproducible results
        np.random.seed(123)  # Seed that should result in detection
        
        # Patient with large polyp (should be detected)
        patient_with_large_polyp = Patient(id=4, age=60, gender='M')
        large_polyp = patient_with_large_polyp.add_polyp(location=3, size=1.5)
        large_polyp.stage = 4  # Advanced adenoma
        
        result = self.manager.apply_strategy("colonoscopy_only", patient_with_large_polyp, 2023)
        
        # Should detect the large polyp
        findings = result.get("findings", {})
        self.assertGreater(findings.get("total_detected", 0), 0)
    
    def test_detection_range_limitations(self):
        """Test detection range limitations for different tests."""
        # Patient with proximal lesion
        patient = Patient(id=5, age=60, gender='F')
        patient.add_polyp(location=5, size=1.0)  # Ascending colon (proximal)
        
        # Sigmoidoscopy should not detect proximal lesions
        characteristics = self.manager.test_characteristics[ScreeningTest.SIGMOIDOSCOPY]
        is_detectable = self.manager._is_lesion_detectable(
            patient.polyps[0], 
            characteristics
        )
        
        self.assertFalse(is_detectable)  # Proximal lesion not detectable by sigmoidoscopy
        
        # Colonoscopy should detect all lesions
        colonoscopy_characteristics = self.manager.test_characteristics[ScreeningTest.COLONOSCOPY]
        is_detectable_colonoscopy = self.manager._is_lesion_detectable(
            patient.polyps[0], 
            colonoscopy_characteristics
        )
        
        self.assertTrue(is_detectable_colonoscopy)
    
    def test_polyp_classification(self):
        """Test polyp classification for detection."""
        # Small polyp
        small_polyp = Polyp(id=1, location=1, size=0.5, stage=1, patient_id=1, patient_gender='M')
        classification = self.manager._classify_polyp_for_detection(small_polyp)
        self.assertEqual(classification, "small_adenoma")
        
        # Advanced adenoma (large size)
        large_polyp = Polyp(id=2, location=1, size=1.5, stage=2, patient_id=1, patient_gender='M')
        classification = self.manager._classify_polyp_for_detection(large_polyp)
        self.assertEqual(classification, "advanced_adenoma")
        
        # Advanced adenoma (high stage)
        advanced_polyp = Polyp(id=3, location=1, size=0.8, stage=4, patient_id=1, patient_gender='M')
        classification = self.manager._classify_polyp_for_detection(advanced_polyp)
        self.assertEqual(classification, "advanced_adenoma")
    
    def test_unknown_strategy_error(self):
        """Test error handling for unknown strategy."""
        with self.assertRaises(ValueError):
            self.manager.apply_strategy("unknown_strategy", self.patient, 2023)


if __name__ == '__main__':
    unittest.main()
