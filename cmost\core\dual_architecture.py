"""
Dual architecture simulation manager for CMOST.

This module implements the dual architecture support that allows switching
between natural population cohort and birth cohort simulation modes.
"""

from typing import Dict, List, Optional, Any, Union
from enum import Enum
import logging
from dataclasses import dataclass, field

from .simulation import Simulation
from .birth_cohort import BirthCohortSimulation
from ..config.settings import Settings
from ..models.patient import Patient


class SimulationMode(Enum):
    """Simulation architecture modes."""
    NATURAL_POPULATION = "natural_population"
    BIRTH_COHORT = "birth_cohort"
    HYBRID = "hybrid"


@dataclass
class ArchitectureConfig:
    """Configuration for dual architecture simulation."""
    
    primary_mode: SimulationMode = SimulationMode.NATURAL_POPULATION
    secondary_mode: Optional[SimulationMode] = None
    
    # Mode switching parameters
    switch_year: Optional[int] = None
    switch_criteria: Dict[str, Any] = field(default_factory=dict)
    
    # Population size settings
    natural_population_size: int = 100000
    annual_birth_cohort_size: int = 1000
    
    # Hybrid mode settings
    population_split_ratio: float = 0.7  # Ratio for natural population in hybrid mode


class DualArchitectureManager:
    """Manager for dual architecture simulation."""
    
    def __init__(self, settings: Settings, config: Optional[ArchitectureConfig] = None):
        """Initialize dual architecture manager.
        
        Args:
            settings: Simulation settings
            config: Architecture configuration
        """
        self.settings = settings
        self.config = config or ArchitectureConfig()
        
        # Initialize simulation engines
        self.natural_simulation: Optional[Simulation] = None
        self.birth_cohort_simulation: Optional[BirthCohortSimulation] = None
        
        # Current state
        self.current_mode = self.config.primary_mode
        self.current_year = 0
        self.results: Dict[str, Any] = {}
        
        # Logging
        self.logger = logging.getLogger("CMOST_DualArchitecture")
        
        # Initialize simulations based on configuration
        self._initialize_simulations()
    
    def _initialize_simulations(self) -> None:
        """Initialize simulation engines based on configuration."""
        if (self.config.primary_mode == SimulationMode.NATURAL_POPULATION or
            self.config.secondary_mode == SimulationMode.NATURAL_POPULATION or
            self.config.primary_mode == SimulationMode.HYBRID):
            
            self.logger.info("Initializing natural population simulation")
            self.natural_simulation = Simulation(self.settings)
            
        if (self.config.primary_mode == SimulationMode.BIRTH_COHORT or
            self.config.secondary_mode == SimulationMode.BIRTH_COHORT or
            self.config.primary_mode == SimulationMode.HYBRID):
            
            self.logger.info("Initializing birth cohort simulation")
            self.birth_cohort_simulation = BirthCohortSimulation(self.settings)
    
    def run_simulation(self, years: int) -> Dict[str, Any]:
        """Run simulation using the configured architecture.
        
        Args:
            years: Number of years to simulate
            
        Returns:
            Dict containing simulation results
        """
        self.logger.info(f"Starting dual architecture simulation for {years} years")
        self.logger.info(f"Primary mode: {self.current_mode.value}")
        
        if self.config.switch_year and self.config.secondary_mode:
            # Run with mode switching
            return self._run_with_mode_switching(years)
        elif self.current_mode == SimulationMode.HYBRID:
            # Run hybrid simulation
            return self._run_hybrid_simulation(years)
        else:
            # Run single mode simulation
            return self._run_single_mode_simulation(years)
    
    def _run_single_mode_simulation(self, years: int) -> Dict[str, Any]:
        """Run simulation in single mode.
        
        Args:
            years: Number of years to simulate
            
        Returns:
            Simulation results
        """
        if self.current_mode == SimulationMode.NATURAL_POPULATION:
            if not self.natural_simulation:
                raise ValueError("Natural population simulation not initialized")
            
            self.logger.info("Running natural population simulation")
            self.natural_simulation.initialize_population(self.config.natural_population_size)
            results = self.natural_simulation.run(years)
            
        elif self.current_mode == SimulationMode.BIRTH_COHORT:
            if not self.birth_cohort_simulation:
                raise ValueError("Birth cohort simulation not initialized")
            
            self.logger.info("Running birth cohort simulation")
            results = self._run_birth_cohort_years(years)
            
        else:
            raise ValueError(f"Unsupported simulation mode: {self.current_mode}")
        
        self.results = results
        return results
    
    def _run_with_mode_switching(self, years: int) -> Dict[str, Any]:
        """Run simulation with mode switching.
        
        Args:
            years: Number of years to simulate
            
        Returns:
            Combined simulation results
        """
        switch_year = self.config.switch_year
        if switch_year >= years:
            # No switching needed, run in primary mode
            return self._run_single_mode_simulation(years)
        
        self.logger.info(f"Running simulation with mode switch at year {switch_year}")
        
        # Run first phase in primary mode
        self.logger.info(f"Phase 1: {self.current_mode.value} for {switch_year} years")
        phase1_results = self._run_single_mode_simulation(switch_year)
        
        # Switch to secondary mode
        old_mode = self.current_mode
        self.current_mode = self.config.secondary_mode
        self.current_year = switch_year
        
        self.logger.info(f"Switching from {old_mode.value} to {self.current_mode.value}")
        
        # Transfer population if needed
        self._transfer_population_between_modes(old_mode, self.current_mode)
        
        # Run second phase
        remaining_years = years - switch_year
        self.logger.info(f"Phase 2: {self.current_mode.value} for {remaining_years} years")
        phase2_results = self._run_single_mode_simulation(remaining_years)
        
        # Combine results
        combined_results = self._combine_simulation_results(phase1_results, phase2_results)
        self.results = combined_results
        return combined_results
    
    def _run_hybrid_simulation(self, years: int) -> Dict[str, Any]:
        """Run hybrid simulation combining both architectures.
        
        Args:
            years: Number of years to simulate
            
        Returns:
            Combined simulation results
        """
        self.logger.info("Running hybrid simulation")
        
        if not self.natural_simulation or not self.birth_cohort_simulation:
            raise ValueError("Both simulation engines required for hybrid mode")
        
        # Calculate population sizes
        total_natural_pop = int(self.config.natural_population_size * self.config.population_split_ratio)
        annual_births = int(self.config.annual_birth_cohort_size * (1 - self.config.population_split_ratio))
        
        # Initialize natural population
        self.natural_simulation.initialize_population(total_natural_pop)
        
        # Run simulations in parallel
        natural_results = self.natural_simulation.run(years)
        birth_cohort_results = self._run_birth_cohort_years(years, annual_births)
        
        # Combine results
        combined_results = self._combine_simulation_results(natural_results, birth_cohort_results)
        self.results = combined_results
        return combined_results
    
    def _run_birth_cohort_years(self, years: int, annual_births: Optional[int] = None) -> Dict[str, Any]:
        """Run birth cohort simulation for specified years.
        
        Args:
            years: Number of years to simulate
            annual_births: Number of births per year (optional)
            
        Returns:
            Birth cohort simulation results
        """
        if not self.birth_cohort_simulation:
            raise ValueError("Birth cohort simulation not initialized")
        
        births_per_year = annual_births or self.config.annual_birth_cohort_size
        
        for year in range(years):
            self.birth_cohort_simulation.simulate_year(self.current_year + year)
            
        return self.birth_cohort_simulation.get_results()
    
    def _transfer_population_between_modes(self, from_mode: SimulationMode, to_mode: SimulationMode) -> None:
        """Transfer population data between simulation modes.
        
        Args:
            from_mode: Source simulation mode
            to_mode: Target simulation mode
        """
        self.logger.info(f"Transferring population from {from_mode.value} to {to_mode.value}")
        
        if from_mode == SimulationMode.NATURAL_POPULATION and to_mode == SimulationMode.BIRTH_COHORT:
            # Transfer existing patients to birth cohort structure
            if self.natural_simulation and self.birth_cohort_simulation:
                patients = self.natural_simulation.patients
                self.birth_cohort_simulation.import_existing_patients(patients, self.current_year)
                
        elif from_mode == SimulationMode.BIRTH_COHORT and to_mode == SimulationMode.NATURAL_POPULATION:
            # Transfer birth cohort patients to natural population
            if self.birth_cohort_simulation and self.natural_simulation:
                all_patients = self.birth_cohort_simulation.get_all_patients()
                self.natural_simulation.import_patients(all_patients)
    
    def _combine_simulation_results(self, results1: Dict[str, Any], results2: Dict[str, Any]) -> Dict[str, Any]:
        """Combine results from two simulation phases.
        
        Args:
            results1: Results from first simulation phase
            results2: Results from second simulation phase
            
        Returns:
            Combined results
        """
        combined = {
            'phase1_results': results1,
            'phase2_results': results2,
            'combined_metrics': {},
            'architecture_info': {
                'primary_mode': self.config.primary_mode.value,
                'secondary_mode': self.config.secondary_mode.value if self.config.secondary_mode else None,
                'switch_year': self.config.switch_year
            }
        }
        
        # Combine key metrics
        for key in ['total_patients', 'cancer_cases', 'deaths', 'screenings_performed']:
            if key in results1 and key in results2:
                combined['combined_metrics'][key] = results1[key] + results2[key]
            elif key in results1:
                combined['combined_metrics'][key] = results1[key]
            elif key in results2:
                combined['combined_metrics'][key] = results2[key]
        
        return combined
    
    def get_current_mode(self) -> SimulationMode:
        """Get current simulation mode.
        
        Returns:
            Current simulation mode
        """
        return self.current_mode
    
    def switch_mode(self, new_mode: SimulationMode) -> None:
        """Manually switch simulation mode.
        
        Args:
            new_mode: New simulation mode to switch to
        """
        if new_mode == self.current_mode:
            self.logger.warning(f"Already in mode {new_mode.value}")
            return
        
        old_mode = self.current_mode
        self.current_mode = new_mode
        
        self.logger.info(f"Manually switched from {old_mode.value} to {new_mode.value}")
        
        # Transfer population if simulations are running
        if self.current_year > 0:
            self._transfer_population_between_modes(old_mode, new_mode)
    
    def get_simulation_summary(self) -> Dict[str, Any]:
        """Get summary of simulation configuration and results.
        
        Returns:
            Simulation summary
        """
        return {
            'configuration': {
                'primary_mode': self.config.primary_mode.value,
                'secondary_mode': self.config.secondary_mode.value if self.config.secondary_mode else None,
                'switch_year': self.config.switch_year,
                'natural_population_size': self.config.natural_population_size,
                'annual_birth_cohort_size': self.config.annual_birth_cohort_size
            },
            'current_state': {
                'current_mode': self.current_mode.value,
                'current_year': self.current_year
            },
            'results_available': bool(self.results)
        }
