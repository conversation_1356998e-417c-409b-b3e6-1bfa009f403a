name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]
        pip install pytest-xdist  # For parallel testing
    
    - name: Lint with flake8
      run: |
        flake8 cmost/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 cmost/ --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics
    
    - name: Format check with black
      run: |
        black --check --diff cmost/
    
    - name: Type check with mypy
      run: |
        mypy cmost/ --ignore-missing-imports
    
    - name: Test with pytest
      run: |
        pytest tests/ -v --cov=cmost --cov-report=xml --cov-report=term-missing -n auto
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  performance-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]
    
    - name: Run performance tests
      run: |
        pytest tests/ -m performance --tb=short
    
    - name: Benchmark simulation performance
      run: |
        python -c "
        from cmost.core.simulation import Simulation
        from cmost.config.settings import Settings
        import time
        
        settings = Settings()
        settings.set('Number_patients', 1000)
        
        start_time = time.time()
        sim = Simulation(settings)
        sim.initialize_population()
        results = sim.run(years=10)
        end_time = time.time()
        
        print(f'Simulation time: {end_time - start_time:.2f} seconds')
        print(f'Patients processed: {len(sim.patients)}')
        print(f'Performance: {len(sim.patients)/(end_time - start_time):.1f} patients/second')
        "

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Bandit security scan
      run: |
        pip install bandit
        bandit -r cmost/ -f json -o bandit-report.json || true
    
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      with:
        name: bandit-report
        path: bandit-report.json

  build-docs:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[docs]
    
    - name: Build documentation
      run: |
        cd docs
        make html
    
    - name: Upload documentation
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: docs/_build/html/

  release:
    runs-on: ubuntu-latest
    needs: [test, performance-test, security-scan]
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    
    - name: Build package
      run: |
        python -m build
    
    - name: Publish to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        twine upload dist/*
