"""
Unit tests for adaptive calibration functionality in CMOST.
"""

import unittest
import numpy as np

from cmost.ml.adaptive_calibration import (
    AdaptiveCalibrator,
    CalibrationData,
    ParameterSpace,
    CalibrationTarget,
    OptimizationMethod,
    RandomForestOptimizer
)


class TestCalibrationData(unittest.TestCase):
    """Test cases for CalibrationData class."""
    
    def test_calibration_data_creation(self):
        """Test calibration data creation."""
        observed = [1.0, 2.0, 3.0, 4.0, 5.0]
        simulated = [1.1, 1.9, 3.2, 3.8, 5.1]
        
        data = CalibrationData(
            target_name="cancer_incidence",
            observed_values=observed,
            simulated_values=simulated
        )
        
        self.assertEqual(data.target_name, "cancer_incidence")
        self.assertEqual(len(data.observed_values), 5)
        self.assertEqual(len(data.simulated_values), 5)
    
    def test_calibration_data_with_features(self):
        """Test calibration data with features."""
        observed = [1.0, 2.0, 3.0]
        simulated = [1.1, 1.9, 3.2]
        features = [[0.5, 0.3], [0.7, 0.4], [0.9, 0.6]]
        feature_names = ["age", "risk_score"]
        
        data = CalibrationData(
            target_name="polyp_prevalence",
            observed_values=observed,
            simulated_values=simulated,
            features=features,
            feature_names=feature_names
        )
        
        self.assertEqual(len(data.features), 3)
        self.assertEqual(len(data.feature_names), 2)
        self.assertEqual(data.feature_names[0], "age")
    
    def test_calibration_data_validation(self):
        """Test calibration data validation."""
        # Mismatched lengths should raise error
        with self.assertRaises(ValueError):
            CalibrationData(
                target_name="test",
                observed_values=[1.0, 2.0],
                simulated_values=[1.0, 2.0, 3.0]
            )
        
        # Mismatched features length should raise error
        with self.assertRaises(ValueError):
            CalibrationData(
                target_name="test",
                observed_values=[1.0, 2.0],
                simulated_values=[1.0, 2.0],
                features=[[0.5], [0.7], [0.9]]  # 3 features for 2 values
            )
    
    def test_residuals_calculation(self):
        """Test residuals calculation."""
        data = CalibrationData(
            target_name="test",
            observed_values=[1.0, 2.0, 3.0],
            simulated_values=[1.1, 1.8, 3.2]
        )
        
        residuals = data.get_residuals()
        expected = [-0.1, 0.2, -0.2]
        
        for r, e in zip(residuals, expected):
            self.assertAlmostEqual(r, e, places=6)
    
    def test_error_metrics(self):
        """Test error metric calculations."""
        data = CalibrationData(
            target_name="test",
            observed_values=[1.0, 2.0, 3.0],
            simulated_values=[1.1, 1.8, 3.2]
        )
        
        mse = data.get_mse()
        mae = data.get_mae()
        
        # MSE = mean([0.01, 0.04, 0.04]) = 0.03
        self.assertAlmostEqual(mse, 0.03, places=6)
        
        # MAE = mean([0.1, 0.2, 0.2]) = 0.1667
        self.assertAlmostEqual(mae, 0.1667, places=4)


class TestParameterSpace(unittest.TestCase):
    """Test cases for ParameterSpace class."""
    
    def test_parameter_space_creation(self):
        """Test parameter space creation."""
        param = ParameterSpace(
            name="progression_rate",
            min_value=0.1,
            max_value=0.5,
            current_value=0.3
        )
        
        self.assertEqual(param.name, "progression_rate")
        self.assertEqual(param.min_value, 0.1)
        self.assertEqual(param.max_value, 0.5)
        self.assertEqual(param.current_value, 0.3)
        self.assertEqual(param.parameter_type, "continuous")
    
    def test_discrete_parameter_space(self):
        """Test discrete parameter space."""
        param = ParameterSpace(
            name="screening_interval",
            min_value=1,
            max_value=10,
            current_value=5,
            parameter_type="discrete",
            discrete_values=[1, 2, 3, 5, 10]
        )
        
        self.assertEqual(param.parameter_type, "discrete")
        self.assertEqual(len(param.discrete_values), 5)
    
    def test_random_sampling(self):
        """Test random sampling from parameter space."""
        # Set random seed for reproducible tests
        np.random.seed(42)
        
        # Continuous parameter
        param_continuous = ParameterSpace(
            name="test_param",
            min_value=0.0,
            max_value=1.0,
            current_value=0.5
        )
        
        samples = [param_continuous.sample_random() for _ in range(100)]
        
        # All samples should be within bounds
        self.assertTrue(all(0.0 <= s <= 1.0 for s in samples))
        
        # Should have some variation
        self.assertGreater(np.std(samples), 0.1)
    
    def test_discrete_sampling(self):
        """Test random sampling from discrete parameter space."""
        np.random.seed(42)
        
        param_discrete = ParameterSpace(
            name="test_discrete",
            min_value=1,
            max_value=5,
            current_value=3,
            parameter_type="discrete",
            discrete_values=[1, 2, 3, 4, 5]
        )
        
        samples = [param_discrete.sample_random() for _ in range(100)]
        
        # All samples should be from discrete values
        self.assertTrue(all(s in [1, 2, 3, 4, 5] for s in samples))
    
    def test_value_clipping(self):
        """Test value clipping to parameter bounds."""
        param = ParameterSpace(
            name="test_param",
            min_value=0.0,
            max_value=1.0,
            current_value=0.5
        )
        
        # Test clipping values outside bounds
        self.assertEqual(param.clip_value(-0.5), 0.0)
        self.assertEqual(param.clip_value(1.5), 1.0)
        self.assertEqual(param.clip_value(0.7), 0.7)
    
    def test_discrete_value_clipping(self):
        """Test value clipping for discrete parameters."""
        param = ParameterSpace(
            name="test_discrete",
            min_value=1,
            max_value=5,
            current_value=3,
            parameter_type="discrete",
            discrete_values=[1, 2, 3, 4, 5]
        )
        
        # Should find closest discrete value
        self.assertEqual(param.clip_value(1.4), 1)
        self.assertEqual(param.clip_value(1.6), 2)
        self.assertEqual(param.clip_value(4.8), 5)


class TestRandomForestOptimizer(unittest.TestCase):
    """Test cases for RandomForestOptimizer class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parameter_spaces = [
            ParameterSpace("param1", 0.0, 1.0, 0.5),
            ParameterSpace("param2", -1.0, 1.0, 0.0)
        ]
        self.optimizer = RandomForestOptimizer(self.parameter_spaces)
    
    def test_optimizer_initialization(self):
        """Test optimizer initialization."""
        self.assertEqual(len(self.optimizer.parameter_spaces), 2)
        self.assertEqual(self.optimizer.n_estimators, 100)
    
    def test_random_parameter_sampling(self):
        """Test random parameter sampling."""
        np.random.seed(42)
        
        params = self.optimizer._sample_random_parameters()
        
        self.assertIn("param1", params)
        self.assertIn("param2", params)
        self.assertTrue(0.0 <= params["param1"] <= 1.0)
        self.assertTrue(-1.0 <= params["param2"] <= 1.0)
    
    def test_optimization_simple_function(self):
        """Test optimization on simple quadratic function."""
        np.random.seed(42)
        
        # Simple quadratic function with minimum at (0.3, 0.7)
        def objective(params):
            x = params["param1"]
            y = params["param2"]
            return (x - 0.3)**2 + (y - 0.7)**2
        
        # Run optimization
        result = self.optimizer.optimize(objective, max_iterations=20)
        
        # Should find parameters close to optimum
        self.assertIsInstance(result, dict)
        self.assertIn("param1", result)
        self.assertIn("param2", result)
        
        # Check if reasonably close to optimum (allowing for randomness)
        final_error = objective(result)
        self.assertLess(final_error, 0.5)  # Should be better than random


class TestAdaptiveCalibrator(unittest.TestCase):
    """Test cases for AdaptiveCalibrator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parameter_spaces = [
            ParameterSpace("progression_rate", 0.1, 0.5, 0.3),
            ParameterSpace("polyp_rate", 0.01, 0.1, 0.05)
        ]
        self.calibrator = AdaptiveCalibrator(self.parameter_spaces)
    
    def test_calibrator_initialization(self):
        """Test calibrator initialization."""
        self.assertEqual(len(self.calibrator.parameter_spaces), 2)
        self.assertEqual(len(self.calibrator.current_parameters), 2)
        self.assertEqual(self.calibrator.current_parameters["progression_rate"], 0.3)
        self.assertEqual(self.calibrator.current_parameters["polyp_rate"], 0.05)
    
    def test_calibration_with_single_target(self):
        """Test calibration with single target."""
        np.random.seed(42)
        
        # Create calibration data
        calibration_data = [
            CalibrationData(
                target_name="cancer_incidence",
                observed_values=[0.05, 0.08, 0.12, 0.15],
                simulated_values=[0.04, 0.09, 0.11, 0.16]
            )
        ]
        
        # Run calibration
        result = self.calibrator.calibrate(
            calibration_data, 
            max_iterations=10,
            target_tolerance=0.01
        )
        
        self.assertIsInstance(result, dict)
        self.assertIn("progression_rate", result)
        self.assertIn("polyp_rate", result)
        
        # Check that parameters are within bounds
        for param_name, value in result.items():
            param_space = next(ps for ps in self.parameter_spaces if ps.name == param_name)
            self.assertGreaterEqual(value, param_space.min_value)
            self.assertLessEqual(value, param_space.max_value)
    
    def test_calibration_with_multiple_targets(self):
        """Test calibration with multiple targets."""
        np.random.seed(42)
        
        # Create multiple calibration datasets
        calibration_data = [
            CalibrationData(
                target_name="cancer_incidence",
                observed_values=[0.05, 0.08, 0.12],
                simulated_values=[0.04, 0.09, 0.11]
            ),
            CalibrationData(
                target_name="polyp_prevalence",
                observed_values=[0.20, 0.25, 0.30],
                simulated_values=[0.18, 0.27, 0.32]
            )
        ]
        
        # Run calibration
        result = self.calibrator.calibrate(
            calibration_data,
            max_iterations=10
        )
        
        self.assertIsInstance(result, dict)
        self.assertEqual(len(result), 2)
        
        # Check calibration history
        self.assertEqual(len(self.calibrator.calibration_history), 1)
        self.assertIn('error', self.calibrator.calibration_history[0])
    
    def test_calibration_summary(self):
        """Test calibration summary generation."""
        # Initially no calibrations
        summary = self.calibrator.get_calibration_summary()
        self.assertEqual(summary["total_calibrations"], 0)
        
        # Run a calibration
        calibration_data = [
            CalibrationData(
                target_name="test",
                observed_values=[1.0, 2.0],
                simulated_values=[1.1, 1.9]
            )
        ]
        
        self.calibrator.calibrate(calibration_data, max_iterations=5)
        
        # Check summary after calibration
        summary = self.calibrator.get_calibration_summary()
        self.assertEqual(summary["total_calibrations"], 1)
        self.assertIn("latest_error", summary)
        self.assertIn("best_error", summary)
        self.assertIn("current_parameters", summary)
    
    def test_parameter_bounds_update(self):
        """Test updating parameter bounds."""
        # Update bounds for progression_rate
        self.calibrator.update_parameter_bounds("progression_rate", 0.2, 0.4)
        
        # Find the updated parameter space
        param_space = next(ps for ps in self.calibrator.parameter_spaces 
                          if ps.name == "progression_rate")
        
        self.assertEqual(param_space.min_value, 0.2)
        self.assertEqual(param_space.max_value, 0.4)
    
    def test_parameter_effect_simulation(self):
        """Test parameter effect simulation."""
        params = {"progression_rate": 0.4, "polyp_rate": 0.08}
        
        # Test effect on cancer incidence
        effect = self.calibrator._simulate_parameter_effect(params, "cancer_incidence")
        self.assertIsInstance(effect, float)
        self.assertGreater(effect, 0)
        
        # Test effect on polyp prevalence
        effect = self.calibrator._simulate_parameter_effect(params, "polyp_prevalence")
        self.assertIsInstance(effect, float)
        self.assertGreater(effect, 0)


if __name__ == '__main__':
    unittest.main()
