# CMOST 快速入门指南

## 5分钟快速体验

### 1. 安装CMOST

```bash
# 克隆项目
git clone https://github.com/your-repo/cmost.git
cd cmost

# 安装依赖
pip install -r requirements.txt

# 安装CMOST
pip install -e .
```

### 2. 运行第一个仿真

```python
# 创建文件: my_first_simulation.py
from cmost.core.simulation import Simulation
from cmost.config.settings import Settings

# 创建基本设置
settings = Settings()
settings.set('Number_patients', 1000)  # 1000个患者
settings.set('Simulation.EnableMultiprocessing', True)

# 创建并运行仿真
simulation = Simulation(settings)
print("开始仿真...")
results = simulation.run(years=10)

# 查看结果
stats = simulation.get_summary_statistics()
print(f"仿真完成！")
print(f"患者总数: {len(simulation.patients)}")
print(f"癌症病例: {stats.get('cancer_cases', 0)}")
print(f"筛查次数: {stats.get('total_screenings', 0)}")
```

运行仿真：
```bash
python my_first_simulation.py
```

### 3. 使用命令行工具

```bash
# 快速仿真
cmost simulate --patients 5000 --years 20 --output quick_results.json

# 查看结果
cmost analyze quick_results.json --summary
```

## 10分钟深入体验

### 1. 比较不同筛查策略

```python
# 创建文件: compare_strategies.py
from cmost.core.simulation import Simulation
from cmost.config.settings import Settings
from cmost.utils.parallel import parallel_simulation

def run_strategy_simulation(strategy_name, num_patients=5000):
    """运行特定筛查策略的仿真"""
    settings = Settings()
    settings.set('Number_patients', num_patients)
    settings.set('Screening.DefaultStrategy', strategy_name)
    settings.set('Screening.ParticipationRate', 0.65)
    
    simulation = Simulation(settings)
    results = simulation.run(years=30)
    
    stats = simulation.get_summary_statistics()
    return {
        'strategy': strategy_name,
        'cancer_cases': stats.get('cancer_cases', 0),
        'cancer_deaths': stats.get('cancer_deaths', 0),
        'total_screenings': stats.get('total_screenings', 0),
        'cost_per_patient': stats.get('average_cost_per_patient', 0)
    }

# 定义要比较的策略
strategies = [
    'colonoscopy_10year',
    'fit_annual',
    'sigmoidoscopy_5year',
    'no_screening'
]

print("比较不同筛查策略...")

# 并行运行所有策略
parameter_sets = [{'strategy_name': s} for s in strategies]
results = parallel_simulation(
    lambda strategy_name: run_strategy_simulation(strategy_name),
    parameter_sets,
    max_workers=4
)

# 显示比较结果
print("\n筛查策略比较结果:")
print("-" * 80)
print(f"{'策略':<20} {'癌症病例':<10} {'癌症死亡':<10} {'筛查次数':<10} {'人均成本':<10}")
print("-" * 80)

for result in results:
    if result:
        print(f"{result['strategy']:<20} "
              f"{result['cancer_cases']:<10} "
              f"{result['cancer_deaths']:<10} "
              f"{result['total_screenings']:<10} "
              f"${result['cost_per_patient']:<9.0f}")
```

### 2. 性能优化演示

```python
# 创建文件: performance_demo.py
from cmost.utils.performance import performance_monitor, profile
from cmost.utils.cache import cached
from cmost.utils.memory import memory_monitor
import time
import numpy as np

# 使用性能监控
@profile("expensive_calculation")
def expensive_calculation(n):
    """模拟耗时计算"""
    result = 0
    for i in range(n):
        result += np.sqrt(i) * np.sin(i)
    return result

# 使用缓存优化
@cached(maxsize=100)
def fibonacci(n):
    """缓存的斐波那契计算"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print("=== 性能优化演示 ===")

# 测试性能监控
with performance_monitor.monitor("demo_block"):
    result1 = expensive_calculation(100000)
    result2 = fibonacci(30)

# 获取性能报告
metrics = performance_monitor.get_all_metrics()
for name, stats in metrics.items():
    print(f"{name}: 执行时间 {stats['execution_time']:.3f}s, "
          f"内存使用 {stats['memory_usage']:.2f}MB")

# 测试内存监控
with memory_monitor(threshold_mb=100):
    large_array = np.random.random((1000, 1000))
    print(f"创建大数组: {large_array.shape}")

print("性能优化演示完成！")
```

### 3. 图形界面体验

```python
# 创建文件: gui_demo.py
import tkinter as tk
from cmost.ui.main_window import CMOSTMainWindow

def launch_gui():
    """启动CMOST图形界面"""
    root = tk.Tk()
    root.title("CMOST - 结直肠癌筛查仿真工具")
    root.geometry("1200x800")
    
    # 创建主应用
    app = CMOSTMainWindow(root)
    
    print("CMOST图形界面已启动！")
    print("您可以通过界面进行以下操作：")
    print("1. 配置仿真参数")
    print("2. 选择筛查策略")
    print("3. 运行仿真")
    print("4. 查看结果和图表")
    
    root.mainloop()

if __name__ == "__main__":
    launch_gui()
```

## 30分钟完整体验

### 1. 高级仿真配置

```python
# 创建文件: advanced_simulation.py
from cmost.core.simulation import Simulation
from cmost.core.dual_architecture import DualArchitectureManager, ArchitectureConfig
from cmost.config.settings import Settings
from cmost.utils.visualization import plot_results, create_summary_report

# 配置双重架构仿真
def run_advanced_simulation():
    """运行高级双重架构仿真"""
    
    # 配置双重架构
    arch_config = ArchitectureConfig(
        natural_population_size=50000,      # 自然人群规模
        annual_birth_cohort_size=5000,      # 年出生队列规模
        population_split_ratio=0.7,         # 人群分配比例
        simulation_years=50                  # 仿真年数
    )
    
    # 创建双重架构管理器
    manager = DualArchitectureManager(arch_config)
    
    print("开始双重架构仿真...")
    print(f"自然人群: {arch_config.natural_population_size}")
    print(f"出生队列: {arch_config.annual_birth_cohort_size}/年")
    
    # 运行仿真
    results = manager.run_combined_simulation(years=50)
    
    # 分析结果
    print("\n仿真结果分析:")
    print(f"自然人群癌症发病率: {results['natural']['cancer_incidence_rate']:.2f}/100,000")
    print(f"出生队列癌症发病率: {results['birth_cohort']['cancer_incidence_rate']:.2f}/100,000")
    print(f"综合筛查效果: {results['combined']['screening_effectiveness']:.2%}")
    
    return results

# 运行高级仿真
if __name__ == "__main__":
    results = run_advanced_simulation()
```

### 2. 机器学习校准

```python
# 创建文件: ml_calibration_demo.py
from cmost.ml.integrated_calibration import run_integrated_calibration
from cmost.calibration.deep_neural_network import DNNCalibrator
import numpy as np

def demonstrate_ml_calibration():
    """演示机器学习校准功能"""
    
    # 模拟目标数据（实际使用中应该是真实的流行病学数据）
    target_data = {
        'cancer_incidence_by_age': {
            50: 50.2, 55: 75.8, 60: 120.5, 65: 180.3, 70: 250.7
        },
        'cancer_mortality_by_age': {
            50: 15.1, 55: 25.4, 60: 42.8, 65: 68.9, 70: 95.2
        },
        'polyp_prevalence_by_age': {
            50: 0.25, 55: 0.35, 60: 0.45, 65: 0.55, 70: 0.65
        }
    }
    
    print("=== 机器学习校准演示 ===")
    print("目标数据已加载")
    
    # 定义参数空间
    parameter_space = {
        'polyp_incidence_rate': (0.01, 0.05),
        'cancer_progression_rate': (0.02, 0.08),
        'screening_sensitivity': (0.7, 0.95)
    }
    
    print("开始集成校准...")
    
    # 运行集成校准
    calibration_result = run_integrated_calibration(
        target_data=target_data,
        parameter_space=parameter_space,
        methods=['neural_network', 'random_forest', 'bayesian'],
        auto_select=True,
        max_iterations=50
    )
    
    print(f"最佳校准方法: {calibration_result.best_method}")
    print(f"校准精度: {calibration_result.best_score:.4f}")
    print("最佳参数:")
    for param, value in calibration_result.best_parameters.items():
        print(f"  {param}: {value:.4f}")
    
    return calibration_result

if __name__ == "__main__":
    result = demonstrate_ml_calibration()
```

### 3. 集群计算演示

```python
# 创建文件: cluster_demo.py
from cmost.cluster.job_manager import JobManager
from cmost.config.settings import Settings
import time

def demonstrate_cluster_computing():
    """演示集群计算功能"""
    
    # 创建作业管理器（本地模式用于演示）
    job_manager = JobManager(cluster_type='local')
    
    print("=== 集群计算演示 ===")
    
    # 创建基础设置
    base_settings = Settings()
    base_settings.set('Number_patients', 10000)
    base_settings.set('Simulation.EnableMultiprocessing', True)
    
    # 定义参数扫描空间
    parameter_space = {
        'Screening.ParticipationRate': [0.5, 0.65, 0.8],
        'Screening.DefaultStrategy': ['colonoscopy_10year', 'fit_annual', 'sigmoidoscopy_5year'],
        'ModelParameters.PolypIncidenceRate': [0.02, 0.03, 0.04]
    }
    
    print("提交参数扫描作业...")
    
    # 提交参数扫描作业
    job_ids = job_manager.submit_parameter_sweep(
        base_settings.to_dict(),
        parameter_space,
        job_name_prefix='cmost_param_sweep',
        time_limit='02:00:00',
        memory='4G',
        num_cores=2
    )
    
    print(f"已提交 {len(job_ids)} 个作业")
    
    # 监控作业状态
    print("监控作业状态...")
    completed_jobs = 0
    
    while completed_jobs < len(job_ids):
        time.sleep(5)  # 等待5秒
        
        for job_id in job_ids:
            status = job_manager.get_job_status(job_id)
            if status['status'] == 'completed' and job_id not in completed_jobs:
                completed_jobs += 1
                print(f"作业 {job_id} 完成 ({completed_jobs}/{len(job_ids)})")
    
    print("所有作业完成！")
    
    # 收集结果
    print("收集结果...")
    all_results = job_manager.collect_results(job_ids)
    
    print(f"成功收集 {len(all_results)} 个结果")
    
    return all_results

if __name__ == "__main__":
    results = demonstrate_cluster_computing()
```

## 常见问题解答

### Q1: 如何提高仿真速度？

**A:** 启用并行处理和性能优化：
```python
settings.set('Simulation.EnableMultiprocessing', True)
settings.set('Simulation.NumProcesses', 8)  # 根据CPU核心数调整
```

### Q2: 如何减少内存使用？

**A:** 使用内存优化功能：
```python
from cmost.utils.memory import memory_monitor

with memory_monitor(threshold_mb=1000):
    # 您的仿真代码
    simulation.run(years=50)
```

### Q3: 如何自定义筛查策略？

**A:** 继承基础策略类：
```python
from cmost.screening.base import ScreeningStrategy

class MyCustomStrategy(ScreeningStrategy):
    def should_screen(self, patient, current_year):
        # 自定义筛查逻辑
        return custom_logic(patient, current_year)
```

### Q4: 如何保存和加载仿真结果？

**A:** 使用内置的保存/加载功能：
```python
# 保存结果
simulation.save_results('my_results.json')

# 加载结果
loaded_simulation = Simulation.load_results('my_results.json')
```

### Q5: 如何获取技术支持？

**A:** 
- 查看文档: `docs/`目录
- 运行示例: `examples/`目录
- 提交问题: GitHub Issues
- 邮箱支持: <EMAIL>

## 下一步

恭喜您完成了CMOST快速入门！接下来您可以：

1. **深入学习**: 阅读完整文档 `README_COMPLETE.md`
2. **性能优化**: 查看性能优化指南 `docs/performance_optimization.md`
3. **高级功能**: 探索机器学习校准和集群计算
4. **自定义开发**: 根据需求扩展和定制功能
5. **社区参与**: 贡献代码或反馈问题

祝您使用愉快！
