"""
性能优化测试模块

测试CMOST性能优化功能的正确性和效果。
"""

import pytest
import time
import numpy as np
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from cmost.utils.performance import (
    PerformanceMonitor, PerformanceMetrics, profile, memory_limit
)
from cmost.utils.parallel import (
    ParallelProcessor, ParallelConfig, parallel_map, SimulationParallelizer
)
from cmost.utils.memory import (
    MemoryManager, ObjectPool, ArrayPool, memory_monitor
)
from cmost.utils.cache import (
    LRUCache, PersistentCache, MultiLevelCache, cached
)


class TestPerformanceMonitoring:
    """性能监控测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.monitor = PerformanceMonitor()
    
    def test_performance_monitor_basic(self):
        """测试基本性能监控"""
        with self.monitor.monitor("test_operation"):
            time.sleep(0.1)  # 模拟耗时操作
        
        metrics = self.monitor.get_metrics("test_operation")
        assert metrics.execution_time >= 0.1
        assert metrics.function_calls == 1
    
    def test_profile_decorator(self):
        """测试性能分析装饰器"""
        @profile("test_function")
        def test_function(n):
            return sum(range(n))
        
        result = test_function(1000)
        assert result == sum(range(1000))
        
        # 检查性能指标是否被记录
        from cmost.utils.performance import performance_monitor
        metrics = performance_monitor.get_metrics("test_function")
        assert metrics.function_calls >= 1
    
    def test_memory_limit_context(self):
        """测试内存限制上下文管理器"""
        with memory_limit(100):  # 100MB限制
            # 创建小数组，不应触发警告
            arr = np.zeros((100, 100))
            assert arr.size == 10000
    
    def test_metrics_serialization(self):
        """测试性能指标序列化"""
        with self.monitor.monitor("serialization_test"):
            time.sleep(0.05)
        
        # 测试保存和加载
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            temp_path = f.name
        
        try:
            self.monitor.save_metrics(temp_path)
            
            # 创建新的监控器并加载指标
            new_monitor = PerformanceMonitor()
            new_monitor.load_metrics(temp_path)
            
            metrics = new_monitor.get_metrics("serialization_test")
            assert metrics.execution_time > 0
        finally:
            Path(temp_path).unlink(missing_ok=True)


class TestParallelProcessing:
    """并行处理测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.config = ParallelConfig(max_workers=2, use_processes=False)
        self.processor = ParallelProcessor(self.config)
    
    def test_parallel_map_basic(self):
        """测试基本并行映射"""
        def square(x):
            return x * x
        
        data = [1, 2, 3, 4, 5]
        results = self.processor.map(square, data)
        
        expected = [1, 4, 9, 16, 25]
        assert results == expected
    
    def test_parallel_map_chunked(self):
        """测试分块并行映射"""
        def double(x):
            return x * 2
        
        data = list(range(100))
        results = self.processor.map_chunked(double, data, chunk_size=10)
        
        expected = [x * 2 for x in data]
        assert len(results) == len(expected)
        assert all(r == e for r, e in zip(results, expected) if r is not None)
    
    def test_parallel_reduce(self):
        """测试并行归约"""
        def add(a, b):
            return a + b
        
        data = list(range(1, 11))  # 1到10
        result = self.processor.reduce(add, data, initializer=0)
        
        expected = sum(data)
        assert result == expected
    
    def test_simulation_parallelizer(self):
        """测试仿真并行化器"""
        parallelizer = SimulationParallelizer(self.config)
        
        def mock_simulation(param1, param2):
            return param1 + param2
        
        parameter_sets = [
            {'param1': 1, 'param2': 2},
            {'param1': 3, 'param2': 4},
            {'param1': 5, 'param2': 6}
        ]
        
        results = parallelizer.run_parallel_simulations(
            mock_simulation, parameter_sets
        )
        
        expected = [3, 7, 11]
        assert results == expected
    
    def test_parallel_map_function(self):
        """测试便捷并行映射函数"""
        def cube(x):
            return x ** 3
        
        data = [1, 2, 3, 4]
        results = parallel_map(cube, data, max_workers=2, use_processes=False)
        
        expected = [1, 8, 27, 64]
        assert results == expected


class TestMemoryManagement:
    """内存管理测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.memory_manager = MemoryManager()
    
    def test_object_pool_basic(self):
        """测试基本对象池功能"""
        class TestObject:
            def __init__(self):
                self.value = 0
            
            def reset(self):
                self.value = 0
        
        pool = self.memory_manager.create_pool(
            'test_pool',
            TestObject,
            reset_func=lambda obj: obj.reset(),
            max_size=10,
            initial_size=3
        )
        
        # 测试获取和释放对象
        obj1 = pool.acquire()
        obj1.value = 42
        
        obj2 = pool.acquire()
        assert obj1 is not obj2
        
        pool.release(obj1)
        
        # 再次获取应该得到重置后的对象
        obj3 = pool.acquire()
        assert obj3.value == 0  # 应该被重置
    
    def test_array_pool(self):
        """测试数组池"""
        array_pool = ArrayPool(max_arrays=5)
        
        # 获取数组
        arr1 = array_pool.acquire((10, 10), dtype=np.float64)
        assert arr1.shape == (10, 10)
        assert arr1.dtype == np.float64
        
        # 修改数组
        arr1.fill(42)
        
        # 释放数组
        array_pool.release(arr1)
        
        # 再次获取应该得到清零的数组
        arr2 = array_pool.acquire((10, 10), dtype=np.float64)
        assert np.all(arr2 == 0)
    
    def test_memory_monitor_context(self):
        """测试内存监控上下文管理器"""
        with memory_monitor(threshold_mb=1000):
            # 创建小数组，不应触发警告
            arr = np.zeros((100, 100))
            assert arr.size == 10000
    
    def test_memory_usage_tracking(self):
        """测试内存使用跟踪"""
        usage = self.memory_manager.get_memory_usage()
        
        assert 'rss_mb' in usage
        assert 'vms_mb' in usage
        assert 'percent' in usage
        assert 'available_mb' in usage
        assert usage['rss_mb'] > 0


class TestCachingSystem:
    """缓存系统测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_lru_cache_basic(self):
        """测试基本LRU缓存"""
        cache = LRUCache(maxsize=3)
        
        # 添加项目
        cache.put('a', 1)
        cache.put('b', 2)
        cache.put('c', 3)
        
        # 测试获取
        assert cache.get('a') == 1
        assert cache.get('b') == 2
        assert cache.get('c') == 3
        assert cache.get('d') is None
        
        # 添加第四个项目，应该淘汰最久未使用的
        cache.put('d', 4)
        assert cache.get('a') is None  # 应该被淘汰
        assert cache.get('d') == 4
    
    def test_lru_cache_ttl(self):
        """测试LRU缓存TTL功能"""
        cache = LRUCache(maxsize=10, ttl=0.1)  # 0.1秒过期
        
        cache.put('key', 'value')
        assert cache.get('key') == 'value'
        
        # 等待过期
        time.sleep(0.15)
        assert cache.get('key') is None
    
    def test_persistent_cache(self):
        """测试持久化缓存"""
        cache = PersistentCache(self.temp_dir, max_size_mb=1)
        
        # 存储数据
        test_data = {'key': 'value', 'number': 42}
        cache.put('test_key', test_data)
        
        # 获取数据
        retrieved_data = cache.get('test_key')
        assert retrieved_data == test_data
        
        # 创建新的缓存实例，应该能读取持久化的数据
        cache2 = PersistentCache(self.temp_dir, max_size_mb=1)
        retrieved_data2 = cache2.get('test_key')
        assert retrieved_data2 == test_data
    
    def test_multilevel_cache(self):
        """测试多级缓存"""
        cache = MultiLevelCache(
            l1_size=2,
            l2_cache_dir=self.temp_dir,
            l2_size_mb=1
        )
        
        # 存储数据
        cache.put('key1', 'value1')
        cache.put('key2', 'value2')
        cache.put('key3', 'value3')  # 应该触发L1淘汰
        
        # 测试获取
        assert cache.get('key1') == 'value1'  # 应该从L2获取并提升到L1
        assert cache.get('key2') == 'value2'
        assert cache.get('key3') == 'value3'
    
    def test_cached_decorator(self):
        """测试缓存装饰器"""
        call_count = 0
        
        @cached(maxsize=10)
        def expensive_function(x):
            nonlocal call_count
            call_count += 1
            return x * x
        
        # 第一次调用
        result1 = expensive_function(5)
        assert result1 == 25
        assert call_count == 1
        
        # 第二次调用相同参数，应该使用缓存
        result2 = expensive_function(5)
        assert result2 == 25
        assert call_count == 1  # 没有增加
        
        # 调用不同参数
        result3 = expensive_function(6)
        assert result3 == 36
        assert call_count == 2


class TestIntegrationPerformance:
    """集成性能测试"""
    
    def test_performance_optimization_integration(self):
        """测试性能优化集成效果"""
        # 创建测试数据
        data_size = 1000
        test_data = list(range(data_size))
        
        def cpu_task(x):
            """CPU密集型任务"""
            return sum(i * i for i in range(x % 100 + 1))
        
        # 测试串行处理
        start_time = time.time()
        serial_results = [cpu_task(x) for x in test_data[:100]]
        serial_time = time.time() - start_time
        
        # 测试并行处理
        start_time = time.time()
        parallel_results = parallel_map(
            cpu_task, 
            test_data[:100], 
            max_workers=2,
            use_processes=False
        )
        parallel_time = time.time() - start_time
        
        # 验证结果正确性
        assert len(serial_results) == len(parallel_results)
        assert all(s == p for s, p in zip(serial_results, parallel_results) if p is not None)
        
        # 并行处理应该更快（在多核系统上）
        if parallel_time > 0:
            speedup = serial_time / parallel_time
            # 至少应该有一些性能提升
            assert speedup > 0.5  # 保守的性能提升期望
    
    def test_memory_optimization_effectiveness(self):
        """测试内存优化效果"""
        memory_manager = MemoryManager()
        
        class TestObject:
            def __init__(self):
                self.data = np.zeros(100)
            
            def reset(self):
                self.data.fill(0)
        
        # 创建对象池
        pool = memory_manager.create_pool(
            'perf_test_pool',
            TestObject,
            reset_func=lambda obj: obj.reset(),
            max_size=50,
            initial_size=10
        )
        
        # 测试对象池使用
        objects = []
        for _ in range(20):
            obj = pool.acquire()
            objects.append(obj)
        
        for obj in objects:
            pool.release(obj)
        
        # 检查统计信息
        stats = pool.get_stats()
        assert stats.pool_hits > 0  # 应该有缓存命中
        assert stats.hit_rate() > 0  # 命中率应该大于0


def run_benchmark():
    """运行性能基准测试"""
    print("=== CMOST 性能基准测试 ===")

    # 并行处理基准测试
    print("\n1. 并行处理基准测试")
    test_parallel = TestParallelProcessing()
    test_parallel.setup_method()

    data = list(range(1000))

    def benchmark_task(x):
        return sum(i * i for i in range(x % 50 + 1))

    # 串行基准
    start_time = time.time()
    serial_results = [benchmark_task(x) for x in data[:200]]
    serial_time = time.time() - start_time

    # 并行基准
    start_time = time.time()
    parallel_results = parallel_map(benchmark_task, data[:200], max_workers=4, use_processes=False)
    parallel_time = time.time() - start_time

    speedup = serial_time / parallel_time if parallel_time > 0 else 0
    print(f"串行时间: {serial_time:.3f}s")
    print(f"并行时间: {parallel_time:.3f}s")
    print(f"加速比: {speedup:.2f}x")

    # 缓存基准测试
    print("\n2. 缓存系统基准测试")

    @cached(maxsize=100)
    def fibonacci_cached(n):
        if n <= 1:
            return n
        return fibonacci_cached(n - 1) + fibonacci_cached(n - 2)

    def fibonacci_uncached(n):
        if n <= 1:
            return n
        return fibonacci_uncached(n - 1) + fibonacci_uncached(n - 2)

    # 测试缓存效果
    test_n = 25

    start_time = time.time()
    result_cached = fibonacci_cached(test_n)
    cached_time = time.time() - start_time

    start_time = time.time()
    result_uncached = fibonacci_uncached(test_n)
    uncached_time = time.time() - start_time

    cache_speedup = uncached_time / cached_time if cached_time > 0 else 0
    print(f"缓存版本时间: {cached_time:.3f}s")
    print(f"无缓存版本时间: {uncached_time:.3f}s")
    print(f"缓存加速比: {cache_speedup:.2f}x")

    # 内存管理基准测试
    print("\n3. 内存管理基准测试")
    memory_manager = MemoryManager()

    class BenchmarkObject:
        def __init__(self):
            self.data = np.random.random(100)

        def reset(self):
            self.data.fill(0)

    pool = memory_manager.create_pool(
        'benchmark_pool',
        BenchmarkObject,
        reset_func=lambda obj: obj.reset(),
        max_size=100,
        initial_size=20
    )

    # 对象池基准
    start_time = time.time()
    for _ in range(1000):
        obj = pool.acquire()
        obj.data[0] = 1.0
        pool.release(obj)
    pool_time = time.time() - start_time

    # 直接创建基准
    start_time = time.time()
    for _ in range(1000):
        obj = BenchmarkObject()
        obj.data[0] = 1.0
    direct_time = time.time() - start_time

    pool_speedup = direct_time / pool_time if pool_time > 0 else 0
    print(f"对象池时间: {pool_time:.3f}s")
    print(f"直接创建时间: {direct_time:.3f}s")
    print(f"对象池加速比: {pool_speedup:.2f}x")

    print("\n=== 基准测试完成 ===")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "benchmark":
        run_benchmark()
    else:
        pytest.main([__file__, "-v"])
