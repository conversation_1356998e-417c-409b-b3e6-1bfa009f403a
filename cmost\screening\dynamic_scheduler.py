"""
Dynamic screening scheduler for CMOST simulation.

This module implements risk-based dynamic screening interval adjustment
and personalized screening scheduling.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import numpy as np
import logging
from datetime import datetime

from ..models.patient import Patient


class RiskLevel(Enum):
    """Patient risk levels for screening."""
    VERY_LOW = "very_low"
    LOW = "low"
    AVERAGE = "average"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"


class ScreeningOutcome(Enum):
    """Screening outcome types."""
    NEGATIVE = "negative"
    POSITIVE_BENIGN = "positive_benign"
    POSITIVE_ADVANCED = "positive_advanced"
    POSITIVE_CANCER = "positive_cancer"
    INCOMPLETE = "incomplete"
    COMPLICATIONS = "complications"


@dataclass
class ScreeningEvent:
    """Record of a screening event."""
    
    year: int
    patient_id: int
    test_type: str
    outcome: ScreeningOutcome
    findings: Dict[str, Any] = field(default_factory=dict)
    
    # Risk assessment at time of screening
    risk_level: RiskLevel = RiskLevel.AVERAGE
    risk_score: float = 1.0
    
    # Quality metrics
    bowel_prep_quality: str = "adequate"  # "poor", "adequate", "excellent"
    completion_rate: float = 1.0  # For colonoscopy
    
    # Follow-up recommendations
    next_screening_year: Optional[int] = None
    recommended_test: Optional[str] = None
    surveillance_needed: bool = False


@dataclass
class DynamicSchedulingRules:
    """Rules for dynamic screening scheduling."""
    
    # Base intervals by risk level (years)
    base_intervals: Dict[RiskLevel, int] = field(default_factory=lambda: {
        RiskLevel.VERY_LOW: 15,
        RiskLevel.LOW: 10,
        RiskLevel.AVERAGE: 10,
        RiskLevel.MODERATE: 5,
        RiskLevel.HIGH: 3,
        RiskLevel.VERY_HIGH: 1
    })
    
    # Interval adjustments based on findings
    finding_adjustments: Dict[str, float] = field(default_factory=lambda: {
        'no_findings': 1.0,
        'small_polyps_1_2': 0.5,  # 3-5 years -> 1.5-2.5 years
        'small_polyps_3_plus': 0.3,  # 3-5 years -> 1-1.5 years
        'advanced_adenoma': 0.3,  # 3 years
        'serrated_lesions': 0.6,  # Slightly shorter interval
        'high_risk_serrated': 0.3,  # 3 years
        'cancer': 0.2  # 1 year surveillance
    })
    
    # Quality adjustments
    quality_adjustments: Dict[str, float] = field(default_factory=lambda: {
        'poor_prep': 0.5,  # Repeat sooner
        'incomplete_exam': 0.5,  # Repeat sooner
        'excellent_quality': 1.2  # Can extend slightly
    })
    
    # Age-based adjustments
    age_adjustments: Dict[Tuple[int, int], float] = field(default_factory=lambda: {
        (50, 59): 1.0,
        (60, 69): 1.0,
        (70, 79): 0.8,  # More frequent for older patients
        (80, 89): 0.6,
        (90, 100): 0.5
    })
    
    # Minimum and maximum intervals
    min_interval: int = 1
    max_interval: int = 15


class DynamicScreeningScheduler:
    """Dynamic screening scheduler with risk-based interval adjustment."""
    
    def __init__(self, rules: Optional[DynamicSchedulingRules] = None):
        """Initialize dynamic screening scheduler.
        
        Args:
            rules: Scheduling rules (uses default if None)
        """
        self.rules = rules or DynamicSchedulingRules()
        self.logger = logging.getLogger("CMOST_DynamicScheduler")
        
        # Patient screening histories
        self.screening_histories: Dict[int, List[ScreeningEvent]] = {}
        
        # Risk assessment cache
        self.risk_assessments: Dict[int, Tuple[RiskLevel, float, int]] = {}  # patient_id -> (level, score, year)
    
    def assess_patient_risk(self, patient: Patient, current_year: int) -> Tuple[RiskLevel, float]:
        """Assess patient's current risk level.
        
        Args:
            patient: Patient to assess
            current_year: Current simulation year
            
        Returns:
            Tuple of (risk_level, risk_score)
        """
        # Check cache first
        if patient.id in self.risk_assessments:
            cached_level, cached_score, cached_year = self.risk_assessments[patient.id]
            if cached_year == current_year:
                return cached_level, cached_score
        
        # Calculate base risk from individual risk factors
        base_risk = patient.individual_risk
        
        # Genetic risk factors
        genetic_multiplier = 1.0
        if patient.genetic_profile:
            genetic_risk = patient.genetic_profile.get_age_specific_risk(patient.age)
            baseline_risk = 0.001  # Baseline age-specific risk
            if baseline_risk > 0:
                genetic_multiplier = min(10.0, genetic_risk / baseline_risk)  # Cap at 10x
        
        # Family history
        family_multiplier = 1.0
        if patient.genetic_profile and patient.genetic_profile.family_history_strength != "none":
            if patient.genetic_profile.family_history_strength == "strong":
                family_multiplier = 3.0
            elif patient.genetic_profile.family_history_strength == "moderate":
                family_multiplier = 2.0
            elif patient.genetic_profile.family_history_strength == "weak":
                family_multiplier = 1.5
        
        # Previous screening findings
        screening_multiplier = 1.0
        if patient.id in self.screening_histories:
            recent_findings = self._get_recent_findings(patient.id, current_year, lookback_years=10)
            if recent_findings:
                if any(f.get('advanced_adenoma', False) for f in recent_findings):
                    screening_multiplier = 2.0
                elif any(f.get('multiple_polyps', False) for f in recent_findings):
                    screening_multiplier = 1.5
                elif any(f.get('serrated_lesions', False) for f in recent_findings):
                    screening_multiplier = 1.3
        
        # Age factor
        age_multiplier = 1.0
        if patient.age >= 70:
            age_multiplier = 1.5
        elif patient.age >= 60:
            age_multiplier = 1.2
        elif patient.age < 50:
            age_multiplier = 0.7
        
        # Calculate final risk score (cap the multipliers to avoid extreme values)
        genetic_multiplier = min(genetic_multiplier, 5.0)
        family_multiplier = min(family_multiplier, 3.0)
        screening_multiplier = min(screening_multiplier, 2.0)
        age_multiplier = min(age_multiplier, 1.5)

        risk_score = base_risk * genetic_multiplier * family_multiplier * screening_multiplier * age_multiplier
        
        # Determine risk level
        if risk_score < 0.5:
            risk_level = RiskLevel.VERY_LOW
        elif risk_score < 1.0:
            risk_level = RiskLevel.LOW
        elif risk_score < 2.0:
            risk_level = RiskLevel.AVERAGE
        elif risk_score < 4.0:
            risk_level = RiskLevel.MODERATE
        elif risk_score < 8.0:
            risk_level = RiskLevel.HIGH
        else:
            risk_level = RiskLevel.VERY_HIGH
        
        # Cache the assessment
        self.risk_assessments[patient.id] = (risk_level, risk_score, current_year)
        
        return risk_level, risk_score
    
    def calculate_next_screening_interval(self, 
                                        patient: Patient, 
                                        screening_event: ScreeningEvent,
                                        current_year: int) -> int:
        """Calculate next screening interval based on risk and findings.
        
        Args:
            patient: Patient who was screened
            screening_event: Recent screening event
            current_year: Current simulation year
            
        Returns:
            Interval in years until next screening
        """
        # Get base interval from risk level
        base_interval = self.rules.base_intervals.get(screening_event.risk_level, 10)
        
        # Apply finding-based adjustments
        finding_adjustment = 1.0
        findings = screening_event.findings
        
        if findings.get('total_detected', 0) == 0:
            finding_adjustment = self.rules.finding_adjustments['no_findings']
        else:
            # Determine most significant finding
            if findings.get('cancers', []):
                finding_adjustment = self.rules.finding_adjustments['cancer']
            elif findings.get('advanced_adenomas', 0) > 0:
                finding_adjustment = self.rules.finding_adjustments['advanced_adenoma']
            elif findings.get('serrated_lesions', []):
                if any(self._is_high_risk_serrated(lesion_id, patient) 
                      for lesion_id in findings['serrated_lesions']):
                    finding_adjustment = self.rules.finding_adjustments['high_risk_serrated']
                else:
                    finding_adjustment = self.rules.finding_adjustments['serrated_lesions']
            elif findings.get('polyps', []):
                polyp_count = len(findings['polyps'])
                if polyp_count >= 3:
                    finding_adjustment = self.rules.finding_adjustments['small_polyps_3_plus']
                else:
                    finding_adjustment = self.rules.finding_adjustments['small_polyps_1_2']
        
        # Apply quality adjustments
        quality_adjustment = 1.0
        if screening_event.bowel_prep_quality == "poor":
            quality_adjustment = self.rules.quality_adjustments['poor_prep']
        elif screening_event.completion_rate < 0.9:
            quality_adjustment = self.rules.quality_adjustments['incomplete_exam']
        elif (screening_event.bowel_prep_quality == "excellent" and 
              screening_event.completion_rate >= 0.95):
            quality_adjustment = self.rules.quality_adjustments['excellent_quality']
        
        # Apply age adjustments
        age_adjustment = 1.0
        for (min_age, max_age), adjustment in self.rules.age_adjustments.items():
            if min_age <= patient.age <= max_age:
                age_adjustment = adjustment
                break
        
        # Calculate final interval
        interval = base_interval * finding_adjustment * quality_adjustment * age_adjustment

        # Apply min/max constraints
        interval = max(self.rules.min_interval, min(self.rules.max_interval, round(interval)))

        # Ensure interval is actually reduced when it should be
        if finding_adjustment < 1.0 or quality_adjustment < 1.0:
            interval = max(1, min(interval, base_interval - 1))
        
        self.logger.debug(f"Calculated interval for patient {patient.id}: {interval} years "
                         f"(base: {base_interval}, finding: {finding_adjustment:.2f}, "
                         f"quality: {quality_adjustment:.2f}, age: {age_adjustment:.2f})")
        
        return int(interval)
    
    def schedule_next_screening(self, 
                              patient: Patient, 
                              screening_result: Dict[str, Any],
                              current_year: int) -> Optional[int]:
        """Schedule next screening for a patient.
        
        Args:
            patient: Patient who was screened
            screening_result: Results from screening
            current_year: Current simulation year
            
        Returns:
            Year of next scheduled screening, or None if no more screening needed
        """
        # Assess current risk
        risk_level, risk_score = self.assess_patient_risk(patient, current_year)
        
        # Determine screening outcome
        outcome = self._determine_screening_outcome(screening_result)
        
        # Create screening event
        screening_event = ScreeningEvent(
            year=current_year,
            patient_id=patient.id,
            test_type=screening_result.get('test', 'unknown'),
            outcome=outcome,
            findings=screening_result.get('findings', {}),
            risk_level=risk_level,
            risk_score=risk_score,
            bowel_prep_quality=screening_result.get('bowel_prep_quality', 'adequate'),
            completion_rate=screening_result.get('completion_rate', 1.0)
        )
        
        # Add to history
        if patient.id not in self.screening_histories:
            self.screening_histories[patient.id] = []
        self.screening_histories[patient.id].append(screening_event)
        
        # Check if patient is too old for continued screening
        if patient.age > 85:
            self.logger.debug(f"Patient {patient.id} too old for continued screening")
            return None
        
        # Calculate next interval
        interval = self.calculate_next_screening_interval(patient, screening_event, current_year)
        next_year = current_year + interval
        
        # Update screening event with recommendation
        screening_event.next_screening_year = next_year
        screening_event.recommended_test = self._recommend_next_test(screening_event)
        
        return next_year
    
    def is_screening_due(self, patient: Patient, current_year: int) -> bool:
        """Check if screening is due for a patient.
        
        Args:
            patient: Patient to check
            current_year: Current simulation year
            
        Returns:
            True if screening is due
        """
        if patient.id not in self.screening_histories:
            # First screening - check if patient is in screening age
            return 50 <= patient.age <= 75
        
        # Get last screening
        last_screening = self.screening_histories[patient.id][-1]
        
        # Check if next screening is due
        if last_screening.next_screening_year is None:
            return False
        
        return current_year >= last_screening.next_screening_year
    
    def get_recommended_test(self, patient: Patient, current_year: int) -> str:
        """Get recommended test type for a patient.
        
        Args:
            patient: Patient to get recommendation for
            current_year: Current simulation year
            
        Returns:
            Recommended test type
        """
        if patient.id not in self.screening_histories:
            # First screening - use risk-based recommendation
            risk_level, _ = self.assess_patient_risk(patient, current_year)
            return self._get_initial_test_recommendation(risk_level)
        
        # Get recommendation from last screening
        last_screening = self.screening_histories[patient.id][-1]
        return last_screening.recommended_test or "colonoscopy"
    
    def _get_recent_findings(self, patient_id: int, current_year: int, lookback_years: int = 5) -> List[Dict]:
        """Get recent screening findings for a patient."""
        if patient_id not in self.screening_histories:
            return []
        
        recent_findings = []
        for event in self.screening_histories[patient_id]:
            if current_year - event.year <= lookback_years:
                recent_findings.append(event.findings)
        
        return recent_findings
    
    def _determine_screening_outcome(self, screening_result: Dict[str, Any]) -> ScreeningOutcome:
        """Determine screening outcome from results."""
        findings = screening_result.get('findings', {})
        
        if findings.get('cancers', []):
            return ScreeningOutcome.POSITIVE_CANCER
        elif findings.get('advanced_adenomas', 0) > 0:
            return ScreeningOutcome.POSITIVE_ADVANCED
        elif findings.get('total_detected', 0) > 0:
            return ScreeningOutcome.POSITIVE_BENIGN
        elif screening_result.get('complications', False):
            return ScreeningOutcome.COMPLICATIONS
        elif screening_result.get('completion_rate', 1.0) < 0.9:
            return ScreeningOutcome.INCOMPLETE
        else:
            return ScreeningOutcome.NEGATIVE
    
    def _is_high_risk_serrated(self, lesion_id: int, patient: Patient) -> bool:
        """Check if a serrated lesion is high-risk."""
        for lesion in patient.serrated_lesions:
            if lesion.id == lesion_id:
                return lesion.size >= 1.0 or lesion.has_dysplasia
        return False
    
    def _recommend_next_test(self, screening_event: ScreeningEvent) -> str:
        """Recommend next test type based on screening event."""
        if screening_event.outcome == ScreeningOutcome.POSITIVE_CANCER:
            return "surveillance_colonoscopy"
        elif screening_event.outcome == ScreeningOutcome.POSITIVE_ADVANCED:
            return "colonoscopy"
        elif screening_event.outcome == ScreeningOutcome.POSITIVE_BENIGN:
            return "colonoscopy"
        elif screening_event.risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
            return "colonoscopy"
        else:
            return screening_event.test_type  # Continue with same test
    
    def _get_initial_test_recommendation(self, risk_level: RiskLevel) -> str:
        """Get initial test recommendation based on risk level."""
        if risk_level in [RiskLevel.VERY_HIGH, RiskLevel.HIGH]:
            return "colonoscopy"
        elif risk_level == RiskLevel.MODERATE:
            return "sigmoidoscopy"
        else:
            return "fit"
    
    def get_screening_summary(self, patient_id: int) -> Dict[str, Any]:
        """Get screening summary for a patient.
        
        Args:
            patient_id: Patient ID
            
        Returns:
            Screening summary
        """
        if patient_id not in self.screening_histories:
            return {"total_screenings": 0, "history": []}
        
        history = self.screening_histories[patient_id]
        
        return {
            "total_screenings": len(history),
            "first_screening_year": history[0].year if history else None,
            "last_screening_year": history[-1].year if history else None,
            "next_screening_year": history[-1].next_screening_year if history else None,
            "positive_screenings": sum(1 for event in history 
                                     if event.outcome != ScreeningOutcome.NEGATIVE),
            "history": [
                {
                    "year": event.year,
                    "test": event.test_type,
                    "outcome": event.outcome.value,
                    "risk_level": event.risk_level.value,
                    "findings": event.findings
                }
                for event in history
            ]
        }
