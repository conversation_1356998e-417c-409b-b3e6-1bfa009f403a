"""
Unit tests for integrated calibration functionality in CMOST.
"""

import unittest
import tempfile
import os
import shutil
from unittest.mock import Mock, patch, MagicMock

from cmost.ml.integrated_calibration import (
    IntegratedCalibrator,
    CalibrationMethod,
    CalibrationResult,
    run_integrated_calibration
)
from cmost.ml.adaptive_calibration import (
    CalibrationData,
    ParameterSpace,
    OptimizationMethod
)


class TestCalibrationResult(unittest.TestCase):
    """Test cases for CalibrationResult class."""
    
    def test_calibration_result_creation(self):
        """Test calibration result creation."""
        result = CalibrationResult(
            method=CalibrationMethod.RANDOM_FOREST,
            parameters={"param1": 1.0, "param2": 2.0},
            error=0.05,
            execution_time=120.5
        )
        
        self.assertEqual(result.method, CalibrationMethod.RANDOM_FOREST)
        self.assertEqual(result.parameters["param1"], 1.0)
        self.assertEqual(result.error, 0.05)
        self.assertEqual(result.execution_time, 120.5)
    
    def test_calibration_result_summary(self):
        """Test calibration result summary."""
        result = CalibrationResult(
            method=CalibrationMethod.DEEP_NEURAL_NETWORK,
            parameters={"param1": 1.5},
            error=0.03,
            execution_time=200.0,
            convergence_info={"converged": True}
        )
        
        summary = result.get_summary()
        
        self.assertEqual(summary["method"], "deep_neural_network")
        self.assertEqual(summary["parameters"]["param1"], 1.5)
        self.assertEqual(summary["error"], 0.03)
        self.assertTrue(summary["converged"])


class TestIntegratedCalibrator(unittest.TestCase):
    """Test cases for IntegratedCalibrator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.calibrator = IntegratedCalibrator(output_dir=self.temp_dir)
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_calibrator_initialization(self):
        """Test calibrator initialization."""
        self.assertIsNotNone(self.calibrator.parameter_spaces)
        self.assertEqual(len(self.calibrator.parameter_spaces), 9)  # 3 parameters × 3 stages
        self.assertEqual(self.calibrator.output_dir, self.temp_dir)
        self.assertEqual(len(self.calibrator.calibration_results), 0)
    
    def test_default_parameter_spaces(self):
        """Test default parameter spaces creation."""
        param_spaces = self.calibrator._create_default_parameter_spaces()
        
        self.assertEqual(len(param_spaces), 9)
        
        # Check parameter names
        param_names = [ps.name for ps in param_spaces]
        expected_names = [
            "early_mult", "early_width", "early_center",
            "adv_mult", "adv_width", "adv_center",
            "cancer_mult", "cancer_width", "cancer_center"
        ]
        
        for name in expected_names:
            self.assertIn(name, param_names)
    
    def test_prepare_calibration_data(self):
        """Test calibration data preparation."""
        calibration_data = self.calibrator._prepare_calibration_data()
        
        self.assertIsInstance(calibration_data, list)
        self.assertGreater(len(calibration_data), 0)
        
        # Check first calibration data
        first_data = calibration_data[0]
        self.assertIsInstance(first_data, CalibrationData)
        self.assertIn(first_data.target_name, ["cancer_incidence", "polyp_prevalence"])
        self.assertGreater(len(first_data.observed_values), 0)
        self.assertEqual(len(first_data.observed_values), len(first_data.simulated_values))
    
    def test_calculate_overall_error(self):
        """Test overall error calculation."""
        # Test with valid validation metrics
        validation_metrics = {
            "metric1": {"rel_error_pct": 5.0},
            "metric2": {"rel_error_pct": 10.0},
            "metric3": {"rel_error_pct": 15.0}
        }
        
        error = self.calibrator._calculate_overall_error(validation_metrics)
        self.assertAlmostEqual(error, 10.0, places=1)  # (5+10+15)/3 = 10
        
        # Test with empty metrics
        error = self.calibrator._calculate_overall_error({})
        self.assertEqual(error, float('inf'))
        
        # Test with None
        error = self.calibrator._calculate_overall_error(None)
        self.assertEqual(error, float('inf'))
    
    @patch('cmost.ml.integrated_calibration.AutoCalibration')
    def test_calibrate_with_dnn_success(self, mock_auto_calibration):
        """Test DNN calibration success case."""
        # Mock the AutoCalibration class
        mock_instance = Mock()
        mock_auto_calibration.return_value = mock_instance
        
        # Mock the calibration pipeline
        mock_parameters = {"param1": 1.0, "param2": 2.0}
        mock_instance.run_calibration_pipeline.return_value = mock_parameters
        
        # Mock validation
        mock_validation = {
            "metric1": {"rel_error_pct": 5.0},
            "metric2": {"rel_error_pct": 10.0}
        }
        mock_instance.validate_calibration.return_value = mock_validation
        
        # Run calibration
        result = self.calibrator.calibrate_with_dnn(n_samples=100, n_patients=100, epochs=5)
        
        # Verify result
        self.assertEqual(result.method, CalibrationMethod.DEEP_NEURAL_NETWORK)
        self.assertEqual(result.parameters, mock_parameters)
        self.assertAlmostEqual(result.error, 7.5, places=1)  # (5+10)/2 = 7.5
        self.assertGreater(result.execution_time, 0)
        self.assertTrue(result.convergence_info["converged"])
        
        # Verify mock calls
        mock_instance.run_calibration_pipeline.assert_called_once_with(
            n_samples=100, n_patients=100, epochs=5
        )
        mock_instance.validate_calibration.assert_called_once_with(mock_parameters)
    
    @patch('cmost.ml.integrated_calibration.AutoCalibration')
    def test_calibrate_with_dnn_failure(self, mock_auto_calibration):
        """Test DNN calibration failure case."""
        # Mock the AutoCalibration class to raise an exception
        mock_auto_calibration.side_effect = Exception("DNN calibration failed")
        
        # Run calibration
        result = self.calibrator.calibrate_with_dnn()
        
        # Verify result
        self.assertEqual(result.method, CalibrationMethod.DEEP_NEURAL_NETWORK)
        self.assertEqual(result.parameters, {})
        self.assertEqual(result.error, float('inf'))
        self.assertGreater(result.execution_time, 0)
        self.assertFalse(result.convergence_info["converged"])
        self.assertIn("DNN calibration failed", result.convergence_info["error"])
    
    @patch('cmost.ml.integrated_calibration.AdaptiveCalibrator')
    def test_calibrate_with_adaptive_success(self, mock_adaptive_calibrator):
        """Test adaptive calibration success case."""
        # Mock the AdaptiveCalibrator class
        mock_instance = Mock()
        mock_adaptive_calibrator.return_value = mock_instance
        
        # Mock calibration
        mock_parameters = {"param1": 1.5, "param2": 2.5}
        mock_instance.calibrate.return_value = mock_parameters
        
        # Mock calibration summary
        mock_summary = {
            "latest_error": 0.05,
            "converged": True,
            "total_calibrations": 1
        }
        mock_instance.get_calibration_summary.return_value = mock_summary
        
        # Run calibration
        result = self.calibrator.calibrate_with_adaptive(
            method=OptimizationMethod.RANDOM_FOREST,
            max_iterations=10
        )
        
        # Verify result
        self.assertEqual(result.method, CalibrationMethod.RANDOM_FOREST)
        self.assertEqual(result.parameters, mock_parameters)
        self.assertEqual(result.error, 0.05)
        self.assertGreater(result.execution_time, 0)
        self.assertTrue(result.convergence_info["converged"])
        
        # Verify mock calls
        mock_instance.calibrate.assert_called_once()
        mock_instance.get_calibration_summary.assert_called_once()
    
    def test_comparison_report_empty(self):
        """Test comparison report with no results."""
        report = self.calibrator.get_comparison_report()
        
        self.assertEqual(report["message"], "No calibration results available")
    
    def test_comparison_report_with_results(self):
        """Test comparison report with results."""
        # Add some mock results
        result1 = CalibrationResult(
            method=CalibrationMethod.RANDOM_FOREST,
            parameters={"param1": 1.0},
            error=0.1,
            execution_time=100.0,
            convergence_info={"converged": True}
        )
        
        result2 = CalibrationResult(
            method=CalibrationMethod.DEEP_NEURAL_NETWORK,
            parameters={"param1": 1.5},
            error=0.05,
            execution_time=200.0,
            convergence_info={"converged": True}
        )
        
        self.calibrator.calibration_results = [result1, result2]
        self.calibrator.best_result = result2
        
        report = self.calibrator.get_comparison_report()
        
        self.assertEqual(report["total_methods_tried"], 2)
        self.assertEqual(report["best_method"], "deep_neural_network")
        self.assertEqual(report["best_error"], 0.05)
        self.assertEqual(len(report["results"]), 2)
        
        # Results should be sorted by error (best first)
        self.assertEqual(report["results"][0]["method"], "deep_neural_network")
        self.assertEqual(report["results"][0]["error"], 0.05)
        self.assertEqual(report["results"][1]["method"], "random_forest")
        self.assertEqual(report["results"][1]["error"], 0.1)
    
    def test_save_results(self):
        """Test saving results to file."""
        import json
        
        # Add a mock result
        result = CalibrationResult(
            method=CalibrationMethod.RANDOM_FOREST,
            parameters={"param1": 1.0},
            error=0.1,
            execution_time=100.0
        )
        
        self.calibrator.calibration_results = [result]
        self.calibrator.best_result = result
        
        # Save results
        filepath = os.path.join(self.temp_dir, "test_results.json")
        self.calibrator.save_results(filepath)
        
        # Verify file was created
        self.assertTrue(os.path.exists(filepath))
        
        # Verify file contents
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        self.assertIn("timestamp", data)
        self.assertIn("best_result", data)
        self.assertIn("all_results", data)
        self.assertIn("comparison_report", data)
        
        self.assertEqual(data["best_result"]["method"], "random_forest")
        self.assertEqual(len(data["all_results"]), 1)


class TestRunIntegratedCalibration(unittest.TestCase):
    """Test cases for run_integrated_calibration function."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('cmost.ml.integrated_calibration.IntegratedCalibrator')
    def test_run_with_auto_select(self, mock_calibrator_class):
        """Test running with auto-select method."""
        # Mock the calibrator
        mock_calibrator = Mock()
        mock_calibrator_class.return_value = mock_calibrator
        
        # Mock auto-select result
        mock_result = CalibrationResult(
            method=CalibrationMethod.RANDOM_FOREST,
            parameters={"param1": 1.0},
            error=0.05,
            execution_time=100.0
        )
        mock_calibrator.auto_select_best_method.return_value = mock_result
        
        # Run calibration
        result = run_integrated_calibration(
            method=CalibrationMethod.AUTO_SELECT,
            output_dir=self.temp_dir,
            quick_evaluation=True
        )
        
        # Verify result
        self.assertEqual(result.method, CalibrationMethod.RANDOM_FOREST)
        self.assertEqual(result.error, 0.05)
        
        # Verify mock calls
        mock_calibrator.auto_select_best_method.assert_called_once_with(quick_evaluation=True)
        mock_calibrator.save_results.assert_called_once()
    
    @patch('cmost.ml.integrated_calibration.IntegratedCalibrator')
    def test_run_with_specific_method(self, mock_calibrator_class):
        """Test running with specific method."""
        # Mock the calibrator
        mock_calibrator = Mock()
        mock_calibrator_class.return_value = mock_calibrator
        
        # Mock DNN result
        mock_result = CalibrationResult(
            method=CalibrationMethod.DEEP_NEURAL_NETWORK,
            parameters={"param1": 1.5},
            error=0.03,
            execution_time=200.0
        )
        mock_calibrator.calibrate_with_dnn.return_value = mock_result
        
        # Run calibration
        result = run_integrated_calibration(
            method=CalibrationMethod.DEEP_NEURAL_NETWORK,
            output_dir=self.temp_dir,
            quick_evaluation=False
        )
        
        # Verify result
        self.assertEqual(result.method, CalibrationMethod.DEEP_NEURAL_NETWORK)
        self.assertEqual(result.error, 0.03)
        
        # Verify mock calls
        mock_calibrator.calibrate_with_dnn.assert_called_once()
        mock_calibrator.save_results.assert_called_once()
    
    def test_run_with_unsupported_method(self):
        """Test running with unsupported method."""
        with self.assertRaises(ValueError) as context:
            run_integrated_calibration(
                method=CalibrationMethod.BAYESIAN_OPTIMIZATION,  # Not implemented
                output_dir=self.temp_dir
            )
        
        self.assertIn("Unsupported calibration method", str(context.exception))


if __name__ == '__main__':
    unittest.main()
