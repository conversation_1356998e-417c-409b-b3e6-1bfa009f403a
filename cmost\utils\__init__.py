"""
Utility functions for CMOST (Colorectal Microsimulation Outcomes Screening Tool).

This package contains utility functions for statistics, file I/O, visualization,
and performance optimization used throughout the CMOST application.
"""

from .statistics import (
    calculate_statistics,
    calculate_incidence_rates,
    calculate_mortality_rates,
    calculate_screening_metrics,
    calculate_cost_effectiveness
)

from .file_io import (
    save_results,
    load_results,
    export_to_excel,
    import_from_excel,
    save_patient_data,
    load_patient_data
)

from .visualization import (
    plot_results,
    plot_age_distribution,
    plot_incidence_curves,
    plot_survival_curves,
    plot_cost_effectiveness,
    create_summary_report
)

# Performance optimization modules
from .performance import (
    PerformanceMetrics,
    PerformanceMonitor,
    performance_monitor,
    profile,
    memory_limit,
    optimize_dataclass
)

from .parallel import (
    ParallelConfig,
    ParallelProcessor,
    SimulationParallelizer,
    parallel_map,
    parallel_simulation,
    ProgressTracker
)

from .memory import (
    MemoryManager,
    ObjectPool,
    ArrayPool,
    memory_manager,
    array_pool,
    managed_object,
    managed_array,
    memory_monitor,
    optimize_gc,
    LazyList
)

from .cache import (
    LRUCache,
    PersistentCache,
    MultiLevelCache,
    default_cache,
    cached,
    cache_context,
    preload_cache
)

__all__ = [
    # Statistics
    'calculate_statistics',
    'calculate_incidence_rates',
    'calculate_mortality_rates',
    'calculate_screening_metrics',
    'calculate_cost_effectiveness',

    # File I/O
    'save_results',
    'load_results',
    'export_to_excel',
    'import_from_excel',
    'save_patient_data',
    'load_patient_data',

    # Visualization
    'plot_results',
    'plot_age_distribution',
    'plot_incidence_curves',
    'plot_survival_curves',
    'plot_cost_effectiveness',
    'create_summary_report',

    # Performance monitoring
    'PerformanceMetrics',
    'PerformanceMonitor',
    'performance_monitor',
    'profile',
    'memory_limit',
    'optimize_dataclass',

    # Parallel processing
    'ParallelConfig',
    'ParallelProcessor',
    'SimulationParallelizer',
    'parallel_map',
    'parallel_simulation',
    'ProgressTracker',

    # Memory management
    'MemoryManager',
    'ObjectPool',
    'ArrayPool',
    'memory_manager',
    'array_pool',
    'managed_object',
    'managed_array',
    'memory_monitor',
    'optimize_gc',
    'LazyList',

    # Caching
    'LRUCache',
    'PersistentCache',
    'MultiLevelCache',
    'default_cache',
    'cached',
    'cache_context',
    'preload_cache'
]
