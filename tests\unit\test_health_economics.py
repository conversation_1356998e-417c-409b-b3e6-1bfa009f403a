"""
Unit tests for health economics evaluation in CMOST.
"""

import unittest
import numpy as np

from cmost.economics.health_economics import (
    HealthEconomicsEvaluator,
    CostData,
    QualityAdjustedLifeYear,
    EconomicOutcome,
    CostCategory,
    UtilityState
)


class TestCostData(unittest.TestCase):
    """Test cases for CostData class."""
    
    def test_cost_data_creation(self):
        """Test cost data creation."""
        cost = CostData(
            category=CostCategory.SCREENING,
            amount=1000.0,
            year=2023,
            patient_id=1,
            description="Colonoscopy screening"
        )
        
        self.assertEqual(cost.category, CostCategory.SCREENING)
        self.assertEqual(cost.amount, 1000.0)
        self.assertEqual(cost.year, 2023)
        self.assertEqual(cost.patient_id, 1)
        self.assertEqual(cost.direct_medical, 1000.0)  # Should default to amount
    
    def test_cost_data_with_components(self):
        """Test cost data with detailed components."""
        cost = CostData(
            category=CostCategory.TREATMENT,
            amount=5000.0,
            year=2023,
            patient_id=1,
            direct_medical=4000.0,
            direct_non_medical=500.0,
            indirect=500.0
        )
        
        self.assertEqual(cost.get_total_cost(), 5000.0)
        self.assertEqual(cost.direct_medical, 4000.0)
        self.assertEqual(cost.direct_non_medical, 500.0)
        self.assertEqual(cost.indirect, 500.0)
    
    def test_cost_discounting(self):
        """Test cost discounting."""
        cost = CostData(
            category=CostCategory.SCREENING,
            amount=1000.0,
            year=2025,  # 2 years from base
            patient_id=1
        )
        
        # Test discounting with 3% rate
        discounted = cost.apply_discount(discount_rate=0.03, base_year=2023)
        expected = 1000.0 / (1.03 ** 2)  # ≈ 943.40
        
        self.assertAlmostEqual(discounted, expected, places=2)
    
    def test_cost_no_discounting_base_year(self):
        """Test no discounting for base year."""
        cost = CostData(
            category=CostCategory.SCREENING,
            amount=1000.0,
            year=2023,
            patient_id=1
        )
        
        discounted = cost.apply_discount(discount_rate=0.03, base_year=2023)
        self.assertEqual(discounted, 1000.0)


class TestQualityAdjustedLifeYear(unittest.TestCase):
    """Test cases for QualityAdjustedLifeYear class."""
    
    def test_qaly_creation(self):
        """Test QALY creation."""
        qaly = QualityAdjustedLifeYear(
            patient_id=1,
            year=2023,
            utility_state=UtilityState.HEALTHY,
            utility_value=1.0,
            duration_years=1.0
        )
        
        self.assertEqual(qaly.patient_id, 1)
        self.assertEqual(qaly.utility_state, UtilityState.HEALTHY)
        self.assertEqual(qaly.utility_value, 1.0)
        self.assertEqual(qaly.duration_years, 1.0)
    
    def test_qaly_calculation(self):
        """Test QALY calculation."""
        qaly = QualityAdjustedLifeYear(
            patient_id=1,
            year=2023,
            utility_state=UtilityState.CANCER_STAGE_I,
            utility_value=0.85,
            duration_years=1.0
        )
        
        # No discounting for base year
        calculated = qaly.calculate_qaly(discount_rate=0.03, base_year=2023)
        self.assertEqual(calculated, 0.85)
    
    def test_qaly_discounting(self):
        """Test QALY discounting."""
        qaly = QualityAdjustedLifeYear(
            patient_id=1,
            year=2025,  # 2 years from base
            utility_state=UtilityState.HEALTHY,
            utility_value=1.0,
            duration_years=1.0
        )
        
        # Test discounting with 3% rate
        discounted = qaly.calculate_qaly(discount_rate=0.03, base_year=2023)
        expected = 1.0 / (1.03 ** 2)  # ≈ 0.9434
        
        self.assertAlmostEqual(discounted, expected, places=4)


class TestHealthEconomicsEvaluator(unittest.TestCase):
    """Test cases for HealthEconomicsEvaluator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.evaluator = HealthEconomicsEvaluator(
            discount_rate=0.03,
            willingness_to_pay=50000.0,
            base_year=2023
        )
    
    def test_evaluator_initialization(self):
        """Test evaluator initialization."""
        self.assertEqual(self.evaluator.discount_rate, 0.03)
        self.assertEqual(self.evaluator.willingness_to_pay, 50000.0)
        self.assertEqual(self.evaluator.base_year, 2023)
        
        # Check standard costs are loaded
        self.assertIn('colonoscopy', self.evaluator.standard_costs)
        self.assertGreater(self.evaluator.standard_costs['colonoscopy'], 0)
        
        # Check standard utilities are loaded
        self.assertIn(UtilityState.HEALTHY, self.evaluator.standard_utilities)
        self.assertEqual(self.evaluator.standard_utilities[UtilityState.HEALTHY], 1.0)
    
    def test_add_cost(self):
        """Test adding cost data."""
        self.evaluator.add_cost(
            cost_category=CostCategory.SCREENING,
            amount=1000.0,
            year=2023,
            patient_id=1,
            description="Colonoscopy"
        )
        
        self.assertEqual(len(self.evaluator.cost_data), 1)
        cost = self.evaluator.cost_data[0]
        self.assertEqual(cost.category, CostCategory.SCREENING)
        self.assertEqual(cost.amount, 1000.0)
        self.assertEqual(cost.patient_id, 1)
    
    def test_add_qaly(self):
        """Test adding QALY data."""
        self.evaluator.add_qaly(
            patient_id=1,
            year=2023,
            utility_state=UtilityState.HEALTHY,
            duration_years=1.0
        )
        
        self.assertEqual(len(self.evaluator.qaly_data), 1)
        qaly = self.evaluator.qaly_data[0]
        self.assertEqual(qaly.patient_id, 1)
        self.assertEqual(qaly.utility_state, UtilityState.HEALTHY)
        self.assertEqual(qaly.utility_value, 1.0)  # Should use standard utility
    
    def test_add_qaly_custom_utility(self):
        """Test adding QALY with custom utility value."""
        self.evaluator.add_qaly(
            patient_id=1,
            year=2023,
            utility_state=UtilityState.HEALTHY,
            custom_utility=0.95
        )
        
        qaly = self.evaluator.qaly_data[0]
        self.assertEqual(qaly.utility_value, 0.95)  # Should use custom value
    
    def test_calculate_patient_outcomes(self):
        """Test calculating patient outcomes."""
        # Add costs for patient 1
        self.evaluator.add_cost(CostCategory.SCREENING, 1000.0, 2023, 1)
        self.evaluator.add_cost(CostCategory.TREATMENT, 5000.0, 2024, 1)
        
        # Add QALYs for patient 1
        self.evaluator.add_qaly(1, 2023, UtilityState.HEALTHY)
        self.evaluator.add_qaly(1, 2024, UtilityState.CANCER_STAGE_I)
        
        outcomes = self.evaluator.calculate_patient_outcomes(1)
        
        self.assertEqual(outcomes.patient_id, 1)
        self.assertEqual(outcomes.total_costs, 6000.0)
        self.assertEqual(outcomes.life_years, 2.0)
        self.assertGreater(outcomes.quality_adjusted_life_years, 1.5)  # 1.0 + 0.85
        self.assertLess(outcomes.quality_adjusted_life_years, 2.0)
        
        # Check costs by category
        self.assertEqual(outcomes.costs_by_category[CostCategory.SCREENING], 1000.0)
        self.assertEqual(outcomes.costs_by_category[CostCategory.TREATMENT], 5000.0)
    
    def test_calculate_population_outcomes(self):
        """Test calculating population outcomes."""
        # Add data for multiple patients
        for patient_id in [1, 2, 3]:
            self.evaluator.add_cost(CostCategory.SCREENING, 1000.0, 2023, patient_id)
            self.evaluator.add_qaly(patient_id, 2023, UtilityState.HEALTHY)
        
        outcomes = self.evaluator.calculate_population_outcomes()
        
        self.assertIsNone(outcomes.patient_id)  # Population-level
        self.assertEqual(outcomes.total_costs, 3000.0)
        self.assertEqual(outcomes.life_years, 3.0)
        self.assertEqual(outcomes.quality_adjusted_life_years, 3.0)
    
    def test_compare_strategies(self):
        """Test comparing two strategies."""
        # Strategy 1 outcomes
        strategy1 = EconomicOutcome(
            discounted_costs=10000.0,
            discounted_qalys=15.0
        )
        
        # Strategy 2 outcomes (more expensive but more effective)
        strategy2 = EconomicOutcome(
            discounted_costs=15000.0,
            discounted_qalys=16.0
        )
        
        comparison = self.evaluator.compare_strategies(
            strategy1, strategy2, "No Screening", "Annual Screening"
        )
        
        self.assertEqual(comparison["incremental_cost"], 5000.0)
        self.assertEqual(comparison["incremental_qalys"], 1.0)
        self.assertEqual(comparison["icer"], 5000.0)  # $5000 per QALY
        
        # Should be cost-effective (below $50,000 threshold)
        self.assertIn("cost-effective", comparison["cost_effectiveness"])
    
    def test_compare_strategies_dominance(self):
        """Test strategy comparison with dominance."""
        # Strategy 1: More expensive, less effective
        strategy1 = EconomicOutcome(
            discounted_costs=15000.0,
            discounted_qalys=14.0
        )
        
        # Strategy 2: Less expensive, more effective (dominates)
        strategy2 = EconomicOutcome(
            discounted_costs=10000.0,
            discounted_qalys=16.0
        )
        
        comparison = self.evaluator.compare_strategies(strategy1, strategy2)
        
        self.assertLess(comparison["incremental_cost"], 0)
        self.assertGreater(comparison["incremental_qalys"], 0)
        self.assertIn("dominates", comparison["cost_effectiveness"])
    
    def test_get_cost_breakdown(self):
        """Test getting cost breakdown."""
        # Add various costs
        self.evaluator.add_cost(CostCategory.SCREENING, 1000.0, 2023, 1)
        self.evaluator.add_cost(CostCategory.SCREENING, 1000.0, 2023, 2)
        self.evaluator.add_cost(CostCategory.TREATMENT, 5000.0, 2024, 1)
        
        breakdown = self.evaluator.get_cost_breakdown()
        
        self.assertEqual(breakdown["total_costs"], 7000.0)
        self.assertEqual(breakdown["by_category"]["screening"]["total"], 2000.0)
        self.assertEqual(breakdown["by_category"]["screening"]["count"], 2)
        self.assertEqual(breakdown["by_category"]["treatment"]["total"], 5000.0)
        
        # Check year breakdown
        self.assertEqual(breakdown["by_year"][2023], 2000.0)
        self.assertEqual(breakdown["by_year"][2024], 5000.0)
    
    def test_cost_breakdown_specific_patients(self):
        """Test cost breakdown for specific patients."""
        # Add costs for different patients
        self.evaluator.add_cost(CostCategory.SCREENING, 1000.0, 2023, 1)
        self.evaluator.add_cost(CostCategory.SCREENING, 1000.0, 2023, 2)
        self.evaluator.add_cost(CostCategory.TREATMENT, 5000.0, 2024, 3)
        
        # Get breakdown for patients 1 and 2 only
        breakdown = self.evaluator.get_cost_breakdown(patient_ids=[1, 2])
        
        self.assertEqual(breakdown["total_costs"], 2000.0)  # Excludes patient 3
        self.assertEqual(breakdown["by_category"]["screening"]["total"], 2000.0)
        self.assertEqual(breakdown["by_category"]["treatment"]["total"], 0.0)
    
    def test_standard_cost_values(self):
        """Test that standard cost values are reasonable."""
        costs = self.evaluator.standard_costs
        
        # Screening costs should be ordered correctly
        self.assertLess(costs['fit_test'], costs['sigmoidoscopy'])
        self.assertLess(costs['sigmoidoscopy'], costs['colonoscopy'])
        
        # Treatment costs should increase with stage
        self.assertLess(costs['surgery_stage_i'], costs['surgery_stage_iv'])
        
        # All costs should be positive
        for cost_name, cost_value in costs.items():
            self.assertGreater(cost_value, 0, f"Cost {cost_name} should be positive")
    
    def test_standard_utility_values(self):
        """Test that standard utility values are reasonable."""
        utilities = self.evaluator.standard_utilities
        
        # Healthy should have highest utility
        self.assertEqual(utilities[UtilityState.HEALTHY], 1.0)
        
        # Cancer utilities should decrease with stage
        self.assertGreater(utilities[UtilityState.CANCER_STAGE_I], 
                          utilities[UtilityState.CANCER_STAGE_IV])
        
        # All utilities should be between 0 and 1
        for state, utility in utilities.items():
            self.assertGreaterEqual(utility, 0.0, f"Utility for {state} should be >= 0")
            self.assertLessEqual(utility, 1.0, f"Utility for {state} should be <= 1")


if __name__ == '__main__':
    unittest.main()
