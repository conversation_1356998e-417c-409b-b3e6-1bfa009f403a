[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cmost"
version = "2.0.0"
description = "Colorectal Microsimulation Outcomes Screening Tool"
readme = "README.md"
license = {text = "GPL-3.0"}
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Healthcare Industry",
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
]
requires-python = ">=3.8"
dependencies = [
    "numpy>=1.20.0",
    "pandas>=1.3.0",
    "scipy>=1.7.0",
    "matplotlib>=3.4.0",
    "seaborn>=0.11.0",
    "h5py>=3.6.0",
    "pyyaml>=6.0",
    "statsmodels>=0.13.0",
    "scikit-learn>=1.0.0",
    "pyDOE2>=1.3.0",
    "tqdm>=4.62.0",
    "click>=8.0.0",
    "joblib>=1.1.0",
]

[project.optional-dependencies]
visualization = [
    "plotly>=5.3.0",
    "kaleido>=0.2.1",
]
cluster = [
    "paramiko>=2.10.1",
    "dask>=2022.1.0",
    "distributed>=2022.1.0",
]
calibration = [
    "torch>=1.10.0",
    "tensorboard>=2.8.0",
]
dev = [
    "pytest>=6.2.5",
    "pytest-cov>=2.12.1",
    "pytest-xdist>=2.5.0",
    "flake8>=4.0.1",
    "black>=22.1.0",
    "mypy>=0.931",
    "bandit>=1.7.0",
    "pre-commit>=2.15.0",
]
docs = [
    "sphinx>=4.4.0",
    "sphinx-rtd-theme>=1.0.0",
    "nbsphinx>=0.8.8",
    "ipython>=8.0.0",
]
all = [
    "plotly>=5.3.0",
    "kaleido>=0.2.1",
    "paramiko>=2.10.1",
    "dask>=2022.1.0",
    "distributed>=2022.1.0",
    "torch>=1.10.0",
    "tensorboard>=2.8.0",
    "openpyxl>=3.0.9",
    "xlrd>=2.0.1",
]

[project.scripts]
cmost = "cmost.cli:main"

[project.urls]
Homepage = "https://github.com/misselwitz/cmost"
Documentation = "https://cmost.readthedocs.io"
Repository = "https://github.com/misselwitz/cmost"
"Bug Tracker" = "https://github.com/misselwitz/cmost/issues"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["cmost"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false  # Gradually enable
ignore_missing_imports = true
show_error_codes = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--cov=cmost",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=70",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "performance: Performance tests",
    "slow: Slow running tests",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["cmost"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".tox",
]

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101", "B601"]
