#!/usr/bin/env python3
"""
CMOST 性能优化示例

本示例展示如何使用CMOST的各种性能优化功能，包括：
1. 性能监控和分析
2. 并行处理优化
3. 内存管理优化
4. 缓存系统使用
5. 综合性能优化策略
"""

import time
import numpy as np
from pathlib import Path
import logging

# CMOST核心模块
from cmost.core.simulation import Simulation
from cmost.config.settings import Settings

# 性能优化模块
from cmost.utils.performance import (
    performance_monitor, profile, memory_limit, PerformanceMetrics
)
from cmost.utils.parallel import (
    ParallelConfig, parallel_simulation, parallel_map, ProgressTracker
)
from cmost.utils.memory import (
    memory_manager, managed_object, memory_monitor, optimize_gc
)
from cmost.utils.cache import (
    cached, MultiLevelCache, preload_cache, cache_context
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceOptimizationDemo:
    """性能优化演示类"""
    
    def __init__(self):
        self.setup_optimization()
    
    def setup_optimization(self):
        """设置性能优化环境"""
        logger.info("=== 设置性能优化环境 ===")
        
        # 优化垃圾回收
        optimize_gc()
        
        # 创建缓存目录
        cache_dir = Path("./demo_cache")
        cache_dir.mkdir(exist_ok=True)
        
        # 设置多级缓存
        self.cache = MultiLevelCache(
            l1_size=256,
            l2_cache_dir=cache_dir,
            l2_size_mb=50
        )
        
        logger.info("性能优化环境设置完成")
    
    @profile("demo_basic_monitoring")
    def demo_basic_monitoring(self):
        """演示基本性能监控"""
        logger.info("\n=== 基本性能监控演示 ===")
        
        # 使用性能监控装饰器
        @profile("expensive_calculation")
        def expensive_calculation(n):
            """模拟耗时计算"""
            result = 0
            for i in range(n):
                result += np.sqrt(i) * np.sin(i)
            return result
        
        # 执行计算
        with performance_monitor.monitor("calculation_block"):
            result1 = expensive_calculation(100000)
            result2 = expensive_calculation(200000)
        
        # 获取性能指标
        calc_metrics = performance_monitor.get_metrics("expensive_calculation")
        block_metrics = performance_monitor.get_metrics("calculation_block")
        
        logger.info(f"计算函数执行时间: {calc_metrics.execution_time:.3f}s")
        logger.info(f"计算函数调用次数: {calc_metrics.function_calls}")
        logger.info(f"代码块执行时间: {block_metrics.execution_time:.3f}s")
        logger.info(f"峰值内存使用: {block_metrics.peak_memory:.2f}MB")
        
        return result1, result2
    
    def demo_parallel_processing(self):
        """演示并行处理优化"""
        logger.info("\n=== 并行处理优化演示 ===")
        
        # 创建测试数据
        data = list(range(1000))
        
        def cpu_intensive_task(x):
            """CPU密集型任务"""
            result = 0
            for i in range(x * 100):
                result += np.sqrt(i + 1)
            return result
        
        # 串行处理
        start_time = time.time()
        serial_results = [cpu_intensive_task(x) for x in data[:100]]
        serial_time = time.time() - start_time
        
        # 并行处理
        start_time = time.time()
        parallel_results = parallel_map(
            cpu_intensive_task, 
            data[:100], 
            max_workers=4,
            use_processes=True
        )
        parallel_time = time.time() - start_time
        
        speedup = serial_time / parallel_time
        logger.info(f"串行处理时间: {serial_time:.3f}s")
        logger.info(f"并行处理时间: {parallel_time:.3f}s")
        logger.info(f"加速比: {speedup:.2f}x")
        
        return speedup
    
    def demo_memory_optimization(self):
        """演示内存管理优化"""
        logger.info("\n=== 内存管理优化演示 ===")
        
        # 创建对象池
        class TestObject:
            def __init__(self):
                self.data = np.zeros(1000)
                self.processed = False
            
            def reset(self):
                self.data.fill(0)
                self.processed = False
            
            def process(self):
                self.data[:] = np.random.random(1000)
                self.processed = True
        
        # 创建对象池
        memory_manager.create_pool(
            'test_object_pool',
            TestObject,
            reset_func=lambda obj: obj.reset(),
            max_size=100,
            initial_size=20
        )
        
        # 测试对象池性能
        with memory_monitor(threshold_mb=100):
            # 使用对象池
            start_time = time.time()
            for _ in range(1000):
                with managed_object('test_object_pool') as obj:
                    obj.process()
            pool_time = time.time() - start_time
            
            # 不使用对象池
            start_time = time.time()
            for _ in range(1000):
                obj = TestObject()
                obj.process()
            direct_time = time.time() - start_time
        
        # 获取内存使用情况
        memory_usage = memory_manager.get_memory_usage()
        pool_stats = memory_manager.get_pool_stats()
        
        logger.info(f"使用对象池时间: {pool_time:.3f}s")
        logger.info(f"直接创建对象时间: {direct_time:.3f}s")
        logger.info(f"内存使用: {memory_usage['rss_mb']:.2f}MB")
        logger.info(f"对象池命中率: {pool_stats['test_object_pool']['hit_rate']:.2%}")
        
        return pool_time, direct_time
    
    def demo_caching_system(self):
        """演示缓存系统"""
        logger.info("\n=== 缓存系统演示 ===")
        
        # 创建缓存函数
        @cached(maxsize=100, ttl=60)
        def fibonacci(n):
            """计算斐波那契数列（递归版本，用于演示缓存效果）"""
            if n <= 1:
                return n
            return fibonacci(n - 1) + fibonacci(n - 2)
        
        # 测试缓存效果
        test_values = [30, 35, 30, 32, 35, 28]
        
        start_time = time.time()
        results = []
        for n in test_values:
            result = fibonacci(n)
            results.append(result)
        cache_time = time.time() - start_time
        
        # 获取缓存统计
        cache_info = fibonacci.cache_info()
        
        logger.info(f"缓存计算时间: {cache_time:.3f}s")
        logger.info(f"缓存命中率: {cache_info['hit_rate']:.2%}")
        logger.info(f"缓存大小: {cache_info['size']}/{cache_info['maxsize']}")
        
        # 演示多级缓存
        with cache_context(cache_dir="./demo_cache") as cache:
            # 存储大型数据到缓存
            large_data = np.random.random((1000, 1000))
            cache.put('large_matrix', large_data)
            
            # 从缓存读取
            retrieved_data = cache.get('large_matrix')
            
            logger.info(f"大型数据缓存成功: {retrieved_data is not None}")
            logger.info(f"数据形状: {retrieved_data.shape if retrieved_data is not None else 'None'}")
        
        return cache_info
    
    def demo_simulation_optimization(self):
        """演示仿真优化"""
        logger.info("\n=== 仿真优化演示 ===")
        
        # 创建优化的仿真设置
        settings = Settings()
        settings.set('Number_patients', 1000)  # 较小规模用于演示
        settings.set('Simulation.EnableMultiprocessing', True)
        settings.set('Simulation.NumProcesses', 2)
        
        # 创建仿真实例
        simulation = Simulation(settings)
        
        # 运行优化的仿真
        with performance_monitor.monitor("optimized_simulation"):
            results = simulation.run(years=10)
        
        # 获取性能指标
        sim_metrics = performance_monitor.get_metrics("optimized_simulation")
        
        logger.info(f"仿真执行时间: {sim_metrics.execution_time:.3f}s")
        logger.info(f"内存使用: {sim_metrics.memory_usage:.2f}MB")
        logger.info(f"患者数量: {len(simulation.patients)}")
        
        return sim_metrics
    
    def demo_comprehensive_optimization(self):
        """演示综合性能优化策略"""
        logger.info("\n=== 综合性能优化演示 ===")
        
        # 定义多个仿真参数组合
        parameter_sets = [
            {'num_patients': 500, 'screening_strategy': 'colonoscopy', 'years': 5},
            {'num_patients': 500, 'screening_strategy': 'fit', 'years': 5},
            {'num_patients': 500, 'screening_strategy': 'sigmoidoscopy', 'years': 5},
        ]
        
        def run_single_simulation(num_patients, screening_strategy, years):
            """运行单个仿真"""
            settings = Settings()
            settings.set('Number_patients', num_patients)
            settings.set('Screening.DefaultStrategy', screening_strategy)
            
            simulation = Simulation(settings)
            return simulation.run(years)
        
        # 使用进度跟踪器
        progress_tracker = ProgressTracker(len(parameter_sets))
        
        # 并行运行多个仿真
        start_time = time.time()
        results = parallel_simulation(
            run_single_simulation,
            parameter_sets,
            max_workers=3
        )
        total_time = time.time() - start_time
        
        # 更新进度
        progress_tracker.update(len(parameter_sets))
        
        successful_results = [r for r in results if r is not None]
        
        logger.info(f"并行仿真完成时间: {total_time:.3f}s")
        logger.info(f"成功仿真数量: {len(successful_results)}/{len(parameter_sets)}")
        
        return successful_results
    
    def generate_performance_report(self):
        """生成性能报告"""
        logger.info("\n=== 生成性能报告 ===")
        
        # 获取所有性能指标
        all_metrics = performance_monitor.get_all_metrics()
        
        # 保存性能报告
        report_file = "performance_report.json"
        performance_monitor.save_metrics(report_file)
        
        # 打印摘要
        logger.info("性能报告摘要:")
        for name, metrics in all_metrics.items():
            logger.info(f"  {name}:")
            logger.info(f"    执行时间: {metrics.get('execution_time', 0):.3f}s")
            logger.info(f"    内存使用: {metrics.get('memory_usage', 0):.2f}MB")
            if 'cache_hit_rate' in metrics:
                logger.info(f"    缓存命中率: {metrics['cache_hit_rate']:.2%}")
        
        logger.info(f"详细报告已保存到: {report_file}")
        
        return all_metrics


def main():
    """主函数"""
    logger.info("开始CMOST性能优化演示")
    
    # 创建演示实例
    demo = PerformanceOptimizationDemo()
    
    try:
        # 运行各种演示
        demo.demo_basic_monitoring()
        demo.demo_parallel_processing()
        demo.demo_memory_optimization()
        demo.demo_caching_system()
        demo.demo_simulation_optimization()
        demo.demo_comprehensive_optimization()
        
        # 生成性能报告
        demo.generate_performance_report()
        
        logger.info("\n=== 演示完成 ===")
        logger.info("所有性能优化功能演示成功完成！")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
        raise
    
    finally:
        # 清理资源
        memory_manager.cleanup_pools()
        logger.info("资源清理完成")


if __name__ == "__main__":
    main()
