# CMOST 功能增强说明

本文档说明了CMOST项目中新增的功能和改进。

## 新增功能概览

### 1. 人口参数选择功能 ✅

**位置**: 文件选择步骤 → 人口参数配置

**功能描述**:
- 支持自然人群和出生队列两种人口类型
- 支持Excel和JSON格式的人口参数文件
- 可配置性别比例和不同年龄段人口占比
- 提供人口参数模板创建功能

**使用方法**:
1. 在文件选择步骤中找到"人口参数配置"区域
2. 选择人口类型（自然人群/出生队列）
3. 可以使用默认参数或加载自定义参数文件
4. 点击"创建模板"按钮生成参数文件模板
5. 点击"浏览..."按钮加载已有的参数文件

**文件格式示例**:

JSON格式:
```json
{
  "population_type": "natural",
  "male_proportion": 0.51,
  "age_distribution": {
    "0-4": 0.055,
    "5-9": 0.055,
    ...
  },
  "birth_cohort": {
    "birth_year": 1970,
    "cohort_size": 100000,
    "follow_years": 80
  }
}
```

### 2. 增强UI集成到main.py ✅

**位置**: main.py --gui界面

**功能描述**:
- 在主GUI界面添加了"运行模拟"按钮
- 点击按钮启动完整的增强仿真界面
- 提供完整的工作流程：文件选择 → 调参配置 → 筛查配置 → 输出配置 → 模拟执行

**使用方法**:
1. 运行 `python main.py --gui`
2. 在主界面点击"运行模拟"按钮
3. 按照工作流程逐步配置各项参数
4. 最后执行仿真模拟

### 3. 调参结果可视化和日志 ✅

**位置**: 调参配置步骤 → 调参结果区域

**功能描述**:
- **参数结果标签页**: 显示最优参数值和调参统计信息
- **可视化对比标签页**: 提供多种图表生成功能
  - 收敛历史图：误差收敛曲线、参数变化曲线、误差分布直方图
  - 参数对比图：最优参数值柱状图、参数重要性饼图
  - 完整HTML报告：包含所有图表和分析结果
- **调参日志标签页**: 显示详细的调参过程日志
  - 实时日志记录：参数变化、误差变化、收敛状态
  - 日志管理功能：刷新、导出、清空
  - 会话摘要：调参方法、迭代次数、执行时间等

**使用方法**:
1. 在调参配置步骤中设置调参参数
2. 点击"开始调参"按钮
3. 调参完成后，在调参结果区域查看三个标签页：
   - **参数结果**: 查看最优参数和统计信息
   - **可视化对比**: 点击按钮生成各种图表
   - **调参日志**: 查看详细的调参过程记录

### 4. 完善筛查工具参数配置 ✅

**位置**: 筛查配置步骤 → 筛查工具参数

**功能描述**:
- 添加了阳性后诊断性肠镜依从性配置
- 添加了监测随访依从性配置
- 完善了贯序筛查中次要筛查工具的参数配置
- 增加了更多详细参数：检查时间、准备时间、不适感评分、假阳性率

**新增参数**:
- 阳性后肠镜依从性 (%)
- 监测随访依从性 (%)
- 检查时间 (分钟)
- 准备时间 (小时)
- 不适感评分 (1-10)
- 假阳性率 (%)

### 5. 修复结果输出路径 ✅

**位置**: 输出配置步骤 → 输出目录设置

**功能描述**:
- 默认输出目录设置为项目根目录下的`results`文件夹
- 自动创建输出目录和子目录结构
- 提供目录创建和验证功能
- 确保结果文件正确输出到指定位置

**目录结构**:
```
results/
├── reports/     # 报告文件
├── data/        # 数据文件
├── charts/      # 图表文件
├── logs/        # 日志文件
└── calibration/ # 调参结果
```

## 技术实现

### 新增模块

1. **cmost/config/population_manager.py**: 人口参数管理
2. **cmost/calibration/visualization.py**: 调参结果可视化
3. **cmost/calibration/logging_manager.py**: 调参日志管理

### 修改的文件

1. **main.py**: 添加"运行模拟"按钮
2. **cmost/ui/enhanced_main_window.py**: 集成所有新功能
3. **cmost/ui/screening_config_panel.py**: 完善筛查参数配置
4. **cmost/ui/output_config_panel.py**: 修复输出路径问题
5. **cmost/config/defaults.py**: 添加人口参数默认值

## 测试

### 运行测试脚本

```bash
# 测试所有增强功能
python test_enhancements.py

# 测试调参UI功能
python test_calibration_ui.py
```

### 手动测试步骤

1. **启动增强UI**:
   ```bash
   python main.py --gui
   # 点击"运行模拟"按钮
   ```

2. **测试人口参数功能**:
   - 在文件选择步骤中配置人口参数
   - 创建和加载人口参数模板

3. **测试调参功能**:
   - 在调参配置步骤中开始调参
   - 查看调参结果的三个标签页
   - 生成可视化图表和报告

4. **测试筛查配置**:
   - 配置筛查工具参数
   - 验证新增的依从性参数

5. **测试输出配置**:
   - 验证默认输出目录为`results`
   - 测试目录创建功能

## 依赖要求

确保安装了以下依赖包：
- matplotlib >= 3.4.0 (用于可视化)
- pandas >= 1.3.0 (用于Excel文件处理)
- openpyxl >= 3.0.9 (用于Excel文件读写)

## 故障排除

### 常见问题

1. **导入错误**: 确保所有依赖包已安装
2. **文件权限错误**: 确保对results目录有写权限
3. **可视化失败**: 检查matplotlib是否正确安装
4. **Excel文件错误**: 确保openpyxl和pandas已安装

### 日志文件位置

- 调参日志: `calibration_logs/`
- 可视化输出: `calibration_results/plots/`
- 系统日志: 控制台输出

## 更新日志

- **2025-07-17**: 完成所有功能增强
  - ✅ 人口参数选择功能
  - ✅ 增强UI集成
  - ✅ 调参结果可视化和日志
  - ✅ 筛查工具参数配置完善
  - ✅ 结果输出路径修复
