# CMOST统一入口使用指南

## 概述

CMOST v2.0引入了统一入口文件`main.py`，提供了更便捷的访问方式和Excel配置支持。本指南将详细介绍如何使用这些新功能。

## 统一入口功能

### 基本用法

```bash
# 显示帮助信息
python main.py --help

# 显示版本信息
python main.py --version
```

### 启动选项

#### 1. 图形用户界面
```bash
python main.py --gui
```
启动基于Tkinter的图形界面，适合不熟悉命令行的用户。

#### 2. 命令行界面
```bash
python main.py --cli
```
启动命令行界面，提供完整的CLI功能。

#### 3. 交互式Python环境
```bash
python main.py --interactive
```
启动交互式Python环境，预加载常用模块：
- `simulation`: 仿真引擎
- `settings`: 配置管理
- `Settings`: 配置类
- `np`: NumPy
- `pd`: Pandas

#### 4. 快速演示
```bash
python main.py --demo
```
运行快速演示，展示CMOST的基本功能和配置选项。

### 配置选项

#### 指定配置文件
```bash
python main.py --config my_config.json --gui
python main.py --config my_config.xlsx --demo
```

#### 详细输出
```bash
python main.py --verbose --demo
```

## Excel配置支持

### 功能特点

1. **多工作表支持**: 不同类型的参数分布在不同的工作表中
2. **自动格式化**: 生成的Excel文件具有专业的格式和样式
3. **参数描述**: 每个参数都有详细的中文描述
4. **类型标注**: 显示参数的数据类型，便于正确输入

### 生成Excel模板

#### 使用统一入口
```bash
# 通过CLI生成
python -m cmost.cli config --template excel --output my_config.xlsx
```

#### 使用Python API
```python
from cmost.config.excel_templates import create_excel_template

# 生成基础模板
create_excel_template('basic', 'config_template.xlsx')
```

### Excel文件结构

生成的Excel文件包含以下工作表：

1. **Settings**: 主要应用设置
2. **ModelParameters**: 疾病模型参数
3. **Screening**: 筛查策略设置
4. **Simulation**: 仿真运行参数
5. **UI**: 用户界面设置
6. **Instructions**: 使用说明

### 加载Excel配置

```python
from cmost.config.settings import Settings

# 创建设置对象
settings = Settings()

# 从Excel文件加载配置
success = settings.load_settings('my_config.xlsx')
if success:
    print("配置加载成功")
else:
    print("配置加载失败")
```

### 保存Excel配置

```python
# 修改配置
settings.set('Number_patients', 50000)
settings.set('Simulation.RandomSeed', 123)

# 保存到Excel文件
settings.save_settings('updated_config.xlsx')
```

### Excel配置验证

```python
from cmost.config.excel_templates import validate_excel_config

# 验证Excel配置文件
result = validate_excel_config('my_config.xlsx')

if result['valid']:
    print(f"配置文件有效，包含{result['parameters_count']}个参数")
    print(f"工作表: {result['sheets_found']}")
else:
    print("配置文件无效:")
    for error in result['errors']:
        print(f"  错误: {error}")
    
    if result['warnings']:
        print("警告:")
        for warning in result['warnings']:
            print(f"  警告: {warning}")
```

## 支持的配置格式

| 格式 | 扩展名 | 描述 | 新功能 |
|------|--------|------|--------|
| JSON | `.json` | 标准JSON格式 | ✓ |
| YAML | `.yaml`, `.yml` | 人类可读的YAML格式 | ✓ |
| MATLAB | `.mat` | MATLAB数据格式 | ✓ |
| Excel | `.xlsx`, `.xls` | Excel电子表格格式 | ✅ 新增 |

## 实际使用示例

### 示例1: 创建和使用Excel配置

```bash
# 1. 生成Excel模板
python -m cmost.cli config --template excel --output screening_study.xlsx

# 2. 在Excel中编辑参数（使用Excel软件）
# 3. 使用配置运行仿真
python main.py --config screening_study.xlsx --demo
```

### 示例2: 批量配置管理

```python
from cmost.config.settings import Settings
from cmost.config.excel_templates import create_excel_template

# 为不同的研究场景创建配置
scenarios = ['baseline', 'high_risk', 'low_compliance']

for scenario in scenarios:
    # 生成模板
    template_path = f"{scenario}_config.xlsx"
    create_excel_template('basic', template_path)
    
    # 加载并修改配置
    settings = Settings()
    settings.load_settings(template_path)
    
    if scenario == 'high_risk':
        settings.set('ModelParameters.early_mult', 0.04)  # 双倍风险
    elif scenario == 'low_compliance':
        settings.set('Screening.ScreeningCompliance', 0.3)  # 低依从性
    
    # 保存修改后的配置
    settings.save_settings(template_path)
    print(f"已创建{scenario}场景配置")
```

### 示例3: 配置文件转换

```python
from cmost.config.settings import Settings

# 从JSON转换为Excel
settings = Settings()
settings.load_settings('old_config.json')
settings.save_settings('new_config.xlsx')

# 从Excel转换为YAML
settings.load_settings('config.xlsx')
settings.save_settings('config.yaml')
```

## 故障排除

### 常见问题

1. **Excel功能不可用**
   ```bash
   pip install pandas openpyxl
   ```

2. **图形界面启动失败**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install python3-tk
   
   # 或使用conda
   conda install tk
   ```

3. **配置文件加载失败**
   - 检查文件路径是否正确
   - 确认文件格式是否支持
   - 使用`validate_excel_config()`验证Excel文件

### 调试模式

```bash
# 启用详细输出
python main.py --verbose --demo

# 查看配置加载过程
python main.py --verbose --config my_config.xlsx --demo
```

## 最佳实践

1. **使用Excel模板**: 始终从生成的模板开始，确保格式正确
2. **备份配置**: 在修改重要配置前创建备份
3. **验证配置**: 使用验证功能检查配置文件的完整性
4. **版本控制**: 将配置文件纳入版本控制系统
5. **文档化**: 在Excel的Instructions工作表中记录修改说明

## 更多资源

- [CMOST用户手册](./user_manual.md)
- [配置参数参考](./configuration_reference.md)
- [API文档](./api_reference.md)
- [示例配置文件](../examples/)
