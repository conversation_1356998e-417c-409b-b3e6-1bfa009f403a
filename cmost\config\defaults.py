"""
Default parameters for the CMOST application.
This module provides default values for all configurable parameters.
"""

import os
from pathlib import Path
from typing import Dict, Any, List

# Get the base directory of the application
BASE_DIR = Path(__file__).parent.parent.absolute()

# Default application settings
DEFAULT_SETTINGS = {
    'Settings_Name': 'Default',
    'Comment': 'no comment please',
    'Identification': 'This_is_a_genuine_PolypCalculator_File',
    'NumberPatientsValues': [10000, 25000, 50000, 100000, 1000000, 10000000],
    'ResultsPath': os.path.join(BASE_DIR, 'Results'),
    'SettingsPath': os.path.join(BASE_DIR, 'Settings'),
    'DispFlag': True,
    'Version': '1.0.0',
}

# Default simulation settings
SIMULATION_SETTINGS = {
    'MaxAge': 100,
    'StartAge': 20,
    'TimeStep': 1.0,
    'RandomSeed': 42,
    'EnableMultiprocessing': True,
    'NumProcesses': 0,  # 0 means use all available cores
}

# Default model parameters
MODEL_PARAMETERS = {
    # Early adenoma parameters
    'early_mult': 0.02,
    'early_width': 15.0,
    'early_center': 55.0,
    
    # Advanced adenoma parameters
    'adv_mult': 0.01,
    'adv_width': 10.0,
    'adv_center': 65.0,
    
    # Cancer parameters
    'cancer_mult': 0.005,
    'cancer_width': 8.0,
    'cancer_center': 70.0,
    'preclinical_dwell_time': 3.0,
    
    # Location-specific risk factors
    'location_factors': {
        1: 1.2,  # Rectum
        2: 1.1,  # Sigmoid colon
        3: 1.0,  # Descending colon
        4: 0.9,  # Transverse colon
        5: 0.8,  # Ascending colon
        6: 0.8   # Cecum
    },
    
    # Gender-specific risk factors
    'gender_factors': {
        'M': 1.2,  # Male
        'F': 0.8   # Female
    }
}

# Default screening settings
SCREENING_SETTINGS = {
    'EnableScreening': True,
    'ScreeningAges': [50, 55, 60, 65, 70, 75],
    'ScreeningCompliance': 0.6,
    'FollowupYears': 10,
    'FollowupCompliance': 0.8,
    
    # Screening test characteristics
    'Tests': {
        'Colonoscopy': {
            'Sensitivity': {
                'early_adenoma': 0.85,
                'advanced_adenoma': 0.95,
                'cancer': 0.95
            },
            'Specificity': 0.90,
            'Cost': 1000,
            'Complications': 0.002
        },
        'FIT': {
            'Sensitivity': {
                'early_adenoma': 0.10,
                'advanced_adenoma': 0.40,
                'cancer': 0.70
            },
            'Specificity': 0.95,
            'Cost': 20,
            'Complications': 0.0001
        },
        'other': {
            'Sensitivity': {
                'early_adenoma': 0.10,
                'advanced_adenoma': 0.40,
                'cancer': 0.70
            },
            'Specificity': 0.95,
            'Cost': 20,
            'Complications': 0.0001
    }
}

# Default UI settings
UI_SETTINGS = {
    'Theme': 'default',
    'FontSize': 10,
    'ShowToolbar': True,
    'DefaultView': 'simulation',
    'AutoSave': True,
    'AutoSaveInterval': 10  # minutes
}

# Default population parameters
POPULATION_PARAMETERS = {
    'PopulationType': 'natural',  # 'natural' or 'birth_cohort'
    'PopulationFile': None,  # Path to population parameter file

    # Default population structure (if no file specified)
    'DefaultPopulation': {
        'male_proportion': 0.51,  # 51% male, 49% female
        'age_distribution': {
            # Age group: proportion
            '0-4': 0.055,
            '5-9': 0.055,
            '10-14': 0.055,
            '15-19': 0.055,
            '20-24': 0.065,
            '25-29': 0.075,
            '30-34': 0.085,
            '35-39': 0.085,
            '40-44': 0.080,
            '45-49': 0.075,
            '50-54': 0.070,
            '55-59': 0.065,
            '60-64': 0.055,
            '65-69': 0.045,
            '70-74': 0.035,
            '75-79': 0.025,
            '80-84': 0.015,
            '85-89': 0.008,
            '90-94': 0.003,
            '95-99': 0.001,
            '100+': 0.0002
        }
    },

    # Birth cohort specific parameters
    'BirthCohort': {
        'birth_year': 1970,
        'cohort_size': 100000,
        'follow_years': 80
    }
}

# Excel export/import settings
EXCEL_SETTINGS = {
    'DefaultExportPath': os.path.join(BASE_DIR, 'exports'),
    'DefaultImportPath': os.path.join(BASE_DIR, 'imports'),
    'ExcelEngine': 'openpyxl',
    'IncludeIndex': False,
    'SheetNames': {
        'settings': 'Settings',
        'model_parameters': 'ModelParameters',
        'screening': 'Screening',
        'simulation': 'Simulation',
        'ui': 'UI'
    },
    'ExportFormat': {
        'float_format': '%.6f',
        'date_format': 'YYYY-MM-DD',
        'datetime_format': 'YYYY-MM-DD HH:MM:SS'
    }
}

# Default risk factor settings
RISK_FACTORS = {
    'family_history_prevalence': 0.10,  # 10% prevalence
    'family_history_risk_multiplier': 2.0,
    'smoking_prevalence': 0.15,
    'smoking_risk_multiplier': 1.5,
    'obesity_prevalence': 0.30,
    'obesity_risk_multiplier': 1.3
}

# Default benchmark values
BENCHMARKS = {
    'early_adenoma_prevalence': {
        'M': {  # Male prevalence by age
            20: 5.0,
            30: 10.0,
            40: 20.0,
            50: 30.0,
            60: 40.0,
            70: 45.0,
            80: 50.0
        },
        'F': {  # Female prevalence by age
            20: 3.0,
            30: 7.0,
            40: 15.0,
            50: 25.0,
            60: 35.0,
            70: 40.0,
            80: 45.0
        }
    },
    'advanced_adenoma_prevalence': {
        'M': {  # Male prevalence by age
            20: 0.5,
            30: 1.0,
            40: 3.0,
            50: 5.0,
            60: 8.0,
            70: 10.0,
            80: 12.0
        },
        'F': {  # Female prevalence by age
            20: 0.3,
            30: 0.7,
            40: 2.0,
            50: 4.0,
            60: 6.0,
            70: 8.0,
            80: 10.0
        }
    },
    'cancer_incidence': {
        'M': {  # Male incidence per 100,000 by age
            20: 0.1,
            30: 1.0,
            40: 10.0,
            50: 50.0,
            60: 100.0,
            70: 150.0,
            80: 200.0
        },
        'F': {  # Female incidence per 100,000 by age
            20: 0.1,
            30: 0.8,
            40: 8.0,
            50: 40.0,
            60: 80.0,
            70: 120.0,
            80: 160.0
        }
    }
}

# Combine all default settings
def get_all_defaults() -> Dict[str, Any]:
    """
    Get all default settings combined into a single dictionary.

    Returns:
        Dict[str, Any]: All default settings
    """
    return {
        **DEFAULT_SETTINGS,
        'Simulation': SIMULATION_SETTINGS,
        'ModelParameters': MODEL_PARAMETERS,
        'Screening': SCREENING_SETTINGS,
        'UI': UI_SETTINGS,
        'Population': POPULATION_PARAMETERS,
        'RiskFactors': RISK_FACTORS,
        'Benchmarks': BENCHMARKS
    }