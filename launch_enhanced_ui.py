#!/usr/bin/env python3
"""
Launch script for the enhanced CMOST UI.

This script launches the enhanced main window with complete workflow support.
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from cmost.ui.enhanced_main_window import EnhancedMainWindow
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖项已正确安装")
    sys.exit(1)


def main():
    """Main function to launch the enhanced UI."""
    try:
        # Create root window
        root = tk.Tk()
        
        # Set window properties
        root.title("CMOST - 结直肠癌筛查仿真工具")
        root.geometry("1200x800")
        
        # Center the window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (root.winfo_screenheight() // 2) - (800 // 2)
        root.geometry(f"1200x800+{x}+{y}")
        
        # Set minimum size
        root.minsize(1000, 600)
        
        # Create and run the application
        app = EnhancedMainWindow(root)
        
        print("CMOST Enhanced UI 已启动")
        print("功能特性:")
        print("1. 完整的工作流程界面")
        print("2. 文件选择和基本配置")
        print("3. 模型调参功能")
        print("4. 筛查策略配置")
        print("5. 输出配置管理")
        print("6. 模拟执行和监控")
        
        # Start the main loop
        root.mainloop()
        
    except Exception as e:
        error_msg = f"启动应用程序时发生错误:\n{str(e)}"
        print(error_msg)
        
        # Try to show error in GUI if possible
        try:
            root = tk.Tk()
            root.withdraw()  # Hide the main window
            messagebox.showerror("启动错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
