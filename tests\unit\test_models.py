"""
Unit tests for CMOST data models.
"""

import pytest
import numpy as np
from datetime import datetime

from cmost.models.patient import Patient
from cmost.models.polyp import Polyp
from cmost.models.cancer import Cancer


class TestPatient:
    """Test cases for Patient model."""
    
    def test_patient_creation(self):
        """Test basic patient creation."""
        patient = Patient(
            id=1,
            age=50,
            gender='M',
            risk_factors={'smoking': 1.5, 'family_history': 2.0}
        )
        
        assert patient.id == 1
        assert patient.age == 50
        assert patient.gender == 'M'
        assert patient.individual_risk == 3.0  # 1.5 * 2.0
        
    def test_patient_risk_calculation(self):
        """Test individual risk calculation."""
        patient = Patient(
            id=1,
            age=50,
            gender='M',
            risk_factors={'smoking': 1.5, 'bmi': 1.2, 'family_history': 2.0}
        )
        
        expected_risk = 1.5 * 1.2 * 2.0  # 3.6
        assert patient.individual_risk == expected_risk
        
    def test_patient_age_progression(self):
        """Test patient aging."""
        patient = Patient(id=1, age=50, gender='M')
        initial_age = patient.age
        
        # Mock progression model
        class MockProgressionModel:
            def progress_polyp(self, *args):
                return args[0], False  # No progression
                
        patient.progress(5, MockProgressionModel())
        assert patient.age == initial_age + 5
        
    def test_patient_alive_status(self):
        """Test patient alive status."""
        patient = Patient(id=1, age=50, gender='M')
        
        # Patient should be alive initially
        assert patient.is_alive(2023)
        
        # Set death year
        patient.death_year = 2025
        assert patient.is_alive(2024)  # Still alive
        assert not patient.is_alive(2025)  # Dead
        assert not patient.is_alive(2026)  # Still dead


class TestPolyp:
    """Test cases for Polyp model."""
    
    def test_polyp_creation(self):
        """Test basic polyp creation."""
        polyp = Polyp(
            id=1,
            location=2,
            size=0.5,
            stage=1,
            patient_id=1,
            patient_gender='M'
        )
        
        assert polyp.id == 1
        assert polyp.location == 2
        assert polyp.size == 0.5
        assert polyp.stage == 1
        assert polyp.patient_id == 1
        assert polyp.patient_gender == 'M'
        
    def test_polyp_advanced_status(self):
        """Test advanced polyp detection."""
        # Early stage polyp
        early_polyp = Polyp(id=1, location=1, size=0.3, stage=2, patient_id=1, patient_gender='M')
        assert not early_polyp.is_advanced
        
        # Advanced stage polyp
        advanced_polyp = Polyp(id=2, location=1, size=1.5, stage=5, patient_id=1, patient_gender='M')
        assert advanced_polyp.is_advanced
        
    def test_polyp_diminutive_status(self):
        """Test diminutive polyp detection."""
        # Diminutive polyp
        diminutive_polyp = Polyp(id=1, location=1, size=0.2, stage=1, patient_id=1, patient_gender='M')
        assert diminutive_polyp.is_diminutive
        
        # Non-diminutive polyp
        large_polyp = Polyp(id=2, location=1, size=0.8, stage=3, patient_id=1, patient_gender='M')
        assert not large_polyp.is_diminutive


class TestCancer:
    """Test cases for Cancer model."""
    
    def test_cancer_creation(self):
        """Test basic cancer creation."""
        cancer = Cancer(
            id=1,
            location=1,
            stage=7,  # Stage I
            patient_id=1,
            patient_gender='M'
        )
        
        assert cancer.id == 1
        assert cancer.location == 1
        assert cancer.stage == 7
        assert cancer.patient_id == 1
        assert cancer.patient_gender == 'M'
        assert not cancer.treatment_applied
        
    def test_cancer_from_polyp(self):
        """Test cancer creation from polyp."""
        polyp = Polyp(
            id=1,
            location=2,
            size=1.5,
            stage=6,
            patient_id=1,
            patient_gender='F'
        )
        
        cancer = Cancer.from_polyp(polyp)
        
        assert cancer.location == polyp.location
        assert cancer.patient_id == polyp.patient_id
        assert cancer.patient_gender == polyp.patient_gender
        assert cancer.source_polyp_id == polyp.id
        assert cancer.stage == 7  # Stage I cancer
        
    def test_cancer_treatment(self):
        """Test cancer treatment application."""
        cancer = Cancer(id=1, location=1, stage=7, patient_id=1, patient_gender='M')
        
        # Apply treatment
        cancer.apply_treatment(2023, 'surgery')
        
        assert cancer.treatment_applied
        assert cancer.treatment_year == 2023
        assert cancer.treatment_type == 'surgery'
        
    def test_cancer_survival_estimation(self):
        """Test cancer survival estimation."""
        # Stage I cancer
        stage1_cancer = Cancer(id=1, location=1, stage=7, patient_id=1, patient_gender='M')
        survival1 = stage1_cancer.estimate_survival()
        
        # Stage IV cancer
        stage4_cancer = Cancer(id=2, location=1, stage=10, patient_id=1, patient_gender='M')
        survival4 = stage4_cancer.estimate_survival()
        
        # Stage I should have better survival than Stage IV
        assert survival1 > survival4


if __name__ == '__main__':
    pytest.main([__file__])
