"""
Unit tests for serrated lesion functionality in CMOST.
"""

import unittest
import numpy as np
from cmost.models.serrated_lesion import SerratedLesion
from cmost.models.patient import Patient
from cmost.core.progression import ProgressionModel


class TestSerratedLesion(unittest.TestCase):
    """Test cases for SerratedLesion class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.lesion = SerratedLesion(
            id=1,
            location=5,  # Ascending colon
            size=0.8,
            stage=1,
            patient_id=1,
            patient_gender='F'
        )
    
    def test_lesion_creation(self):
        """Test serrated lesion creation."""
        self.assertEqual(self.lesion.id, 1)
        self.assertEqual(self.lesion.location, 5)
        self.assertEqual(self.lesion.size, 0.8)
        self.assertEqual(self.lesion.stage, 1)
        self.assertEqual(self.lesion.patient_gender, 'F')
        self.assertFalse(self.lesion.has_dysplasia)
    
    def test_is_large_property(self):
        """Test is_large property."""
        small_lesion = SerratedLesion(1, 1, 0.5, 1, 1, 'M')
        large_lesion = SerratedLesion(2, 1, 1.5, 2, 1, 'M')
        
        self.assertFalse(small_lesion.is_large)
        self.assertTrue(large_lesion.is_large)
    
    def test_is_advanced_property(self):
        """Test is_advanced property."""
        early_lesion = SerratedLesion(1, 1, 0.5, 1, 1, 'M')
        advanced_lesion = SerratedLesion(2, 1, 1.5, 3, 1, 'M', has_dysplasia=True)
        
        self.assertFalse(early_lesion.is_advanced)
        self.assertTrue(advanced_lesion.is_advanced)
    
    def test_cancer_risk_level(self):
        """Test cancer risk level assessment."""
        low_risk = SerratedLesion(1, 1, 0.5, 1, 1, 'M')
        moderate_risk = SerratedLesion(2, 1, 1.5, 2, 1, 'M')
        high_risk = SerratedLesion(3, 1, 1.5, 2, 1, 'M', has_dysplasia=True)
        very_high_risk = SerratedLesion(4, 1, 2.0, 4, 1, 'M')
        
        self.assertEqual(low_risk.cancer_risk_level, 'low')
        self.assertEqual(moderate_risk.cancer_risk_level, 'moderate')
        self.assertEqual(high_risk.cancer_risk_level, 'high')
        self.assertEqual(very_high_risk.cancer_risk_level, 'very_high')
    
    def test_screening_detectability(self):
        """Test screening detectability for different tests."""
        # Test colonoscopy detectability
        colonoscopy_detect = self.lesion.get_screening_detectability('colonoscopy')
        self.assertGreater(colonoscopy_detect, 0)
        self.assertLessEqual(colonoscopy_detect, 1.0)
        
        # Test sigmoidoscopy detectability (should be 0 for proximal lesion)
        sigmoidoscopy_detect = self.lesion.get_screening_detectability('sigmoidoscopy')
        self.assertEqual(sigmoidoscopy_detect, 0.0)  # Location 5 is proximal
        
        # Test FIT detectability
        fit_detect = self.lesion.get_screening_detectability('fit')
        self.assertGreater(fit_detect, 0)
        self.assertLess(fit_detect, colonoscopy_detect)  # FIT should be less sensitive
    
    def test_to_cancer_conversion(self):
        """Test conversion to cancer."""
        cancer = self.lesion.to_cancer()
        
        self.assertEqual(cancer.id, self.lesion.id)
        self.assertEqual(cancer.location, self.lesion.location)
        self.assertEqual(cancer.stage, 7)  # Stage I cancer
        self.assertEqual(cancer.patient_id, self.lesion.patient_id)
        self.assertEqual(cancer.patient_gender, self.lesion.patient_gender)
        self.assertEqual(cancer.source_polyp_id, self.lesion.id)
    
    def test_to_dict_and_from_dict(self):
        """Test serialization and deserialization."""
        lesion_dict = self.lesion.to_dict()
        reconstructed = SerratedLesion.from_dict(lesion_dict)
        
        self.assertEqual(reconstructed.id, self.lesion.id)
        self.assertEqual(reconstructed.location, self.lesion.location)
        self.assertEqual(reconstructed.size, self.lesion.size)
        self.assertEqual(reconstructed.stage, self.lesion.stage)
        self.assertEqual(reconstructed.patient_id, self.lesion.patient_id)
        self.assertEqual(reconstructed.patient_gender, self.lesion.patient_gender)
        self.assertEqual(reconstructed.has_dysplasia, self.lesion.has_dysplasia)


class TestSerratedLesionProgression(unittest.TestCase):
    """Test cases for serrated lesion progression."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.progression_model = ProgressionModel()
        self.lesion = SerratedLesion(
            id=1,
            location=5,
            size=0.8,
            stage=1,
            patient_id=1,
            patient_gender='F'
        )
    
    def test_progression_model_method(self):
        """Test progression model method for serrated lesions."""
        # Test valid progression
        new_stage, is_cancer = self.progression_model.progress_serrated_lesion(
            current_stage=1,
            location=5,
            gender='F',
            individual_risk=1.0,
            age=60,
            has_dysplasia=False
        )
        
        self.assertIn(new_stage, [0, 1, 2])  # Can regress, stay same, or progress
        self.assertIsInstance(is_cancer, bool)
        
        # Test progression from stage 4 to cancer
        new_stage, is_cancer = self.progression_model.progress_serrated_lesion(
            current_stage=4,
            location=5,
            gender='F',
            individual_risk=2.0,  # High risk
            age=70,
            has_dysplasia=True
        )
        
        # With high risk and dysplasia, should have chance of cancer progression
        if is_cancer:
            self.assertEqual(new_stage, 7)  # Stage I cancer
    
    def test_lesion_progression_integration(self):
        """Test lesion progression integration."""
        # Set random seed for reproducible tests
        np.random.seed(42)
        
        # Progress lesion for multiple years
        progressed_to_cancer = self.lesion.progress(
            years=5,
            progression_model=self.progression_model,
            individual_risk=1.5,
            age=65
        )
        
        self.assertIsInstance(progressed_to_cancer, bool)
        # Stage should be valid
        self.assertGreaterEqual(self.lesion.stage, 0)
        self.assertLessEqual(self.lesion.stage, 7)


class TestPatientSerratedLesionIntegration(unittest.TestCase):
    """Test cases for patient-serrated lesion integration."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.patient = Patient(
            id=1,
            age=55,
            gender='F',
            risk_factors={'family_history': 1.5}
        )
    
    def test_add_serrated_lesion(self):
        """Test adding serrated lesion to patient."""
        lesion = self.patient.add_serrated_lesion(location=5, size=0.8)
        
        self.assertEqual(len(self.patient.serrated_lesions), 1)
        self.assertEqual(lesion.patient_id, self.patient.id)
        self.assertEqual(lesion.patient_gender, self.patient.gender)
        self.assertEqual(lesion.location, 5)
        self.assertEqual(lesion.size, 0.8)
    
    def test_remove_serrated_lesion(self):
        """Test removing serrated lesion from patient."""
        lesion = self.patient.add_serrated_lesion(location=5, size=0.8)
        lesion_id = lesion.id
        
        # Remove lesion
        removed = self.patient.remove_serrated_lesion(lesion_id)
        
        self.assertTrue(removed)
        self.assertEqual(len(self.patient.serrated_lesions), 0)
        
        # Try to remove non-existent lesion
        removed_again = self.patient.remove_serrated_lesion(lesion_id)
        self.assertFalse(removed_again)
    
    def test_get_serrated_lesions_by_location(self):
        """Test getting serrated lesions by location."""
        self.patient.add_serrated_lesion(location=5, size=0.8)
        self.patient.add_serrated_lesion(location=3, size=1.2)
        self.patient.add_serrated_lesion(location=5, size=0.6)
        
        location_5_lesions = self.patient.get_serrated_lesions_by_location(5)
        location_3_lesions = self.patient.get_serrated_lesions_by_location(3)
        
        self.assertEqual(len(location_5_lesions), 2)
        self.assertEqual(len(location_3_lesions), 1)
        self.assertTrue(all(l.location == 5 for l in location_5_lesions))
        self.assertTrue(all(l.location == 3 for l in location_3_lesions))
    
    def test_get_advanced_serrated_lesions(self):
        """Test getting advanced serrated lesions."""
        # Add non-advanced lesion
        self.patient.add_serrated_lesion(location=5, size=0.5)
        
        # Add advanced lesion
        advanced_lesion = self.patient.add_serrated_lesion(location=3, size=1.5)
        advanced_lesion.stage = 3
        advanced_lesion.has_dysplasia = True
        
        advanced_lesions = self.patient.get_advanced_serrated_lesions()
        
        self.assertEqual(len(advanced_lesions), 1)
        self.assertTrue(advanced_lesions[0].is_advanced)


if __name__ == '__main__':
    unittest.main()
