"""
性能优化工具模块

提供缓存管理、内存优化、性能监控等核心功能，用于提升CMOST仿真的执行效率。
"""

import time
import psutil
import threading
import functools
import gc
from typing import Any, Dict, List, Optional, Callable, Tuple, Union
from collections import OrderedDict, defaultdict
from dataclasses import dataclass, field
from contextlib import contextmanager
import logging
import weakref
import pickle
import json
from pathlib import Path


logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    execution_time: float = 0.0
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    function_calls: int = 0
    peak_memory: float = 0.0
    start_time: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'execution_time': self.execution_time,
            'memory_usage': self.memory_usage,
            'cpu_usage': self.cpu_usage,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'function_calls': self.function_calls,
            'peak_memory': self.peak_memory,
            'cache_hit_rate': self.cache_hits / max(1, self.cache_hits + self.cache_misses)
        }


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(PerformanceMetrics)
        self.active_contexts = {}
        self._lock = threading.Lock()
    
    @contextmanager
    def monitor(self, name: str):
        """性能监控上下文管理器"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        start_cpu = psutil.cpu_percent()
        
        try:
            yield
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            end_cpu = psutil.cpu_percent()
            
            with self._lock:
                metrics = self.metrics[name]
                metrics.execution_time += end_time - start_time
                metrics.memory_usage = end_memory
                metrics.cpu_usage = end_cpu
                metrics.peak_memory = max(metrics.peak_memory, end_memory)
                metrics.function_calls += 1
    
    def get_metrics(self, name: str) -> PerformanceMetrics:
        """获取指定名称的性能指标"""
        return self.metrics[name]
    
    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取所有性能指标"""
        return {name: metrics.to_dict() for name, metrics in self.metrics.items()}
    
    def reset_metrics(self, name: Optional[str] = None):
        """重置性能指标"""
        with self._lock:
            if name:
                self.metrics[name] = PerformanceMetrics()
            else:
                self.metrics.clear()
    
    def save_metrics(self, filepath: Union[str, Path]):
        """保存性能指标到文件"""
        filepath = Path(filepath)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.get_all_metrics(), f, indent=2, ensure_ascii=False)
    
    def load_metrics(self, filepath: Union[str, Path]):
        """从文件加载性能指标"""
        filepath = Path(filepath)
        if filepath.exists():
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                for name, metrics_dict in data.items():
                    metrics = PerformanceMetrics()
                    for key, value in metrics_dict.items():
                        if hasattr(metrics, key):
                            setattr(metrics, key, value)
                    self.metrics[name] = metrics


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, maxsize: int = 128):
        self.maxsize = maxsize
        self.cache = OrderedDict()
        self.hits = 0
        self.misses = 0
        self._lock = threading.RLock()
    
    def get(self, key: Any) -> Any:
        """获取缓存值"""
        with self._lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                self.hits += 1
                return value
            else:
                self.misses += 1
                return None
    
    def put(self, key: Any, value: Any):
        """设置缓存值"""
        with self._lock:
            if key in self.cache:
                # 更新现有值
                self.cache.pop(key)
            elif len(self.cache) >= self.maxsize:
                # 删除最久未使用的项
                self.cache.popitem(last=False)
            
            self.cache[key] = value
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.hits = 0
            self.misses = 0
    
    def info(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total = self.hits + self.misses
            return {
                'hits': self.hits,
                'misses': self.misses,
                'hit_rate': self.hits / max(1, total),
                'size': len(self.cache),
                'maxsize': self.maxsize
            }


def lru_cache(maxsize: int = 128):
    """LRU缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        cache = LRUCache(maxsize)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = (args, tuple(sorted(kwargs.items())))
            
            # 尝试从缓存获取
            result = cache.get(key)
            if result is not None:
                return result
            
            # 计算结果并缓存
            result = func(*args, **kwargs)
            cache.put(key, result)
            return result
        
        wrapper.cache_info = cache.info
        wrapper.cache_clear = cache.clear
        return wrapper
    
    return decorator


class MemoryPool:
    """内存池管理器"""
    
    def __init__(self, obj_type: type, initial_size: int = 10):
        self.obj_type = obj_type
        self.pool = []
        self.in_use = set()
        self._lock = threading.Lock()
        
        # 预分配对象
        for _ in range(initial_size):
            self.pool.append(self._create_object())
    
    def _create_object(self):
        """创建新对象"""
        return self.obj_type()
    
    def acquire(self):
        """获取对象"""
        with self._lock:
            if self.pool:
                obj = self.pool.pop()
            else:
                obj = self._create_object()
            
            self.in_use.add(id(obj))
            return obj
    
    def release(self, obj):
        """释放对象"""
        with self._lock:
            obj_id = id(obj)
            if obj_id in self.in_use:
                self.in_use.remove(obj_id)
                # 重置对象状态
                if hasattr(obj, 'reset'):
                    obj.reset()
                self.pool.append(obj)
    
    def size(self) -> Tuple[int, int]:
        """返回(可用对象数, 使用中对象数)"""
        with self._lock:
            return len(self.pool), len(self.in_use)


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.pools = {}
        self.weak_refs = weakref.WeakSet()
    
    def create_pool(self, name: str, obj_type: type, initial_size: int = 10) -> MemoryPool:
        """创建内存池"""
        pool = MemoryPool(obj_type, initial_size)
        self.pools[name] = pool
        return pool
    
    def get_pool(self, name: str) -> Optional[MemoryPool]:
        """获取内存池"""
        return self.pools.get(name)
    
    def force_gc(self):
        """强制垃圾回收"""
        collected = gc.collect()
        logger.info(f"垃圾回收完成，回收了 {collected} 个对象")
        return collected
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
            'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
            'percent': process.memory_percent(),       # 内存使用百分比
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }


# 全局实例
performance_monitor = PerformanceMonitor()
memory_optimizer = MemoryOptimizer()


def profile(name: Optional[str] = None):
    """性能分析装饰器"""
    def decorator(func: Callable) -> Callable:
        profile_name = name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with performance_monitor.monitor(profile_name):
                return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


@contextmanager
def memory_limit(limit_mb: float):
    """内存限制上下文管理器"""
    start_memory = psutil.Process().memory_info().rss / 1024 / 1024
    
    try:
        yield
    finally:
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        if current_memory - start_memory > limit_mb:
            logger.warning(f"内存使用超出限制: {current_memory - start_memory:.2f}MB > {limit_mb}MB")
            memory_optimizer.force_gc()


def optimize_dataclass(cls):
    """数据类优化装饰器，添加__slots__以减少内存使用"""
    if hasattr(cls, '__dataclass_fields__'):
        cls.__slots__ = tuple(cls.__dataclass_fields__.keys())
    return cls
