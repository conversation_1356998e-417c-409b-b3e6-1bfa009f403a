# Core dependencies
numpy>=1.20.0
pandas>=1.3.0
scipy>=1.7.0
matplotlib>=3.4.0
seaborn>=0.11.0

# Data handling and processing
h5py>=3.6.0
openpyxl>=3.0.9
xlrd>=2.0.1
pyyaml>=6.0

# Statistics and modeling
statsmodels>=0.13.0
scikit-learn>=1.0.0
pyDOE3>=1.2.0

# Progress tracking and CLI
tqdm>=4.62.0
click>=8.0.0
colorama>=0.4.4

# Visualization
plotly>=5.3.0
kaleido>=0.2.1  # For static plotly exports

# Parallel processing
joblib>=1.1.0
dask>=2022.1.0
distributed>=2022.1.0

# Machine learning (for auto-calibration)
torch>=1.10.0
tensorboard>=2.8.0

# Development and testing
pytest>=6.2.5
pytest-cov>=2.12.1
flake8>=4.0.1
black>=22.1.0
mypy>=0.931

# Documentation
sphinx>=4.4.0
sphinx-rtd-theme>=1.0.0
nbsphinx>=0.8.8
ipython>=8.0.0

# Cluster computing
paramiko>=2.10.1  # For SSH connections to clusters