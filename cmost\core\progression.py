"""
Disease progression model for colorectal cancer simulation.
"""
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
import numpy as np
import logging
from enum import Enum


class PolypStage(Enum):
    """Enumeration of polyp stages."""
    DIMINUTIVE = 1      # <5mm
    SMALL = 2           # 5-9mm
    MEDIUM = 3          # 10-19mm
    LARGE = 4           # ≥20mm
    ADVANCED = 5        # Advanced adenoma with villous features
    ADVANCED_HGD = 6    # Advanced adenoma with high-grade dysplasia


class CancerStage(Enum):
    """Enumeration of cancer stages."""
    STAGE_I = 7         # T1-T2, N0, M0
    STAGE_II = 8        # T3-T4, N0, M0
    STAGE_III = 9       # Any T, N1-N2, M0
    STAGE_IV = 10       # Any T, Any N, M1


class ProgressionModel:
    """Model for disease progression from polyps to cancer."""
    
    def __init__(self, 
                 dwell_speed: str = 'medium',
                 location_factors: Optional[Dict[int, float]] = None,
                 gender_factors: Optional[Dict[str, float]] = None):
        """Initialize progression model with parameters.
        
        Args:
            dwell_speed: Speed of progression ('slow', 'medium', 'fast')
            location_factors: Progression factors by anatomical location
            gender_factors: Progression factors by gender
        """
        self.logger = logging.getLogger("CMOST_Progression")
        self.dwell_speed = dwell_speed
        
        # Set up default location factors if not provided
        self.location_factors = location_factors or {
            1: 1.2,  # Rectum
            2: 1.1,  # Sigmoid colon
            3: 1.0,  # Descending colon
            4: 0.9,  # Transverse colon
            5: 0.8,  # Ascending colon
            6: 0.8   # Cecum
        }
        
        # Set up default gender factors if not provided
        self.gender_factors = gender_factors or {
            'M': 1.0,  # Male (reference)
            'F': 0.8   # Female
        }
        
        # Initialize progression rates based on dwell speed
        self._initialize_progression_rates()
        
        # Initialize direct-to-cancer probabilities
        self._initialize_direct_cancer_rates()
        
        # Initialize cancer progression rates
        self._initialize_cancer_progression_rates()
        
        # Initialize regression (healing) rates
        self._initialize_regression_rates()
    
    def _initialize_progression_rates(self) -> None:
        """Initialize annual progression rates for each polyp stage."""
        # Base annual progression rates by stage
        # These are the probabilities of progressing from one stage to the next in a year
        if self.dwell_speed == 'slow':
            self.base_progression_rates = {
                PolypStage.DIMINUTIVE: 0.02,    # 2% annual progression from diminutive to small
                PolypStage.SMALL: 0.015,        # 1.5% annual progression from small to medium
                PolypStage.MEDIUM: 0.03,        # 3% annual progression from medium to large
                PolypStage.LARGE: 0.05,         # 5% annual progression from large to advanced
                PolypStage.ADVANCED: 0.08,      # 8% annual progression from advanced to HGD
                PolypStage.ADVANCED_HGD: 0.12   # 12% annual progression from HGD to cancer
            }
        elif self.dwell_speed == 'fast':
            self.base_progression_rates = {
                PolypStage.DIMINUTIVE: 0.05,    # 5% annual progression
                PolypStage.SMALL: 0.04,         # 4% annual progression
                PolypStage.MEDIUM: 0.08,        # 8% annual progression
                PolypStage.LARGE: 0.12,         # 12% annual progression
                PolypStage.ADVANCED: 0.18,      # 18% annual progression
                PolypStage.ADVANCED_HGD: 0.25   # 25% annual progression
            }
        else:  # medium (default)
            self.base_progression_rates = {
                PolypStage.DIMINUTIVE: 0.03,    # 3% annual progression
                PolypStage.SMALL: 0.025,        # 2.5% annual progression
                PolypStage.MEDIUM: 0.05,        # 5% annual progression
                PolypStage.LARGE: 0.08,         # 8% annual progression
                PolypStage.ADVANCED: 0.12,      # 12% annual progression
                PolypStage.ADVANCED_HGD: 0.18   # 18% annual progression
            }
    
    def _initialize_direct_cancer_rates(self) -> None:
        """Initialize probabilities of direct progression to cancer."""
        # Probability of direct progression to cancer (skipping stages)
        # These represent the "fast track" to cancer without going through all stages
        if self.dwell_speed == 'slow':
            self.direct_cancer_rates = {
                PolypStage.DIMINUTIVE: 0.0005,  # 0.05% chance of direct progression to cancer
                PolypStage.SMALL: 0.001,        # 0.1% chance
                PolypStage.MEDIUM: 0.002,       # 0.2% chance
                PolypStage.LARGE: 0.005,        # 0.5% chance
                PolypStage.ADVANCED: 0.01,      # 1% chance
                PolypStage.ADVANCED_HGD: 0.0    # Already progressing to cancer normally
            }
        elif self.dwell_speed == 'fast':
            self.direct_cancer_rates = {
                PolypStage.DIMINUTIVE: 0.002,   # 0.2% chance
                PolypStage.SMALL: 0.004,        # 0.4% chance
                PolypStage.MEDIUM: 0.008,       # 0.8% chance
                PolypStage.LARGE: 0.015,        # 1.5% chance
                PolypStage.ADVANCED: 0.03,      # 3% chance
                PolypStage.ADVANCED_HGD: 0.0    # Already progressing to cancer normally
            }
        else:  # medium (default)
            self.direct_cancer_rates = {
                PolypStage.DIMINUTIVE: 0.001,   # 0.1% chance
                PolypStage.SMALL: 0.002,        # 0.2% chance
                PolypStage.MEDIUM: 0.004,       # 0.4% chance
                PolypStage.LARGE: 0.01,         # 1% chance
                PolypStage.ADVANCED: 0.02,      # 2% chance
                PolypStage.ADVANCED_HGD: 0.0    # Already progressing to cancer normally
            }
    
    def _initialize_cancer_progression_rates(self) -> None:
        """Initialize cancer progression rates between stages."""
        # Mean dwell times in each cancer stage (in years)
        if self.dwell_speed == 'slow':
            self.cancer_dwell_times = {
                CancerStage.STAGE_I: 3.0,      # 3 years in Stage I
                CancerStage.STAGE_II: 2.0,     # 2 years in Stage II
                CancerStage.STAGE_III: 1.5     # 1.5 years in Stage III before Stage IV
            }
        elif self.dwell_speed == 'fast':
            self.cancer_dwell_times = {
                CancerStage.STAGE_I: 1.5,      # 1.5 years in Stage I
                CancerStage.STAGE_II: 1.0,     # 1 year in Stage II
                CancerStage.STAGE_III: 0.8     # 0.8 years in Stage III before Stage IV
            }
        else:  # medium (default)
            self.cancer_dwell_times = {
                CancerStage.STAGE_I: 2.0,      # 2 years in Stage I
                CancerStage.STAGE_II: 1.5,     # 1.5 years in Stage II
                CancerStage.STAGE_III: 1.0     # 1 year in Stage III before Stage IV
            }
        
        # Convert dwell times to annual progression probabilities
        # Using exponential distribution: P(progress in 1 year) = 1 - exp(-1/mean_dwell_time)
        self.cancer_progression_rates = {
            stage: 1 - np.exp(-1/dwell_time) 
            for stage, dwell_time in self.cancer_dwell_times.items()
        }
    
    def _initialize_regression_rates(self) -> None:
        """Initialize polyp regression (healing) rates."""
        # Annual probability of regression (moving back one stage or disappearing)
        self.regression_rates = {
            PolypStage.DIMINUTIVE: 0.10,    # 10% chance of disappearing
            PolypStage.SMALL: 0.05,         # 5% chance of regression
            PolypStage.MEDIUM: 0.02,        # 2% chance of regression
            PolypStage.LARGE: 0.01,         # 1% chance of regression
            PolypStage.ADVANCED: 0.005,     # 0.5% chance of regression
            PolypStage.ADVANCED_HGD: 0.001  # 0.1% chance of regression
        }
        
        # Cancer doesn't regress without treatment
        for stage in CancerStage:
            self.regression_rates[stage] = 0.0
    
    def get_progression_probability(self, 
                                   stage: Union[int, PolypStage, CancerStage],
                                   location: int,
                                   gender: str,
                                   individual_risk: float,
                                   age: Optional[int] = None) -> float:
        """Calculate progression probability for a specific lesion.
        
        Args:
            stage: Current stage of the polyp or cancer
            location: Anatomical location (1-6)
            gender: Patient gender ('M' or 'F')
            individual_risk: Individual risk multiplier
            age: Patient age (optional)
            
        Returns:
            Annual probability of progression to the next stage
        """
        # Convert int to enum if needed
        if isinstance(stage, int):
            if 1 <= stage <= 6:
                stage = PolypStage(stage)
            elif 7 <= stage <= 10:
                stage = CancerStage(stage)
        
        # Get base progression rate for this stage
        if stage in self.base_progression_rates:
            base_rate = self.base_progression_rates[stage]
        elif stage in self.cancer_progression_rates:
            base_rate = self.cancer_progression_rates[stage]
        else:
            self.logger.warning(f"Unknown stage: {stage}, using default progression rate of 0")
            return 0.0
        
        # Apply location factor
        location_factor = self.location_factors.get(location, 1.0)
        
        # Apply gender factor
        gender_factor = self.gender_factors.get(gender, 1.0)
        
        # Apply individual risk factor
        risk_factor = individual_risk
        
        # Apply age factor if provided
        age_factor = 1.0
        if age is not None:
            if age < 40:
                age_factor = 0.8
            elif 40 <= age < 50:
                age_factor = 0.9
            elif 50 <= age < 60:
                age_factor = 1.0
            elif 60 <= age < 70:
                age_factor = 1.1
            elif 70 <= age < 80:
                age_factor = 1.2
            else:
                age_factor = 1.3
        
        # Calculate final probability
        probability = base_rate * location_factor * gender_factor * risk_factor * age_factor
        
        # Cap probability at 1.0
        return min(probability, 1.0)
    
    def get_direct_cancer_probability(self,
                                     stage: Union[int, PolypStage],
                                     location: int,
                                     gender: str,
                                     individual_risk: float,
                                     age: Optional[int] = None) -> float:
        """Calculate probability of direct progression to cancer.
        
        Args:
            stage: Current stage of the polyp
            location: Anatomical location (1-6)
            gender: Patient gender ('M' or 'F')
            individual_risk: Individual risk multiplier
            age: Patient age (optional)
            
        Returns:
            Annual probability of direct progression to cancer
        """
        # Convert int to enum if needed
        if isinstance(stage, int):
            if 1 <= stage <= 6:
                stage = PolypStage(stage)
            else:
                return 0.0  # Already cancer
        
        # Get base direct cancer rate for this stage
        if stage in self.direct_cancer_rates:
            base_rate = self.direct_cancer_rates[stage]
        else:
            self.logger.warning(f"Unknown stage: {stage}, using default direct cancer rate of 0")
            return 0.0
        
        # Apply location factor
        location_factor = self.location_factors.get(location, 1.0)
        
        # Apply gender factor
        gender_factor = self.gender_factors.get(gender, 1.0)
        
        # Apply individual risk factor
        risk_factor = individual_risk
        
        # Apply age factor if provided
        age_factor = 1.0
        if age is not None:
            if age < 40:
                age_factor = 0.7
            elif 40 <= age < 50:
                age_factor = 0.8
            elif 50 <= age < 60:
                age_factor = 1.0
            elif 60 <= age < 70:
                age_factor = 1.2
            elif 70 <= age < 80:
                age_factor = 1.4
            else:
                age_factor = 1.6
        
        # Calculate final probability
        probability = base_rate * location_factor * gender_factor * risk_factor * age_factor
        
        # Cap probability at 1.0
        return min(probability, 1.0)
    
    def get_regression_probability(self,
                                  stage: Union[int, PolypStage, CancerStage],
                                  individual_risk: float) -> float:
        """Calculate probability of regression (healing or shrinking).
        
        Args:
            stage: Current stage of the polyp or cancer
            individual_risk: Individual risk multiplier (inverse relationship)
            
        Returns:
            Annual probability of regression
        """
        # Convert int to enum if needed
        if isinstance(stage, int):
            if 1 <= stage <= 6:
                stage = PolypStage(stage)
            elif 7 <= stage <= 10:
                stage = CancerStage(stage)
        
        # Get base regression rate for this stage
        if stage in self.regression_rates:
            base_rate = self.regression_rates[stage]
        else:
            self.logger.warning(f"Unknown stage: {stage}, using default regression rate of 0")
            return 0.0
        
        # Apply inverse of individual risk factor
        # Higher risk individuals have lower regression rates
        risk_factor = 1.0 / individual_risk if individual_risk > 0 else 1.0
        
        # Calculate final probability
        probability = base_rate * risk_factor
        
        # Cap probability at 1.0
        return min(probability, 1.0)
    
    def progress_polyp(self, 
                      current_stage: int, 
                      location: int,
                      gender: str,
                      individual_risk: float,
                      age: Optional[int] = None) -> Tuple[int, bool]:
        """Progress a polyp based on progression model.
        
        Args:
            current_stage: Current stage of the polyp (1-6)
            location: Anatomical location (1-6)
            gender: Patient gender ('M' or 'F')
            individual_risk: Individual risk multiplier
            age: Patient age (optional)
            
        Returns:
            Tuple of (new_stage, progressed_to_cancer)
        """
        if current_stage < 1 or current_stage > 6:
            self.logger.warning(f"Invalid polyp stage: {current_stage}")
            return current_stage, False
        
        # Check for direct progression to cancer
        direct_cancer_prob = self.get_direct_cancer_probability(
            current_stage, location, gender, individual_risk, age
        )
        
        if np.random.random() < direct_cancer_prob:
            return 7, True  # Stage I cancer
        
        # Check for normal progression
        progression_prob = self.get_progression_probability(
            current_stage, location, gender, individual_risk, age
        )
        
        if np.random.random() < progression_prob:
            new_stage = current_stage + 1
            progressed_to_cancer = (new_stage == 7)  # Stage 7 is Stage I cancer
            return new_stage, progressed_to_cancer
        
        # Check for regression
        regression_prob = self.get_regression_probability(current_stage, individual_risk)
        
        if np.random.random() < regression_prob:
            new_stage = current_stage - 1
            return max(0, new_stage), False  # Stage 0 means polyp disappears
        
        # No change
        return current_stage, False
    
    def progress_cancer(self,
                       current_stage: int,
                       treatment_applied: bool = False) -> int:
        """Progress cancer based on progression model.
        
        Args:
            current_stage: Current stage of cancer (7-10)
            treatment_applied: Whether treatment has been applied
            
        Returns:
            New cancer stage
        """
        if current_stage < 7 or current_stage > 10:
            self.logger.warning(f"Invalid cancer stage: {current_stage}")
            return current_stage
        
        # Stage IV is terminal, no further progression
        if current_stage == 10:
            return current_stage
        
        # If treatment has been applied, reduce progression probability
        if treatment_applied:
            # Treatment effectiveness decreases with higher stages
            if current_stage == 7:  # Stage I
                return current_stage  # No progression after successful treatment
            elif current_stage == 8:  # Stage II
                progression_prob = self.cancer_progression_rates[CancerStage.STAGE_II] * 0.3
            elif current_stage == 9:  # Stage III
                progression_prob = self.cancer_progression_rates[CancerStage.STAGE_III] * 0.5
        else:
            # Get progression probability for current stage
            if current_stage == 7:
                progression_prob = self.cancer_progression_rates[CancerStage.STAGE_I]
            elif current_stage == 8:
                progression_prob = self.cancer_progression_rates[CancerStage.STAGE_II]
            elif current_stage == 9:
                progression_prob = self.cancer_progression_rates[CancerStage.STAGE_III]
        
        # Check for progression
        if np.random.random() < progression_prob:
            return current_stage + 1
        
        return current_stage
    
    def generate_sojourn_times(self, num_patients: int = 1) -> Dict[str, np.ndarray]:
        """Generate sojourn times for cancer progression through stages.
        
        Args:
            num_patients: Number of patients to generate times for
            
        Returns:
            Dictionary of arrays with sojourn times for each stage
        """
        # Generate sojourn times using exponential distribution
        sojourn_times = {}
        
        for stage, mean_time in self.cancer_dwell_times.items():
            # Generate random times from exponential distribution
            times = np.random.exponential(mean_time, num_patients)
            
            # Convert to years and ensure minimum time
            times = np.maximum(times, 0.1)  # Minimum 0.1 years (about 1 month)
            
            sojourn_times[stage.name] = times
        
        return sojourn_times
    
    def calculate_cumulative_risk(self, 
                                 age: int, 
                                 gender: str, 
                                 risk_factors: Dict[str, float],
                                 years: int = 10) -> Dict[str, float]:
        """Calculate cumulative risk of developing cancer over time.
        
        Args:
            age: Starting age
            gender: Patient gender ('M' or 'F')
            risk_factors: Dictionary of risk factors and their values
            years: Number of years to project
            
        Returns:
            Dictionary with cumulative risks at different time points
        """
        # Calculate individual risk multiplier
        individual_risk = 1.0
        for factor, value in risk_factors.items():
            individual_risk *= value
        
        # Base annual risk of developing cancer by age
        base_risks = {
            30: 0.0001,  # 0.01% annual risk at age 30
            40: 0.0003,  # 0.03% annual risk at age 40
            50: 0.0007,  # 0.07% annual risk at age 50
            60: 0.0015,  # 0.15% annual risk at age 60
            70: 0.0030,  # 0.30% annual risk at age 70
            80: 0.0045,  # 0.45% annual risk at age 80
            90: 0.0060   # 0.60% annual risk at age 90
        }
        
        # Find closest age bracket
        age_brackets = sorted(base_risks.keys())
        closest_age = min(age_brackets, key=lambda x: abs(x - age))
        
        # Get base risk
        base_risk = base_risks[closest_age]
        
        # Apply gender factor
        gender_factor = self.gender_factors.get(gender, 1.0)
        
        # Calculate annual risk
        annual_risk = base_risk * gender_factor * individual_risk
        
        # Calculate cumulative risk at different time points
        # Using formula: 1 - (1 - annual_risk)^years
        cumulative_risks = {}
        for year in range(1, years + 1):
            current_age = age + year - 1
            
            # Update annual risk if crossing age bracket
            if year > 1:
                for bracket in age_brackets:
                    if current_age == bracket:
                        base_risk = base_risks[bracket]
                        annual_risk = base_risk * gender_factor * individual_risk
                        break
            
            cumulative_risk = 1 - (1 - annual_risk) ** year
            cumulative_risks[f"year_{year}"] = cumulative_risk
        
        return cumulative_risks
    
    def estimate_dwell_time(self, 
                           polyp_stage: int, 
                           location: int,
                           gender: str,
                           individual_risk: float) -> float:
        """Estimate expected dwell time from current polyp stage to cancer.
        
        Args:
            polyp_stage: Current stage of the polyp (1-6)
            location: Anatomical location (1-6)
            gender: Patient gender ('M' or 'F')
            individual_risk: Individual risk multiplier
            
        Returns:
            Expected dwell time in years
        """
        if polyp_stage < 1 or polyp_stage > 6:
            self.logger.warning(f"Invalid polyp stage: {polyp_stage}")
            return 0.0
        
        # For each stage, calculate expected time to progress
        # Time = 1 / progression_probability
        total_time = 0.0
        current = polyp_stage
        
        while current < 7:  # Until cancer (stage 7)
            # Get progression probability
            prob = self.get_progression_probability(current, location, gender, individual_risk)
            
            # Add direct-to-cancer probability
            direct_prob = self.get_direct_cancer_probability(current, location, gender, individual_risk)
            
            # Total probability of advancing
            total_prob = prob + direct_prob
            
            # Expected time in this stage
            if total_prob > 0:
                time_in_stage = 1.0 / total_prob
            else:
                time_in_stage = float('inf')  # Will never progress
            
            total_time += time_in_stage
            
            # If direct progression to cancer is more likely than normal progression
            if direct_prob > prob:
                break  # We've reached cancer
            
            current += 1
        
        return total_time

    def progress_serrated_lesion(self,
                                current_stage: int,
                                location: int,
                                gender: str,
                                individual_risk: float,
                                age: Optional[int] = None,
                                has_dysplasia: bool = False) -> Tuple[int, bool]:
        """Progress a serrated lesion based on progression model.

        Args:
            current_stage: Current stage of the serrated lesion (1-4)
            location: Anatomical location (1-6)
            gender: Patient gender ('M' or 'F')
            individual_risk: Individual risk multiplier
            age: Patient age (optional)
            has_dysplasia: Whether lesion has dysplasia

        Returns:
            Tuple of (new_stage, progressed_to_cancer)
        """
        if current_stage < 1 or current_stage > 4:
            self.logger.warning(f"Invalid serrated lesion stage: {current_stage}")
            return current_stage, False

        # Serrated pathway progression rates (generally slower than adenoma pathway)
        base_progression_rates = {
            1: 0.05,  # Small SSL to Large SSL
            2: 0.08,  # Large SSL to SSL with dysplasia
            3: 0.15,  # SSL with dysplasia to preclinical cancer
            4: 0.25   # Preclinical to clinical cancer
        }

        # Location factors for serrated lesions (more common in proximal colon)
        serrated_location_factors = {
            1: 0.8,   # Rectum (less common)
            2: 0.9,   # Sigmoid colon
            3: 1.0,   # Descending colon
            4: 1.3,   # Transverse colon (more common)
            5: 1.5,   # Ascending colon (most common)
            6: 1.4    # Cecum
        }

        # Gender factors (slightly higher in females for serrated pathway)
        serrated_gender_factors = {
            'M': 0.9,
            'F': 1.1
        }

        # Age factors (serrated lesions more common in older patients)
        age_factor = 1.0
        if age is not None:
            if age < 50:
                age_factor = 0.7
            elif age < 60:
                age_factor = 1.0
            elif age < 70:
                age_factor = 1.2
            else:
                age_factor = 1.4

        # Dysplasia accelerates progression
        dysplasia_factor = 1.5 if has_dysplasia else 1.0

        # Calculate progression probability
        base_rate = base_progression_rates.get(current_stage, 0)
        location_factor = serrated_location_factors.get(location, 1.0)
        gender_factor = serrated_gender_factors.get(gender, 1.0)

        progression_prob = (base_rate * location_factor * gender_factor *
                          age_factor * dysplasia_factor * individual_risk)

        # Check for progression
        if np.random.random() < progression_prob:
            new_stage = current_stage + 1

            # Stage 4 progression leads to cancer
            if current_stage == 4:
                return 7, True  # Stage I cancer
            else:
                return new_stage, False

        # Check for regression (rare in serrated pathway)
        regression_rates = {
            1: 0.02,  # Small SSL can regress
            2: 0.01,  # Large SSL rarely regresses
            3: 0.005, # SSL with dysplasia very rarely regresses
            4: 0.0    # Preclinical cancer doesn't regress
        }

        regression_prob = regression_rates.get(current_stage, 0) / individual_risk

        if np.random.random() < regression_prob:
            new_stage = current_stage - 1
            return max(0, new_stage), False  # Stage 0 means lesion disappears

        # No change
        return current_stage, False