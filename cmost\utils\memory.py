"""
内存管理优化模块

提供内存池、对象复用、垃圾回收优化等功能，用于减少CMOST仿真的内存占用。
"""

import gc
import sys
import weakref
import threading
import psutil
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic, Set
from collections import defaultdict, deque
from dataclasses import dataclass
import logging
from contextlib import contextmanager
import numpy as np


logger = logging.getLogger(__name__)
T = TypeVar('T')


@dataclass
class MemoryStats:
    """内存统计信息"""
    total_allocated: int = 0
    total_freed: int = 0
    current_usage: int = 0
    peak_usage: int = 0
    pool_hits: int = 0
    pool_misses: int = 0
    gc_collections: int = 0
    
    def hit_rate(self) -> float:
        """缓存命中率"""
        total = self.pool_hits + self.pool_misses
        return self.pool_hits / max(1, total)


class ObjectPool(Generic[T]):
    """通用对象池"""
    
    def __init__(self, 
                 factory: callable,
                 reset_func: Optional[callable] = None,
                 max_size: int = 100,
                 initial_size: int = 10):
        self.factory = factory
        self.reset_func = reset_func
        self.max_size = max_size
        self.pool = deque()
        self.in_use: Set[int] = set()
        self.stats = MemoryStats()
        self._lock = threading.RLock()
        
        # 预分配对象
        for _ in range(initial_size):
            obj = self.factory()
            self.pool.append(obj)
            self.stats.total_allocated += 1
    
    def acquire(self) -> T:
        """获取对象"""
        with self._lock:
            if self.pool:
                obj = self.pool.popleft()
                self.stats.pool_hits += 1
            else:
                obj = self.factory()
                self.stats.pool_misses += 1
                self.stats.total_allocated += 1
            
            self.in_use.add(id(obj))
            self.stats.current_usage += 1
            self.stats.peak_usage = max(self.stats.peak_usage, self.stats.current_usage)
            
            return obj
    
    def release(self, obj: T):
        """释放对象"""
        with self._lock:
            obj_id = id(obj)
            if obj_id not in self.in_use:
                logger.warning("尝试释放未被获取的对象")
                return
            
            self.in_use.remove(obj_id)
            self.stats.current_usage -= 1
            
            # 重置对象状态
            if self.reset_func:
                try:
                    self.reset_func(obj)
                except Exception as e:
                    logger.error(f"对象重置失败: {e}")
                    self.stats.total_freed += 1
                    return
            
            # 如果池未满，则放回池中
            if len(self.pool) < self.max_size:
                self.pool.append(obj)
            else:
                self.stats.total_freed += 1
    
    def clear(self):
        """清空对象池"""
        with self._lock:
            self.stats.total_freed += len(self.pool)
            self.pool.clear()
            self.in_use.clear()
            self.stats.current_usage = 0
    
    def size(self) -> tuple:
        """返回(可用对象数, 使用中对象数)"""
        with self._lock:
            return len(self.pool), len(self.in_use)
    
    def get_stats(self) -> MemoryStats:
        """获取统计信息"""
        return self.stats


class MemoryManager:
    """内存管理器"""
    
    def __init__(self):
        self.pools: Dict[str, ObjectPool] = {}
        self.weak_refs: weakref.WeakSet = weakref.WeakSet()
        self.memory_threshold_mb = 1000  # 内存阈值
        self.gc_threshold = 1000  # GC阈值
        self.allocation_count = 0
        self._lock = threading.RLock()
    
    def create_pool(self, 
                   name: str,
                   factory: callable,
                   reset_func: Optional[callable] = None,
                   max_size: int = 100,
                   initial_size: int = 10) -> ObjectPool:
        """创建对象池"""
        with self._lock:
            if name in self.pools:
                logger.warning(f"对象池 '{name}' 已存在，将被替换")
            
            pool = ObjectPool(factory, reset_func, max_size, initial_size)
            self.pools[name] = pool
            logger.info(f"创建对象池 '{name}': max_size={max_size}, initial_size={initial_size}")
            
            return pool
    
    def get_pool(self, name: str) -> Optional[ObjectPool]:
        """获取对象池"""
        return self.pools.get(name)
    
    def acquire_from_pool(self, pool_name: str):
        """从指定池获取对象"""
        pool = self.get_pool(pool_name)
        if pool:
            return pool.acquire()
        else:
            raise ValueError(f"对象池 '{pool_name}' 不存在")
    
    def release_to_pool(self, pool_name: str, obj):
        """释放对象到指定池"""
        pool = self.get_pool(pool_name)
        if pool:
            pool.release(obj)
        else:
            logger.warning(f"对象池 '{pool_name}' 不存在，无法释放对象")
    
    def track_object(self, obj):
        """跟踪对象"""
        self.weak_refs.add(obj)
        self.allocation_count += 1
        
        # 定期触发垃圾回收
        if self.allocation_count % self.gc_threshold == 0:
            self.force_gc()
    
    def force_gc(self) -> int:
        """强制垃圾回收"""
        collected = gc.collect()
        logger.debug(f"垃圾回收完成，回收了 {collected} 个对象")
        
        # 更新统计信息
        for pool in self.pools.values():
            pool.stats.gc_collections += 1
        
        return collected
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024,
            'tracked_objects': len(self.weak_refs),
            'allocation_count': self.allocation_count
        }
    
    def check_memory_pressure(self) -> bool:
        """检查内存压力"""
        memory_usage = self.get_memory_usage()
        return memory_usage['rss_mb'] > self.memory_threshold_mb
    
    def cleanup_pools(self):
        """清理所有对象池"""
        with self._lock:
            for name, pool in self.pools.items():
                pool.clear()
                logger.info(f"清理对象池 '{name}'")
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有对象池统计信息"""
        stats = {}
        for name, pool in self.pools.items():
            pool_stats = pool.get_stats()
            available, in_use = pool.size()
            
            stats[name] = {
                'available': available,
                'in_use': in_use,
                'total_allocated': pool_stats.total_allocated,
                'total_freed': pool_stats.total_freed,
                'current_usage': pool_stats.current_usage,
                'peak_usage': pool_stats.peak_usage,
                'hit_rate': pool_stats.hit_rate(),
                'gc_collections': pool_stats.gc_collections
            }
        
        return stats


class ArrayPool:
    """NumPy数组池"""
    
    def __init__(self, max_arrays: int = 50):
        self.max_arrays = max_arrays
        self.arrays_by_shape: Dict[tuple, deque] = defaultdict(deque)
        self.total_arrays = 0
        self._lock = threading.RLock()
    
    def acquire(self, shape: tuple, dtype=np.float64) -> np.ndarray:
        """获取数组"""
        with self._lock:
            key = (shape, dtype)
            if key in self.arrays_by_shape and self.arrays_by_shape[key]:
                arr = self.arrays_by_shape[key].popleft()
                arr.fill(0)  # 清零
                return arr
            else:
                return np.zeros(shape, dtype=dtype)
    
    def release(self, arr: np.ndarray):
        """释放数组"""
        with self._lock:
            if self.total_arrays >= self.max_arrays:
                return  # 池已满，直接丢弃
            
            key = (arr.shape, arr.dtype)
            self.arrays_by_shape[key].append(arr)
            self.total_arrays += 1
    
    def clear(self):
        """清空数组池"""
        with self._lock:
            self.arrays_by_shape.clear()
            self.total_arrays = 0


# 全局内存管理器实例
memory_manager = MemoryManager()
array_pool = ArrayPool()


@contextmanager
def managed_object(pool_name: str):
    """托管对象上下文管理器"""
    obj = memory_manager.acquire_from_pool(pool_name)
    try:
        yield obj
    finally:
        memory_manager.release_to_pool(pool_name, obj)


@contextmanager
def managed_array(shape: tuple, dtype=np.float64):
    """托管数组上下文管理器"""
    arr = array_pool.acquire(shape, dtype)
    try:
        yield arr
    finally:
        array_pool.release(arr)


@contextmanager
def memory_monitor(threshold_mb: float = 1000):
    """内存监控上下文管理器"""
    start_memory = psutil.Process().memory_info().rss / 1024 / 1024
    
    try:
        yield
    finally:
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_increase = end_memory - start_memory
        
        if memory_increase > threshold_mb:
            logger.warning(f"内存使用增加 {memory_increase:.2f}MB，超过阈值 {threshold_mb}MB")
            memory_manager.force_gc()


def optimize_gc():
    """优化垃圾回收设置"""
    # 调整GC阈值
    gc.set_threshold(700, 10, 10)
    
    # 启用调试模式（开发时使用）
    if logger.isEnabledFor(logging.DEBUG):
        gc.set_debug(gc.DEBUG_STATS)


def memory_efficient_range(start: int, stop: int, step: int = 1, chunk_size: int = 1000):
    """内存高效的范围生成器"""
    current = start
    while current < stop:
        chunk_end = min(current + chunk_size * step, stop)
        yield range(current, chunk_end, step)
        current = chunk_end


class LazyList:
    """延迟加载列表"""
    
    def __init__(self, generator_func: callable, *args, **kwargs):
        self.generator_func = generator_func
        self.args = args
        self.kwargs = kwargs
        self._cache = {}
        self._length = None
    
    def __getitem__(self, index):
        if index not in self._cache:
            # 生成并缓存项目
            gen = self.generator_func(*self.args, **self.kwargs)
            for i, item in enumerate(gen):
                if i == index:
                    self._cache[index] = item
                    break
            else:
                raise IndexError("Index out of range")
        
        return self._cache[index]
    
    def __len__(self):
        if self._length is None:
            # 计算长度（可能需要遍历生成器）
            gen = self.generator_func(*self.args, **self.kwargs)
            self._length = sum(1 for _ in gen)
        
        return self._length
