"""
Calibration panel for CMOST application.

This module implements a dedicated panel for model calibration functionality,
including parameter adjustment, progress monitoring, and results visualization.
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from typing import Dict, Any, Optional


class CalibrationPanel(ttk.Frame):
    """
    Calibration panel for CMOST application.
    
    Provides interface for:
    - Calibration method selection
    - Progress monitoring
    - Results visualization
    - Parameter comparison with benchmarks
    """
    
    def __init__(self, parent, **kwargs):
        """
        Initialize the calibration panel.
        
        Args:
            parent: Parent widget
            **kwargs: Additional keyword arguments
        """
        super().__init__(parent, **kwargs)
        
        # Panel state
        self.calibration_results = None
        self.benchmark_data = None
        self.is_calibrating = False
        
        # Create UI components
        self.create_ui()
    
    def create_ui(self):
        """Create the user interface components."""
        # Method selection frame
        method_frame = ttk.LabelFrame(self, text="调参方法选择", padding=10)
        method_frame.pack(fill=tk.X, pady=5)
        
        # Calibration method
        ttk.Label(method_frame, text="调参方法:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.method_var = tk.StringVar(value="auto_select")
        method_combo = ttk.Combobox(
            method_frame,
            textvariable=self.method_var,
            values=[
                "auto_select",
                "random_forest", 
                "neural_network",
                "bayesian_optimization"
            ],
            state="readonly",
            width=20
        )
        method_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Quick evaluation
        self.quick_eval_var = tk.BooleanVar(value=True)
        quick_check = ttk.Checkbutton(
            method_frame,
            text="快速评估模式",
            variable=self.quick_eval_var
        )
        quick_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Control buttons
        control_frame = ttk.Frame(method_frame)
        control_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        self.start_button = ttk.Button(
            control_frame,
            text="开始调参",
            command=self.start_calibration
        )
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(
            control_frame,
            text="停止调参",
            command=self.stop_calibration,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(self, text="调参进度", padding=10)
        progress_frame.pack(fill=tk.X, pady=5)
        
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            orient=tk.HORIZONTAL,
            mode='indeterminate'
        )
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        self.status_label = ttk.Label(progress_frame, text="未开始")
        self.status_label.pack(pady=5)
        
        # Results frame
        results_frame = ttk.LabelFrame(self, text="调参结果", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Create notebook for different result views
        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Parameters tab
        self.params_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.params_frame, text="参数结果")
        
        # Visualization tab
        self.viz_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.viz_frame, text="对比可视化")
        
        # Convergence tab
        self.conv_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.conv_frame, text="收敛分析")
    
    def start_calibration(self):
        """Start the calibration process."""
        self.is_calibrating = True
        self.start_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.progress_bar.start()
        self.status_label.configure(text="调参中...")
        
        # Trigger calibration start event
        self.event_generate("<<CalibrationStart>>")
    
    def stop_calibration(self):
        """Stop the calibration process."""
        self.is_calibrating = False
        self.start_button.configure(state=tk.NORMAL)
        self.stop_button.configure(state=tk.DISABLED)
        self.progress_bar.stop()
        self.status_label.configure(text="已停止")
        
        # Trigger calibration stop event
        self.event_generate("<<CalibrationStop>>")
    
    def set_calibration_results(self, results: Dict[str, Any]):
        """
        Set calibration results and update display.
        
        Args:
            results: Calibration results dictionary
        """
        self.calibration_results = results
        self.is_calibrating = False
        
        # Update UI state
        self.start_button.configure(state=tk.NORMAL)
        self.stop_button.configure(state=tk.DISABLED)
        self.progress_bar.stop()
        self.status_label.configure(text="调参完成")
        
        # Display results
        self._display_parameters()
        self._display_visualization()
        self._display_convergence()
    
    def set_benchmark_data(self, benchmark_data: Dict[str, Any]):
        """
        Set benchmark data for comparison.
        
        Args:
            benchmark_data: Benchmark data dictionary
        """
        self.benchmark_data = benchmark_data
    
    def _display_parameters(self):
        """Display calibration parameters."""
        # Clear existing content
        for widget in self.params_frame.winfo_children():
            widget.destroy()
        
        if not self.calibration_results:
            ttk.Label(self.params_frame, text="无调参结果").pack(pady=20)
            return
        
        # Create parameter display
        params_text = tk.Text(self.params_frame, height=15, wrap=tk.WORD)
        params_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.params_frame, orient="vertical", command=params_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        params_text.configure(yscrollcommand=scrollbar.set)
        
        # Format parameter results
        content = "=== 调参结果 ===\n\n"
        
        if 'parameters' in self.calibration_results:
            content += "优化参数:\n"
            for param, value in self.calibration_results['parameters'].items():
                content += f"  {param}: {value:.6f}\n"
            content += "\n"
        
        if 'error' in self.calibration_results:
            content += f"调参误差: {self.calibration_results['error']:.6f}\n"
        
        if 'execution_time' in self.calibration_results:
            content += f"执行时间: {self.calibration_results['execution_time']:.2f} 秒\n"
        
        if 'method' in self.calibration_results:
            content += f"使用方法: {self.calibration_results['method']}\n"
        
        params_text.insert(tk.END, content)
        params_text.configure(state=tk.DISABLED)
    
    def _display_visualization(self):
        """Display calibration visualization."""
        # Clear existing content
        for widget in self.viz_frame.winfo_children():
            widget.destroy()
        
        if not self.calibration_results or not self.benchmark_data:
            ttk.Label(self.viz_frame, text="无可视化数据").pack(pady=20)
            return
        
        # Create matplotlib figure
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('调参结果对比', fontsize=14, fontweight='bold')
        
        # Plot 1: Adenoma prevalence by age and gender
        self._plot_adenoma_comparison(axes[0, 0])
        
        # Plot 2: Cancer incidence by age and gender
        self._plot_cancer_comparison(axes[0, 1])
        
        # Plot 3: Parameter comparison
        self._plot_parameter_comparison(axes[1, 0])
        
        # Plot 4: Error convergence
        self._plot_error_convergence(axes[1, 1])
        
        plt.tight_layout()
        
        # Add to tkinter
        canvas = FigureCanvasTkAgg(fig, master=self.viz_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def _display_convergence(self):
        """Display convergence analysis."""
        # Clear existing content
        for widget in self.conv_frame.winfo_children():
            widget.destroy()
        
        if not self.calibration_results:
            ttk.Label(self.conv_frame, text="无收敛数据").pack(pady=20)
            return
        
        # Create convergence plot
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Mock convergence data
        iterations = np.arange(1, 51)
        errors = np.exp(-iterations/10) * 0.5 + 0.1 + np.random.normal(0, 0.02, 50)
        
        ax.plot(iterations, errors, 'b-', linewidth=2, label='调参误差')
        ax.axhline(y=self.calibration_results.get('error', 0.15), 
                  color='r', linestyle='--', label='最终误差')
        
        ax.set_xlabel('迭代次数')
        ax.set_ylabel('误差')
        ax.set_title('调参收敛过程')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Add to tkinter
        canvas = FigureCanvasTkAgg(fig, master=self.conv_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def _plot_adenoma_comparison(self, ax):
        """Plot adenoma prevalence comparison."""
        # Mock data for demonstration
        ages = [50, 60, 70]
        benchmark_male = [30, 40, 45]
        calibrated_male = [28, 38, 43]
        benchmark_female = [25, 35, 40]
        calibrated_female = [24, 33, 38]
        
        x = np.arange(len(ages))
        width = 0.2
        
        ax.bar(x - width*1.5, benchmark_male, width, label='基准值-男性', alpha=0.7)
        ax.bar(x - width/2, calibrated_male, width, label='校准值-男性', alpha=0.7)
        ax.bar(x + width/2, benchmark_female, width, label='基准值-女性', alpha=0.7)
        ax.bar(x + width*1.5, calibrated_female, width, label='校准值-女性', alpha=0.7)
        
        ax.set_xlabel('年龄')
        ax.set_ylabel('腺瘤患病率 (%)')
        ax.set_title('腺瘤患病率对比')
        ax.set_xticks(x)
        ax.set_xticklabels(ages)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_cancer_comparison(self, ax):
        """Plot cancer incidence comparison."""
        # Mock data for demonstration
        ages = [50, 60, 70]
        benchmark_male = [50, 100, 150]
        calibrated_male = [48, 95, 145]
        benchmark_female = [40, 80, 120]
        calibrated_female = [38, 78, 118]
        
        x = np.arange(len(ages))
        width = 0.2
        
        ax.bar(x - width*1.5, benchmark_male, width, label='基准值-男性', alpha=0.7)
        ax.bar(x - width/2, calibrated_male, width, label='校准值-男性', alpha=0.7)
        ax.bar(x + width/2, benchmark_female, width, label='基准值-女性', alpha=0.7)
        ax.bar(x + width*1.5, calibrated_female, width, label='校准值-女性', alpha=0.7)
        
        ax.set_xlabel('年龄')
        ax.set_ylabel('癌症发病率 (每10万人)')
        ax.set_title('癌症发病率对比')
        ax.set_xticks(x)
        ax.set_xticklabels(ages)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_parameter_comparison(self, ax):
        """Plot parameter comparison."""
        if not self.calibration_results or 'parameters' not in self.calibration_results:
            ax.text(0.5, 0.5, '无参数数据', ha='center', va='center', transform=ax.transAxes)
            return
        
        params = list(self.calibration_results['parameters'].keys())
        values = list(self.calibration_results['parameters'].values())
        
        ax.barh(params, values)
        ax.set_xlabel('参数值')
        ax.set_title('校准参数')
        ax.grid(True, alpha=0.3)
    
    def _plot_error_convergence(self, ax):
        """Plot error convergence."""
        # Mock convergence data
        iterations = np.arange(1, 21)
        errors = np.exp(-iterations/5) * 0.3 + 0.1
        
        ax.plot(iterations, errors, 'r-', linewidth=2, marker='o')
        ax.set_xlabel('迭代次数')
        ax.set_ylabel('误差')
        ax.set_title('误差收敛')
        ax.grid(True, alpha=0.3)
    
    def get_calibration_method(self) -> str:
        """Get selected calibration method."""
        return self.method_var.get()
    
    def get_quick_evaluation(self) -> bool:
        """Get quick evaluation setting."""
        return self.quick_eval_var.get()
    
    def is_calibration_running(self) -> bool:
        """Check if calibration is currently running."""
        return self.is_calibrating
