"""
并行处理优化模块

提供多进程、多线程并行处理功能，用于加速CMOST仿真计算。
"""

import os
import time
import multiprocessing as mp
import threading
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from typing import Any, Callable, Dict, List, Optional, Tuple, Union, Iterator
from dataclasses import dataclass
import logging
import queue
import pickle
from functools import partial
import numpy as np


logger = logging.getLogger(__name__)


@dataclass
class TaskResult:
    """任务结果数据类"""
    task_id: int
    result: Any
    execution_time: float
    error: Optional[Exception] = None
    worker_id: Optional[int] = None


@dataclass
class ParallelConfig:
    """并行配置"""
    max_workers: Optional[int] = None
    chunk_size: int = 1
    use_processes: bool = True
    timeout: Optional[float] = None
    memory_limit_mb: Optional[float] = None
    
    def __post_init__(self):
        if self.max_workers is None:
            self.max_workers = min(32, (os.cpu_count() or 1) + 4)


class TaskDistributor:
    """任务分发器"""
    
    def __init__(self, config: ParallelConfig):
        self.config = config
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.workers = []
        self.is_running = False
    
    def add_task(self, task_id: int, func: Callable, args: Tuple = (), kwargs: Dict = None):
        """添加任务"""
        kwargs = kwargs or {}
        self.task_queue.put((task_id, func, args, kwargs))
    
    def start_workers(self):
        """启动工作进程/线程"""
        self.is_running = True
        
        if self.config.use_processes:
            # 使用多进程
            for i in range(self.config.max_workers):
                worker = mp.Process(target=self._process_worker, args=(i,))
                worker.start()
                self.workers.append(worker)
        else:
            # 使用多线程
            for i in range(self.config.max_workers):
                worker = threading.Thread(target=self._thread_worker, args=(i,))
                worker.start()
                self.workers.append(worker)
    
    def _process_worker(self, worker_id: int):
        """进程工作函数"""
        while self.is_running:
            try:
                task_id, func, args, kwargs = self.task_queue.get(timeout=1.0)
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    task_result = TaskResult(task_id, result, execution_time, worker_id=worker_id)
                except Exception as e:
                    execution_time = time.time() - start_time
                    task_result = TaskResult(task_id, None, execution_time, error=e, worker_id=worker_id)
                
                self.result_queue.put(task_result)
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {e}")
    
    def _thread_worker(self, worker_id: int):
        """线程工作函数"""
        self._process_worker(worker_id)  # 逻辑相同
    
    def get_results(self, timeout: Optional[float] = None) -> List[TaskResult]:
        """获取所有结果"""
        results = []
        start_time = time.time()
        
        while True:
            try:
                remaining_time = None
                if timeout:
                    elapsed = time.time() - start_time
                    remaining_time = max(0, timeout - elapsed)
                    if remaining_time <= 0:
                        break
                
                result = self.result_queue.get(timeout=remaining_time or 1.0)
                results.append(result)
                
            except queue.Empty:
                if self.task_queue.empty():
                    break
        
        return results
    
    def stop(self):
        """停止所有工作进程/线程"""
        self.is_running = False
        
        for worker in self.workers:
            if self.config.use_processes:
                worker.terminate()
                worker.join()
            else:
                worker.join(timeout=1.0)
        
        self.workers.clear()


class ParallelProcessor:
    """并行处理器"""
    
    def __init__(self, config: Optional[ParallelConfig] = None):
        self.config = config or ParallelConfig()
        self.executor = None
    
    def map(self, func: Callable, iterable: List[Any], **kwargs) -> List[Any]:
        """并行映射函数"""
        if self.config.use_processes:
            with ProcessPoolExecutor(max_workers=self.config.max_workers) as executor:
                futures = [executor.submit(func, item) for item in iterable]
                results = []
                
                for future in as_completed(futures, timeout=self.config.timeout):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Task failed: {e}")
                        results.append(None)
                
                return results
        else:
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                futures = [executor.submit(func, item) for item in iterable]
                results = []
                
                for future in as_completed(futures, timeout=self.config.timeout):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Task failed: {e}")
                        results.append(None)
                
                return results
    
    def map_chunked(self, func: Callable, iterable: List[Any], chunk_size: Optional[int] = None) -> List[Any]:
        """分块并行映射"""
        chunk_size = chunk_size or self.config.chunk_size
        chunks = [iterable[i:i + chunk_size] for i in range(0, len(iterable), chunk_size)]
        
        def process_chunk(chunk):
            return [func(item) for item in chunk]
        
        chunk_results = self.map(process_chunk, chunks)
        
        # 展平结果
        results = []
        for chunk_result in chunk_results:
            if chunk_result:
                results.extend(chunk_result)
        
        return results
    
    def reduce(self, func: Callable, iterable: List[Any], initializer: Any = None) -> Any:
        """并行归约"""
        if not iterable:
            return initializer
        
        # 分块处理
        chunk_size = max(1, len(iterable) // self.config.max_workers)
        chunks = [iterable[i:i + chunk_size] for i in range(0, len(iterable), chunk_size)]
        
        def reduce_chunk(chunk):
            result = initializer
            for item in chunk:
                if result is None:
                    result = item
                else:
                    result = func(result, item)
            return result
        
        # 并行处理每个块
        chunk_results = self.map(reduce_chunk, chunks)
        
        # 合并块结果
        final_result = initializer
        for chunk_result in chunk_results:
            if chunk_result is not None:
                if final_result is None:
                    final_result = chunk_result
                else:
                    final_result = func(final_result, chunk_result)
        
        return final_result


class SimulationParallelizer:
    """仿真并行化器"""
    
    def __init__(self, config: Optional[ParallelConfig] = None):
        self.config = config or ParallelConfig()
        self.processor = ParallelProcessor(config)
    
    def run_parallel_simulations(self, 
                                simulation_func: Callable,
                                parameter_sets: List[Dict[str, Any]],
                                **kwargs) -> List[Any]:
        """并行运行多个仿真"""
        
        def run_single_simulation(params):
            """运行单个仿真"""
            try:
                return simulation_func(**params, **kwargs)
            except Exception as e:
                logger.error(f"Simulation failed with params {params}: {e}")
                return None
        
        logger.info(f"Starting parallel execution of {len(parameter_sets)} simulations")
        start_time = time.time()
        
        results = self.processor.map(run_single_simulation, parameter_sets)
        
        execution_time = time.time() - start_time
        successful_results = [r for r in results if r is not None]
        
        logger.info(f"Parallel simulation completed: {len(successful_results)}/{len(parameter_sets)} successful, "
                   f"execution time: {execution_time:.2f}s")
        
        return results
    
    def run_patient_batches(self,
                           patient_processor: Callable,
                           patients: List[Any],
                           batch_size: Optional[int] = None) -> List[Any]:
        """并行处理患者批次"""
        
        batch_size = batch_size or max(1, len(patients) // self.config.max_workers)
        batches = [patients[i:i + batch_size] for i in range(0, len(patients), batch_size)]
        
        def process_batch(batch):
            """处理患者批次"""
            return [patient_processor(patient) for patient in batch]
        
        logger.info(f"开始并行处理 {len(patients)} 个患者，分为 {len(batches)} 个批次")
        
        batch_results = self.processor.map(process_batch, batches)
        
        # 展平结果
        results = []
        for batch_result in batch_results:
            if batch_result:
                results.extend(batch_result)
        
        return results


def parallel_map(func: Callable, 
                iterable: List[Any], 
                max_workers: Optional[int] = None,
                use_processes: bool = True,
                chunk_size: int = 1) -> List[Any]:
    """便捷的并行映射函数"""
    config = ParallelConfig(
        max_workers=max_workers,
        use_processes=use_processes,
        chunk_size=chunk_size
    )
    processor = ParallelProcessor(config)
    return processor.map(func, iterable)


def parallel_simulation(simulation_func: Callable,
                       parameter_sets: List[Dict[str, Any]],
                       max_workers: Optional[int] = None,
                       **kwargs) -> List[Any]:
    """便捷的并行仿真函数"""
    config = ParallelConfig(max_workers=max_workers)
    parallelizer = SimulationParallelizer(config)
    return parallelizer.run_parallel_simulations(simulation_func, parameter_sets, **kwargs)


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total_tasks: int):
        self.total_tasks = total_tasks
        self.completed_tasks = 0
        self.start_time = time.time()
        self._lock = threading.Lock()
    
    def update(self, increment: int = 1):
        """更新进度"""
        with self._lock:
            self.completed_tasks += increment
            progress = self.completed_tasks / self.total_tasks
            elapsed_time = time.time() - self.start_time
            
            if self.completed_tasks > 0:
                eta = elapsed_time * (self.total_tasks - self.completed_tasks) / self.completed_tasks
                logger.info(f"进度: {progress:.1%} ({self.completed_tasks}/{self.total_tasks}), "
                           f"预计剩余时间: {eta:.1f}s")
    
    def is_complete(self) -> bool:
        """检查是否完成"""
        return self.completed_tasks >= self.total_tasks
