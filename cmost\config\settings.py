"""
Configuration settings for the CMOST application.
This module provides centralized configuration management for the application.
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, List, Union
from pathlib import Path


class Settings:
    """
    Settings manager for the CMOST application.
    Handles loading, saving, and accessing configuration settings.
    """
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None) -> None:
        """
        Initialize settings manager.
        
        Args:
            config_path: Path to configuration file. If None, uses default locations.
        """
        self.config_path: Optional[Union[str, Path]] = config_path
        self.current_path: str = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.settings: Dict[str, Any] = {}
        self._load_default_settings()
        
        if config_path:
            self.load_settings(str(config_path))
    
    def _load_default_settings(self):
        """Load default settings."""
        # Application settings
        self.settings = {
            'Settings_Name': 'Default',
            'Comment': 'no comment please',
            'Identification': 'This_is_a_genuine_PolypCalculator_File',
            'NumberPatientsValues': [10000, 25000, 50000, 100000, 1000000,10000000],
            'ResultsPath': os.path.join(self.current_path, 'Results'),
            'SettingsPath': os.path.join(self.current_path, 'Settings'),
            'DispFlag': True,
            'Version': '1.0.0',
            
            # Simulation settings
            'Simulation': {
                'MaxAge': 100,
                'StartAge': 20,
                'TimeStep': 1.0,
                'RandomSeed': 42,
                'EnableMultiprocessing': True,
                'NumProcesses': 0,  # 0 means use all available cores
            },
            
            # Model parameters
            'ModelParameters': {
                'early_mult': 0.02,
                'early_width': 15.0,
                'early_center': 55.0,
                'adv_mult': 0.01,
                'adv_width': 10.0,
                'adv_center': 65.0,
                'cancer_mult': 0.005,
                'cancer_width': 8.0,
                'cancer_center': 70.0,
                'preclinical_dwell_time': 3.0
            },
            
            # Screening settings
            'Screening': {
                'EnableScreening': True,
                'ScreeningAges': [40, 45, 50, 55, 60, 65, 70, 75, 80, 85],
                'ScreeningCompliance': 0.6,
                'FollowupYears': 10,
                'FollowupCompliance': 0.8
            },
            
            # UI settings
            'UI': {
                'Theme': 'default',
                'FontSize': 10,
                'ShowToolbar': True,
                'DefaultView': 'simulation',
                'AutoSave': True,
                'AutoSaveInterval': 10  # minutes
            }
        }
    
    def load_settings(self, file_path: str) -> bool:
        """
        Load settings from a file.
        
        Args:
            file_path: Path to settings file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.mat':
                # Load MATLAB .mat file
                try:
                    import scipy.io as sio
                except ImportError:
                    print("scipy is required to load MATLAB files")
                    return False
                    
                mat_contents = sio.loadmat(file_path)
                
                # Get the 'temp' structure from the file
                if 'temp' in mat_contents:
                    self.settings.update(mat_contents['temp'])
                else:
                    # If 'temp' doesn't exist, try to load all variables
                    for key in mat_contents:
                        if not key.startswith('__'):  # Skip MATLAB metadata
                            self.settings[key] = mat_contents[key]
            
            elif file_ext == '.json':
                # Load JSON file
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.settings.update(json.load(f))
            
            elif file_ext in ['.yaml', '.yml']:
                # Load YAML file
                try:
                    import yaml
                except ImportError:
                    print("PyYAML is required to load YAML files")
                    return False

                with open(file_path, 'r', encoding='utf-8') as f:
                    self.settings.update(yaml.safe_load(f))

            elif file_ext in ['.xlsx', '.xls']:
                # Load Excel file
                try:
                    import pandas as pd
                except ImportError:
                    print("pandas is required to load Excel files")
                    return False

                try:
                    import openpyxl
                except ImportError:
                    print("openpyxl is required to load Excel files")
                    return False

                # Read Excel file with multiple sheets
                excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')

                # Process each sheet
                for sheet_name, df in excel_data.items():
                    if sheet_name.lower() == 'settings' or sheet_name.lower() == 'config':
                        # Main settings sheet - expect key-value pairs
                        if len(df.columns) >= 2:
                            # Assume first column is key, second is value
                            key_col = df.columns[0]
                            value_col = df.columns[1]

                            for _, row in df.iterrows():
                                key = str(row[key_col]).strip()
                                value = row[value_col]

                                # Skip empty or NaN keys
                                if pd.isna(key) or key == '' or key == 'nan':
                                    continue

                                # Handle different value types
                                if pd.isna(value):
                                    value = None
                                elif isinstance(value, str):
                                    # Try to parse as JSON for complex values
                                    try:
                                        value = json.loads(value)
                                    except (json.JSONDecodeError, ValueError):
                                        # Keep as string if not valid JSON
                                        pass

                                # Set nested keys using dot notation
                                self._set_nested_value(key, value)

                    elif sheet_name.lower() == 'model_parameters':
                        # Model parameters sheet
                        if 'ModelParameters' not in self.settings:
                            self.settings['ModelParameters'] = {}

                        if len(df.columns) >= 2:
                            key_col = df.columns[0]
                            value_col = df.columns[1]

                            for _, row in df.iterrows():
                                key = str(row[key_col]).strip()
                                value = row[value_col]

                                if pd.isna(key) or key == '' or key == 'nan':
                                    continue

                                if not pd.isna(value):
                                    self.settings['ModelParameters'][key] = value

                    elif sheet_name.lower() == 'screening':
                        # Screening settings sheet
                        if 'Screening' not in self.settings:
                            self.settings['Screening'] = {}

                        if len(df.columns) >= 2:
                            key_col = df.columns[0]
                            value_col = df.columns[1]

                            for _, row in df.iterrows():
                                key = str(row[key_col]).strip()
                                value = row[value_col]

                                if pd.isna(key) or key == '' or key == 'nan':
                                    continue

                                if not pd.isna(value):
                                    # Handle list values (e.g., ScreeningAges)
                                    if isinstance(value, str) and ',' in value:
                                        try:
                                            value = [float(x.strip()) for x in value.split(',')]
                                        except ValueError:
                                            value = [x.strip() for x in value.split(',')]

                                    self.settings['Screening'][key] = value

            else:
                print(f"Unsupported file format: {file_ext}")
                return False
            
            return True
        
        except FileNotFoundError:
            print(f"Settings file not found: {file_path}")
            return False
        except PermissionError:
            print(f"Permission denied accessing file: {file_path}")
            return False
        except json.JSONDecodeError as e:
            print(f"Invalid JSON format in {file_path}: {str(e)}")
            return False
        except UnicodeDecodeError as e:
            print(f"Encoding error reading {file_path}: {str(e)}")
            return False
        except (KeyError, ValueError) as e:
            print(f"Invalid settings format in {file_path}: {str(e)}")
            return False
        except Exception as e:
            print(f"Unexpected error loading settings from {file_path}: {str(e)}")
            return False
    
    def save_settings(self, file_path: Optional[str] = None) -> bool:
        """
        Save settings to a file.
        
        Args:
            file_path: Path to save settings file. If None, uses the current config_path.
            
        Returns:
            bool: True if successful, False otherwise
        """
        if file_path is None:
            file_path = self.config_path
        
        if file_path is None:
            print("No file path specified for saving settings")
            return False
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.mat':
                # Save as MATLAB .mat file
                import scipy.io as sio
                sio.savemat(file_path, {'temp': self.settings})
            
            elif file_ext == '.json':
                # Save as JSON file
                with open(file_path, 'w') as f:
                    json.dump(self.settings, f, indent=4)
            
            elif file_ext in ['.yaml', '.yml']:
                # Save as YAML file
                with open(file_path, 'w') as f:
                    yaml.dump(self.settings, f, default_flow_style=False)

            elif file_ext in ['.xlsx', '.xls']:
                # Save as Excel file
                try:
                    import pandas as pd
                except ImportError:
                    print("pandas is required to save Excel files")
                    return False

                try:
                    import openpyxl
                except ImportError:
                    print("openpyxl is required to save Excel files")
                    return False

                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    # Main settings sheet
                    main_settings = []
                    for key, value in self.settings.items():
                        if key not in ['ModelParameters', 'Screening', 'Simulation', 'UI']:
                            # Convert complex values to JSON strings
                            if isinstance(value, (dict, list)):
                                value_str = json.dumps(value, ensure_ascii=False, indent=2)
                            else:
                                value_str = str(value)
                            main_settings.append({'Parameter': key, 'Value': value_str})

                    if main_settings:
                        df_main = pd.DataFrame(main_settings)
                        df_main.to_excel(writer, sheet_name='Settings', index=False)

                    # Model parameters sheet
                    if 'ModelParameters' in self.settings:
                        model_params = []
                        for key, value in self.settings['ModelParameters'].items():
                            model_params.append({'Parameter': key, 'Value': value})

                        if model_params:
                            df_model = pd.DataFrame(model_params)
                            df_model.to_excel(writer, sheet_name='ModelParameters', index=False)

                    # Screening settings sheet
                    if 'Screening' in self.settings:
                        screening_params = []
                        for key, value in self.settings['Screening'].items():
                            # Handle list values
                            if isinstance(value, list):
                                value_str = ', '.join(str(x) for x in value)
                            else:
                                value_str = str(value)
                            screening_params.append({'Parameter': key, 'Value': value_str})

                        if screening_params:
                            df_screening = pd.DataFrame(screening_params)
                            df_screening.to_excel(writer, sheet_name='Screening', index=False)

                    # Simulation settings sheet
                    if 'Simulation' in self.settings:
                        sim_params = []
                        for key, value in self.settings['Simulation'].items():
                            sim_params.append({'Parameter': key, 'Value': value})

                        if sim_params:
                            df_sim = pd.DataFrame(sim_params)
                            df_sim.to_excel(writer, sheet_name='Simulation', index=False)

                    # UI settings sheet
                    if 'UI' in self.settings:
                        ui_params = []
                        for key, value in self.settings['UI'].items():
                            ui_params.append({'Parameter': key, 'Value': value})

                        if ui_params:
                            df_ui = pd.DataFrame(ui_params)
                            df_ui.to_excel(writer, sheet_name='UI', index=False)

            else:
                print(f"Unsupported file format: {file_ext}")
                return False
            
            return True
        
        except Exception as e:
            print(f"Error saving settings: {str(e)}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a setting value.
        
        Args:
            key: Setting key (can use dot notation for nested settings)
            default: Default value if key not found
            
        Returns:
            Setting value or default
        """
        # Handle nested keys with dot notation
        if '.' in key:
            parts = key.split('.')
            current = self.settings
            
            for part in parts[:-1]:
                if part not in current:
                    return default
                current = current[part]
            
            return current.get(parts[-1], default)
        
        return self.settings.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a setting value.
        
        Args:
            key: Setting key (can use dot notation for nested settings)
            value: Value to set
        """
        # Handle nested keys with dot notation
        if '.' in key:
            parts = key.split('.')
            current = self.settings
            
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            current[parts[-1]] = value
        else:
            self.settings[key] = value
    
    def get_all(self) -> Dict[str, Any]:
        """
        Get all settings.
        
        Returns:
            Dict[str, Any]: All settings
        """
        return self.settings.copy()
    
    def reset_to_defaults(self) -> None:
        """Reset all settings to defaults."""
        self._load_default_settings()
    
    def update(self, settings_dict: Dict[str, Any]) -> None:
        """
        Update settings with values from a dictionary.
        
        Args:
            settings_dict: Dictionary of settings to update
        """
        self._update_recursive(self.settings, settings_dict)
    
    def _update_recursive(self, current: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """
        Recursively update nested dictionaries.
        
        Args:
            current: Current dictionary to update
            update_dict: Dictionary with updates
        """
        for key, value in update_dict.items():
            if key in current and isinstance(current[key], dict) and isinstance(value, dict):
                self._update_recursive(current[key], value)
            else:
                current[key] = value
    
    def export_to_matlab(self, file_path: str) -> bool:
        """
        Export settings to MATLAB format.
        
        Args:
            file_path: Path to save MATLAB file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            import scipy.io as sio
            sio.savemat(file_path, {'temp': self.settings})
            return True
        except Exception as e:
            print(f"Error exporting to MATLAB: {str(e)}")
            return False
    
    def _set_nested_value(self, key: str, value) -> None:
        """
        Set a nested value using dot notation.

        Args:
            key: Key with dot notation (e.g., 'Simulation.RandomSeed')
            value: Value to set
        """
        keys = key.split('.')
        current = self.settings

        # Navigate to the parent dictionary
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            elif not isinstance(current[k], dict):
                # If the intermediate key exists but is not a dict, convert it
                current[k] = {}
            current = current[k]

        # Set the final value
        current[keys[-1]] = value

    def import_from_matlab(self, file_path: str) -> bool:
        """
        Import settings from MATLAB format.
        
        Args:
            file_path: Path to MATLAB file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            import scipy.io as sio
            mat_contents = sio.loadmat(file_path)
            
            if 'temp' in mat_contents:
                self.settings.update(mat_contents['temp'])
                return True
            else:
                print("No 'temp' variable found in MATLAB file")
                return False
        except Exception as e:
            print(f"Error importing from MATLAB: {str(e)}")
            return False


# Create a global settings instance
settings = Settings()


def initialize_settings(config_path: Optional[str] = None) -> None:
    """
    Initialize global settings.
    
    Args:
        config_path: Path to configuration file
    """
    global settings
    settings = Settings(config_path)


def get_setting(key: str, default: Any = None) -> Any:
    """
    Get a setting value from the global settings.
    
    Args:
        key: Setting key
        default: Default value if key not found
        
    Returns:
        Setting value or default
    """
    global settings
    return settings.get(key, default)


def set_setting(key: str, value: Any) -> None:
    """
    Set a setting value in the global settings.
    
    Args:
        key: Setting key
        value: Value to set
    """
    global settings
    settings.set(key, value)


def save_settings(file_path: Optional[str] = None) -> bool:
    """
    Save global settings to a file.
    
    Args:
        file_path: Path to save settings file
        
    Returns:
        bool: True if successful, False otherwise
    """
    global settings
    return settings.save_settings(file_path)


def load_settings(file_path: str) -> bool:
    """
    Load settings from a file into global settings.
    
    Args:
        file_path: Path to settings file
        
    Returns:
        bool: True if successful, False otherwise
    """
    global settings
    return settings.load_settings(file_path)