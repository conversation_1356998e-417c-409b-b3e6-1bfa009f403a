"""
Output configuration panel for CMOST application.

This module implements a dedicated panel for output configuration,
including format selection, content options, and file naming.
"""

import tkinter as tk
from tkinter import ttk, filedialog
import os
from pathlib import Path
from typing import Dict, Any, List


class OutputConfigPanel(ttk.Frame):
    """
    Output configuration panel for CMOST application.
    
    Provides interface for:
    - Output format selection
    - Content selection
    - File naming options
    - Directory selection
    """
    
    def __init__(self, parent, **kwargs):
        """
        Initialize the output configuration panel.
        
        Args:
            parent: Parent widget
            **kwargs: Additional keyword arguments
        """
        super().__init__(parent, **kwargs)
        
        # Panel state
        self.output_config = {}
        
        # Output format options
        self.format_options = {
            "excel": {"name": "Excel报告 (.xlsx)", "description": "包含表格和图表的Excel文件"},
            "pdf": {"name": "PDF报告 (.pdf)", "description": "专业格式的PDF报告"},
            "csv": {"name": "CSV数据 (.csv)", "description": "逗号分隔的数据文件"},
            "json": {"name": "JSON数据 (.json)", "description": "结构化的JSON数据文件"},
            "matlab": {"name": "MATLAB数据 (.mat)", "description": "MATLAB格式的数据文件"},
            "html": {"name": "HTML报告 (.html)", "description": "网页格式的交互式报告"}
        }
        
        # Content options
        self.content_options = {
            "summary": {"name": "摘要统计", "description": "关键指标的摘要统计"},
            "detailed_results": {"name": "详细结果", "description": "完整的仿真结果数据"},
            "charts": {"name": "图表可视化", "description": "各种统计图表和可视化"},
            "calibration_results": {"name": "调参结果", "description": "模型调参的详细结果"},
            "screening_analysis": {"name": "筛查分析", "description": "筛查策略的效果分析"},
            "cost_effectiveness": {"name": "成本效益分析", "description": "成本效益评估结果"},
            "sensitivity_analysis": {"name": "敏感性分析", "description": "参数敏感性分析结果"},
            "raw_data": {"name": "原始数据", "description": "未处理的仿真原始数据"}
        }
        
        # Create UI components
        self.create_ui()
    
    def create_ui(self):
        """Create the user interface components."""
        # Output format selection
        format_frame = ttk.LabelFrame(self, text="输出格式选择", padding=10)
        format_frame.pack(fill=tk.X, pady=5)
        
        # Create format checkboxes
        self.format_vars = {}
        row = 0
        col = 0
        for format_key, format_info in self.format_options.items():
            var = tk.BooleanVar(value=(format_key in ["excel", "pdf"]))
            self.format_vars[format_key] = var
            
            check = ttk.Checkbutton(
                format_frame,
                text=format_info["name"],
                variable=var,
                command=self._update_preview
            )
            check.grid(row=row, column=col, sticky=tk.W, padx=10, pady=3)
            
            # Add tooltip (simplified)
            self._create_tooltip(check, format_info["description"])
            
            col += 1
            if col >= 2:
                col = 0
                row += 1
        
        # Output content selection
        content_frame = ttk.LabelFrame(self, text="输出内容选择", padding=10)
        content_frame.pack(fill=tk.X, pady=5)
        
        # Create content checkboxes
        self.content_vars = {}
        row = 0
        col = 0
        for content_key, content_info in self.content_options.items():
            var = tk.BooleanVar(value=(content_key in ["summary", "detailed_results", "charts"]))
            self.content_vars[content_key] = var
            
            check = ttk.Checkbutton(
                content_frame,
                text=content_info["name"],
                variable=var,
                command=self._update_preview
            )
            check.grid(row=row, column=col, sticky=tk.W, padx=10, pady=3)
            
            # Add tooltip
            self._create_tooltip(check, content_info["description"])
            
            col += 1
            if col >= 2:
                col = 0
                row += 1
        
        # Output directory and naming
        dir_frame = ttk.LabelFrame(self, text="输出目录和文件命名", padding=10)
        dir_frame.pack(fill=tk.X, pady=5)
        
        # Output directory
        ttk.Label(dir_frame, text="输出目录:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        # 确保results目录存在并设为默认
        results_dir = os.path.abspath("results")
        os.makedirs(results_dir, exist_ok=True)
        self.output_dir_var = tk.StringVar(value=results_dir)

        dir_entry = ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=50)
        dir_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(
            dir_frame,
            text="浏览...",
            command=self._browse_directory
        ).grid(row=0, column=2, padx=5, pady=5)

        # 添加创建目录按钮
        ttk.Button(
            dir_frame,
            text="创建目录",
            command=self._create_output_directory
        ).grid(row=0, column=3, padx=5, pady=5)
        
        # File name prefix
        ttk.Label(dir_frame, text="文件名前缀:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.filename_prefix_var = tk.StringVar(value="cmost_simulation")
        prefix_entry = ttk.Entry(dir_frame, textvariable=self.filename_prefix_var, width=30)
        prefix_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Naming options
        naming_options_frame = ttk.Frame(dir_frame)
        naming_options_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)
        
        self.include_timestamp_var = tk.BooleanVar(value=True)
        timestamp_check = ttk.Checkbutton(
            naming_options_frame,
            text="包含时间戳",
            variable=self.include_timestamp_var,
            command=self._update_preview
        )
        timestamp_check.pack(side=tk.LEFT, padx=5)
        
        self.include_params_var = tk.BooleanVar(value=False)
        params_check = ttk.Checkbutton(
            naming_options_frame,
            text="包含参数信息",
            variable=self.include_params_var,
            command=self._update_preview
        )
        params_check.pack(side=tk.LEFT, padx=5)
        
        # Advanced options
        advanced_frame = ttk.LabelFrame(self, text="高级选项", padding=10)
        advanced_frame.pack(fill=tk.X, pady=5)
        
        # Compression option
        self.compress_var = tk.BooleanVar(value=False)
        compress_check = ttk.Checkbutton(
            advanced_frame,
            text="压缩输出文件",
            variable=self.compress_var
        )
        compress_check.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Separate files option
        self.separate_files_var = tk.BooleanVar(value=True)
        separate_check = ttk.Checkbutton(
            advanced_frame,
            text="为不同内容创建单独文件",
            variable=self.separate_files_var
        )
        separate_check.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Auto-open option
        self.auto_open_var = tk.BooleanVar(value=True)
        auto_open_check = ttk.Checkbutton(
            advanced_frame,
            text="完成后自动打开结果",
            variable=self.auto_open_var
        )
        auto_open_check.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Backup option
        self.backup_var = tk.BooleanVar(value=False)
        backup_check = ttk.Checkbutton(
            advanced_frame,
            text="创建备份副本",
            variable=self.backup_var
        )
        backup_check.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Output preview
        preview_frame = ttk.LabelFrame(self, text="输出预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Preview text widget
        self.preview_text = tk.Text(preview_frame, height=10, wrap=tk.WORD)
        self.preview_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add scrollbar
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=self.preview_text.yview)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        
        # Control buttons
        button_frame = ttk.Frame(self)
        button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(
            button_frame,
            text="重置为默认",
            command=self._reset_to_defaults
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            button_frame,
            text="保存配置",
            command=self._save_config
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            button_frame,
            text="加载配置",
            command=self._load_config
        ).pack(side=tk.LEFT, padx=5)
        
        # Initial preview update
        self._update_preview()
        
        # Bind events
        self.filename_prefix_var.trace("w", lambda *args: self._update_preview())
        self.output_dir_var.trace("w", lambda *args: self._update_preview())
    
    def _create_tooltip(self, widget, text):
        """Create a simple tooltip for a widget."""
        def on_enter(event):
            # This is a simplified tooltip implementation
            # In a real application, you might use a more sophisticated tooltip library
            pass
        
        def on_leave(event):
            pass
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    def _browse_directory(self):
        """Browse for output directory."""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir_var.get()
        )
        if directory:
            self.output_dir_var.set(directory)
    
    def _update_preview(self):
        """Update the output preview."""
        preview_text = "=== 输出配置预览 ===\n\n"
        
        # Selected formats
        selected_formats = [key for key, var in self.format_vars.items() if var.get()]
        if selected_formats:
            preview_text += "输出格式:\n"
            for fmt in selected_formats:
                preview_text += f"  • {self.format_options[fmt]['name']}\n"
        else:
            preview_text += "输出格式: 未选择任何格式\n"
        
        preview_text += "\n"
        
        # Selected content
        selected_content = [key for key, var in self.content_vars.items() if var.get()]
        if selected_content:
            preview_text += "输出内容:\n"
            for content in selected_content:
                preview_text += f"  • {self.content_options[content]['name']}\n"
        else:
            preview_text += "输出内容: 未选择任何内容\n"
        
        preview_text += "\n"
        
        # File naming
        preview_text += "文件命名:\n"
        preview_text += f"  输出目录: {self.output_dir_var.get()}\n"
        preview_text += f"  文件前缀: {self.filename_prefix_var.get()}\n"
        
        # Generate example filenames
        example_files = self._generate_example_filenames()
        if example_files:
            preview_text += "\n示例文件名:\n"
            for filename in example_files[:5]:  # Show first 5 examples
                preview_text += f"  • {filename}\n"
            if len(example_files) > 5:
                preview_text += f"  ... 还有 {len(example_files) - 5} 个文件\n"
        
        # Advanced options
        advanced_options = []
        if self.compress_var.get():
            advanced_options.append("压缩输出文件")
        if self.separate_files_var.get():
            advanced_options.append("创建单独文件")
        if self.auto_open_var.get():
            advanced_options.append("自动打开结果")
        if self.backup_var.get():
            advanced_options.append("创建备份副本")
        
        if advanced_options:
            preview_text += "\n高级选项:\n"
            for option in advanced_options:
                preview_text += f"  • {option}\n"
        
        # Update preview text widget
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(tk.END, preview_text)
    
    def _generate_example_filenames(self) -> List[str]:
        """Generate example filenames based on current configuration."""
        filenames = []
        
        prefix = self.filename_prefix_var.get() or "cmost_simulation"
        
        # Add timestamp if selected
        timestamp_suffix = ""
        if self.include_timestamp_var.get():
            timestamp_suffix = "_20240117_143022"
        
        # Add parameter info if selected
        param_suffix = ""
        if self.include_params_var.get():
            param_suffix = "_100k_patients"
        
        selected_formats = [key for key, var in self.format_vars.items() if var.get()]
        selected_content = [key for key, var in self.content_vars.items() if var.get()]
        
        if self.separate_files_var.get():
            # Create separate files for each content type
            for content in selected_content:
                for fmt in selected_formats:
                    ext = self._get_file_extension(fmt)
                    filename = f"{prefix}_{content}{param_suffix}{timestamp_suffix}.{ext}"
                    filenames.append(filename)
        else:
            # Create combined files
            for fmt in selected_formats:
                ext = self._get_file_extension(fmt)
                filename = f"{prefix}{param_suffix}{timestamp_suffix}.{ext}"
                filenames.append(filename)
        
        return filenames
    
    def _get_file_extension(self, format_key: str) -> str:
        """Get file extension for a format."""
        extensions = {
            "excel": "xlsx",
            "pdf": "pdf",
            "csv": "csv",
            "json": "json",
            "matlab": "mat",
            "html": "html"
        }
        return extensions.get(format_key, "txt")
    
    def _reset_to_defaults(self):
        """Reset all options to default values."""
        # Reset format selections
        for key, var in self.format_vars.items():
            var.set(key in ["excel", "pdf"])
        
        # Reset content selections
        for key, var in self.content_vars.items():
            var.set(key in ["summary", "detailed_results", "charts"])
        
        # Reset other options
        self.output_dir_var.set("./results")
        self.filename_prefix_var.set("cmost_simulation")
        self.include_timestamp_var.set(True)
        self.include_params_var.set(False)
        self.compress_var.set(False)
        self.separate_files_var.set(True)
        self.auto_open_var.set(True)
        self.backup_var.set(False)
        
        self._update_preview()
    
    def _save_config(self):
        """Save current configuration to file."""
        config_file = filedialog.asksaveasfilename(
            title="保存输出配置",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if config_file:
            config = self.get_output_config()
            try:
                import json
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                tk.messagebox.showinfo("保存成功", f"配置已保存到: {config_file}")
            except Exception as e:
                tk.messagebox.showerror("保存失败", f"保存配置时发生错误: {str(e)}")
    
    def _load_config(self):
        """Load configuration from file."""
        config_file = filedialog.askopenfilename(
            title="加载输出配置",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if config_file:
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.set_output_config(config)
                tk.messagebox.showinfo("加载成功", f"配置已从 {config_file} 加载")
            except Exception as e:
                tk.messagebox.showerror("加载失败", f"加载配置时发生错误: {str(e)}")
    
    def get_output_config(self) -> Dict[str, Any]:
        """
        Get the current output configuration.
        
        Returns:
            Dictionary containing output configuration
        """
        config = {
            "formats": {key: var.get() for key, var in self.format_vars.items()},
            "content": {key: var.get() for key, var in self.content_vars.items()},
            "output_directory": self.output_dir_var.get(),
            "filename_prefix": self.filename_prefix_var.get(),
            "include_timestamp": self.include_timestamp_var.get(),
            "include_params": self.include_params_var.get(),
            "compress": self.compress_var.get(),
            "separate_files": self.separate_files_var.get(),
            "auto_open": self.auto_open_var.get(),
            "backup": self.backup_var.get()
        }
        return config
    
    def set_output_config(self, config: Dict[str, Any]):
        """
        Set output configuration from dictionary.
        
        Args:
            config: Configuration dictionary
        """
        # Set format selections
        if "formats" in config:
            for key, value in config["formats"].items():
                if key in self.format_vars:
                    self.format_vars[key].set(value)
        
        # Set content selections
        if "content" in config:
            for key, value in config["content"].items():
                if key in self.content_vars:
                    self.content_vars[key].set(value)
        
        # Set other options
        if "output_directory" in config:
            self.output_dir_var.set(config["output_directory"])
        if "filename_prefix" in config:
            self.filename_prefix_var.set(config["filename_prefix"])
        if "include_timestamp" in config:
            self.include_timestamp_var.set(config["include_timestamp"])
        if "include_params" in config:
            self.include_params_var.set(config["include_params"])
        if "compress" in config:
            self.compress_var.set(config["compress"])
        if "separate_files" in config:
            self.separate_files_var.set(config["separate_files"])
        if "auto_open" in config:
            self.auto_open_var.set(config["auto_open"])
        if "backup" in config:
            self.backup_var.set(config["backup"])
        
        self._update_preview()
    
    def validate_config(self) -> tuple[bool, str]:
        """
        Validate the current configuration.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check if at least one format is selected
        if not any(var.get() for var in self.format_vars.values()):
            return False, "请至少选择一种输出格式"
        
        # Check if at least one content type is selected
        if not any(var.get() for var in self.content_vars.values()):
            return False, "请至少选择一种输出内容"
        
        # Check if output directory is valid
        output_dir = self.output_dir_var.get()
        if not output_dir:
            return False, "请指定输出目录"
        
        # Check if filename prefix is valid
        filename_prefix = self.filename_prefix_var.get()
        if not filename_prefix:
            return False, "请指定文件名前缀"
        
        # Check for invalid characters in filename
        invalid_chars = '<>:"/\\|?*'
        if any(char in filename_prefix for char in invalid_chars):
            return False, f"文件名前缀包含无效字符: {invalid_chars}"
        
        return True, ""

    def _create_output_directory(self):
        """创建输出目录"""
        output_dir = self.output_dir_var.get()
        if output_dir:
            try:
                os.makedirs(output_dir, exist_ok=True)
                # 验证目录是否可写
                test_file = os.path.join(output_dir, "test_write.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)

                # 创建子目录结构
                subdirs = ['reports', 'data', 'charts', 'logs', 'calibration']
                for subdir in subdirs:
                    os.makedirs(os.path.join(output_dir, subdir), exist_ok=True)

                from tkinter import messagebox
                messagebox.showinfo("成功", f"输出目录创建成功:\n{output_dir}\n\n已创建以下子目录:\n" +
                                  "\n".join([f"- {subdir}" for subdir in subdirs]))

            except PermissionError:
                from tkinter import messagebox
                messagebox.showerror("权限错误", f"没有权限在指定位置创建目录:\n{output_dir}")
            except Exception as e:
                from tkinter import messagebox
                messagebox.showerror("创建失败", f"创建输出目录时发生错误:\n{str(e)}")

    def ensure_output_directory(self) -> bool:
        """确保输出目录存在且可写"""
        output_dir = self.output_dir_var.get()
        if not output_dir:
            return False

        try:
            # 创建目录（如果不存在）
            os.makedirs(output_dir, exist_ok=True)

            # 验证目录是否可写
            test_file = os.path.join(output_dir, "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)

            return True

        except Exception:
            return False
