"""
Demonstration of CMOST multi-tool screening strategies.

This script shows how to use different screening strategies and combinations
for colorectal cancer screening.
"""

import logging
import numpy as np
from cmost.screening.strategy_manager import (
    ScreeningStrategyManager,
    ScreeningStrategy,
    ScreeningTest,
    StrategyType
)
from cmost.models.patient import Patient


def setup_logging():
    """Set up logging for the demo."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_test_patients():
    """Create test patients with different risk profiles."""
    patients = []
    
    # Low-risk patient
    low_risk = Patient(
        id=1,
        age=55,
        gender='M',
        risk_factors={'smoking': 1.0}
    )
    low_risk.add_polyp(location=2, size=0.5)  # Small distal polyp
    patients.append(("Low Risk", low_risk))
    
    # Moderate-risk patient
    moderate_risk = Patient(
        id=2,
        age=60,
        gender='F',
        risk_factors={'family_history': 2.0}
    )
    moderate_risk.add_polyp(location=4, size=1.2)  # Large proximal polyp
    moderate_risk.add_serrated_lesion(location=5, size=0.8)
    patients.append(("Moderate Risk", moderate_risk))
    
    # High-risk patient
    high_risk = Patient(
        id=3,
        age=65,
        gender='M',
        risk_factors={'family_history': 3.0, 'smoking': 1.5}
    )
    high_risk.add_polyp(location=3, size=1.5)  # Advanced adenoma
    high_risk.add_serrated_lesion(location=6, size=1.0)
    # Add a small cancer
    from cmost.models.cancer import Cancer
    cancer = Cancer(
        id=1001,
        location=4,
        stage=7,  # Stage I
        patient_id=3,
        patient_gender='M'
    )
    high_risk.cancers.append(cancer)
    patients.append(("High Risk", high_risk))
    
    return patients


def demo_single_test_strategies():
    """Demonstrate single test screening strategies."""
    print("\n" + "="*60)
    print("DEMO 1: Single Test Strategies")
    print("="*60)
    
    manager = ScreeningStrategyManager()
    patients = create_test_patients()
    
    strategies = ["colonoscopy_only", "fit_only"]
    
    for strategy_name in strategies:
        print(f"\n--- {strategy_name.replace('_', ' ').title()} ---")
        
        for patient_type, patient in patients:
            # Set random seed for reproducible results
            np.random.seed(42 + patient.id)
            
            result = manager.apply_strategy(strategy_name, patient, 2023)
            
            print(f"{patient_type} Patient (ID: {patient.id}):")
            print(f"  - Test: {result.get('test', 'N/A')}")
            print(f"  - Result: {result.get('result', 'N/A')}")
            print(f"  - Cost: ${result.get('cost', 0):.2f}")
            
            if result.get('findings'):
                findings = result['findings']
                total = findings.get('total_detected', 0)
                print(f"  - Lesions detected: {total}")
                if total > 0:
                    print(f"    * Polyps: {len(findings.get('polyps', []))}")
                    print(f"    * Serrated lesions: {len(findings.get('serrated_lesions', []))}")
                    print(f"    * Cancers: {len(findings.get('cancers', []))}")


def demo_sequential_strategies():
    """Demonstrate sequential screening strategies."""
    print("\n" + "="*60)
    print("DEMO 2: Sequential Strategies")
    print("="*60)
    
    manager = ScreeningStrategyManager()
    patients = create_test_patients()
    
    print("\n--- FIT with Colonoscopy Follow-up ---")
    
    for patient_type, patient in patients:
        # Set random seed for reproducible results
        np.random.seed(123 + patient.id)
        
        result = manager.apply_strategy("fit_colonoscopy", patient, 2023)
        
        print(f"\n{patient_type} Patient (ID: {patient.id}):")
        
        # Primary test results
        primary = result.get('primary_test', {})
        print(f"  Primary Test (FIT):")
        print(f"    - Result: {primary.get('result', 'N/A')}")
        print(f"    - Cost: ${primary.get('cost', 0):.2f}")
        
        # Follow-up test results
        if 'follow_up_test' in result:
            followup = result['follow_up_test']
            print(f"  Follow-up Test (Colonoscopy):")
            print(f"    - Result: {followup.get('result', 'N/A')}")
            print(f"    - Cost: ${followup.get('cost', 0):.2f}")
            
            if followup.get('findings'):
                findings = followup['findings']
                total = findings.get('total_detected', 0)
                print(f"    - Lesions detected: {total}")
        else:
            print(f"  No follow-up needed (FIT negative)")
        
        # Calculate total cost
        total_cost = primary.get('cost', 0)
        if 'follow_up_test' in result:
            total_cost += result['follow_up_test'].get('cost', 0)
        print(f"  Total Cost: ${total_cost:.2f}")


def demo_parallel_strategies():
    """Demonstrate parallel screening strategies."""
    print("\n" + "="*60)
    print("DEMO 3: Parallel Strategies")
    print("="*60)
    
    manager = ScreeningStrategyManager()
    patients = create_test_patients()
    
    print("\n--- Sigmoidoscopy + FIT Combination ---")
    
    for patient_type, patient in patients:
        # Set random seed for reproducible results
        np.random.seed(456 + patient.id)
        
        result = manager.apply_strategy("sigmoidoscopy_fit", patient, 2023)
        
        print(f"\n{patient_type} Patient (ID: {patient.id}):")
        print(f"  Combined Result: {result.get('combined_result', 'N/A')}")
        
        tests = result.get('tests', {})
        total_cost = 0
        total_detected = 0
        
        for test_name, test_result in tests.items():
            print(f"  {test_name.title()}:")
            print(f"    - Result: {test_result.get('result', 'N/A')}")
            print(f"    - Cost: ${test_result.get('cost', 0):.2f}")
            
            total_cost += test_result.get('cost', 0)
            
            if test_result.get('findings'):
                findings = test_result['findings']
                detected = findings.get('total_detected', 0)
                total_detected += detected
                print(f"    - Lesions detected: {detected}")
        
        print(f"  Total Cost: ${total_cost:.2f}")
        print(f"  Total Lesions Detected: {total_detected}")


def demo_risk_stratified_strategy():
    """Demonstrate risk-stratified screening strategy."""
    print("\n" + "="*60)
    print("DEMO 4: Risk-Stratified Strategy")
    print("="*60)
    
    manager = ScreeningStrategyManager()
    patients = create_test_patients()
    
    print("\n--- Risk-Based Test Selection ---")
    
    for patient_type, patient in patients:
        # Set random seed for reproducible results
        np.random.seed(789 + patient.id)
        
        result = manager.apply_strategy("risk_stratified", patient, 2023)
        
        print(f"\n{patient_type} Patient (ID: {patient.id}):")
        print(f"  Risk Score: {result.get('risk_score', 0):.2f}")
        print(f"  Risk Level: {result.get('risk_level', 'N/A')}")
        print(f"  Selected Test: {result.get('test', 'N/A')}")
        print(f"  Result: {result.get('result', 'N/A')}")
        print(f"  Cost: ${result.get('cost', 0):.2f}")
        
        if result.get('findings'):
            findings = result['findings']
            total = findings.get('total_detected', 0)
            print(f"  Lesions detected: {total}")


def demo_custom_strategy():
    """Demonstrate creating and using custom strategies."""
    print("\n" + "="*60)
    print("DEMO 5: Custom Strategy")
    print("="*60)
    
    manager = ScreeningStrategyManager()
    
    # Create a custom strategy: CT Colonography for high-risk patients
    custom_strategy = ScreeningStrategy(
        name="CT Colonography for High Risk",
        strategy_type=StrategyType.SINGLE_TEST,
        primary_test=ScreeningTest.CT_COLONOGRAPHY,
        intervals={ScreeningTest.CT_COLONOGRAPHY: 5},
        age_ranges={ScreeningTest.CT_COLONOGRAPHY: (50, 75)}
    )
    
    # Add custom test characteristics
    from cmost.screening.strategy_manager import TestCharacteristics
    ct_characteristics = TestCharacteristics("ct_colonography")
    ct_characteristics.sensitivity = {
        'small_adenoma': 0.70,
        'advanced_adenoma': 0.85,
        'serrated_lesion': 0.60,
        'cancer': 0.90
    }
    ct_characteristics.specificity = 0.85
    ct_characteristics.cost = 500.0
    ct_characteristics.requires_bowel_prep = True
    ct_characteristics.is_invasive = False
    
    manager.test_characteristics[ScreeningTest.CT_COLONOGRAPHY] = ct_characteristics
    manager.add_custom_strategy(custom_strategy)
    
    print("\n--- Custom CT Colonography Strategy ---")
    
    patients = create_test_patients()
    
    for patient_type, patient in patients:
        # Set random seed for reproducible results
        np.random.seed(999 + patient.id)
        
        result = manager.apply_strategy("ct_colonography_for_high_risk", patient, 2023)
        
        print(f"\n{patient_type} Patient (ID: {patient.id}):")
        print(f"  Test: {result.get('test', 'N/A')}")
        print(f"  Result: {result.get('result', 'N/A')}")
        print(f"  Cost: ${result.get('cost', 0):.2f}")
        
        if result.get('findings'):
            findings = result['findings']
            total = findings.get('total_detected', 0)
            print(f"  Lesions detected: {total}")


def demo_strategy_comparison():
    """Compare different strategies on the same patient."""
    print("\n" + "="*60)
    print("DEMO 6: Strategy Comparison")
    print("="*60)
    
    manager = ScreeningStrategyManager()
    
    # Use the moderate-risk patient for comparison
    _, patient = create_test_patients()[1]  # Moderate risk patient
    
    strategies = [
        "colonoscopy_only",
        "fit_only", 
        "fit_colonoscopy",
        "sigmoidoscopy_fit",
        "risk_stratified"
    ]
    
    print(f"\nComparing strategies for Moderate Risk Patient (ID: {patient.id}):")
    print(f"Patient has {len(patient.polyps)} polyps and {len(patient.serrated_lesions)} serrated lesions")
    
    results = []
    
    for strategy in strategies:
        # Set same random seed for fair comparison
        np.random.seed(42)
        
        try:
            result = manager.apply_strategy(strategy, patient, 2023)
            
            # Calculate total cost and detection
            total_cost = 0
            total_detected = 0
            
            if 'cost' in result:
                total_cost = result['cost']
            elif 'primary_test' in result:
                total_cost += result['primary_test'].get('cost', 0)
                if 'follow_up_test' in result:
                    total_cost += result['follow_up_test'].get('cost', 0)
            elif 'tests' in result:
                for test_result in result['tests'].values():
                    total_cost += test_result.get('cost', 0)
            
            # Count total detections
            if 'findings' in result and result['findings']:
                total_detected = result['findings'].get('total_detected', 0)
            elif 'primary_test' in result:
                primary_findings = result['primary_test'].get('findings', {})
                total_detected += primary_findings.get('total_detected', 0)
                if 'follow_up_test' in result:
                    followup_findings = result['follow_up_test'].get('findings', {})
                    total_detected += followup_findings.get('total_detected', 0)
            elif 'tests' in result:
                for test_result in result['tests'].values():
                    test_findings = test_result.get('findings', {})
                    total_detected += test_findings.get('total_detected', 0)
            
            results.append({
                'strategy': strategy,
                'cost': total_cost,
                'detected': total_detected
            })
            
        except Exception as e:
            print(f"Error with strategy {strategy}: {e}")
    
    # Display comparison
    print(f"\n{'Strategy':<25} {'Cost':<10} {'Detected':<10} {'Cost/Detection':<15}")
    print("-" * 65)
    
    for result in results:
        cost_per_detection = result['cost'] / max(1, result['detected'])
        print(f"{result['strategy']:<25} ${result['cost']:<9.2f} {result['detected']:<10} ${cost_per_detection:<14.2f}")


def main():
    """Run all demonstrations."""
    print("CMOST Multi-Tool Screening Strategies Demonstration")
    print("===================================================")
    
    # Set up logging
    setup_logging()
    
    try:
        # Run demonstrations
        demo_single_test_strategies()
        demo_sequential_strategies()
        demo_parallel_strategies()
        demo_risk_stratified_strategy()
        demo_custom_strategy()
        demo_strategy_comparison()
        
        print("\n" + "="*60)
        print("All demonstrations completed successfully!")
        print("="*60)
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
