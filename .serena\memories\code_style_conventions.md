# 代码风格和约定

## Python代码规范
- **缩进**: 4空格，禁用Tab
- **行长度**: 最大88字符（Black格式化器设置）
- **命名约定**:
  - 变量和函数: snake_case
  - 类: PascalCase
  - 常量: UPPER_CASE
- **字符串格式化**: 优先使用f-string

## 类型提示
- 使用类型提示，但当前设置为逐步启用（`disallow_untyped_defs = false`）
- 导入类型: `from typing import Optional, Dict, Any, List`

## 文档字符串
- 使用详细的docstring，包含Args和Returns部分
- 示例格式:
```python
def function_name(param: str) -> bool:
    \"\"\"
    Function description.
    
    Args:
        param: Parameter description
        
    Returns:
        bool: Return value description
    \"\"\"
```

## 导入顺序
- 遵循isort配置，使用black profile
- 已知第一方包: ["cmost"]

## 代码质量工具配置
- **Black**: 行长度88，目标Python 3.8+
- **Flake8**: 最大行长度88，忽略E203, W503
- **MyPy**: Python 3.8，忽略缺失导入
- **Bandit**: 安全检查，排除测试目录

## 测试约定
- 测试文件: `test_*.py`
- 测试类: `Test*`
- 测试函数: `test_*`
- 测试标记: unit, integration, performance, slow