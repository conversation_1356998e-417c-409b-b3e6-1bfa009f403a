"""
Risk assessment questionnaire module for CMOST screening.

This module implements risk assessment questionnaires as a screening tool,
commonly used as the first step in sequential screening strategies.
"""

from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from dataclasses import dataclass, field
from enum import Enum

from ..models.patient import Patient


class RiskLevel(Enum):
    """Risk level categories."""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class QuestionnaireQuestion:
    """Represents a single questionnaire question."""
    
    id: str
    text: str
    question_type: str  # 'yes_no', 'multiple_choice', 'numeric'
    options: List[str] = field(default_factory=list)
    weight: float = 1.0
    risk_multiplier: Dict[str, float] = field(default_factory=dict)


@dataclass
class QuestionnaireResponse:
    """Represents a patient's response to questionnaire."""
    
    patient_id: int
    responses: Dict[str, Any] = field(default_factory=dict)
    risk_score: float = 0.0
    risk_level: RiskLevel = RiskLevel.LOW
    recommendation: str = ""
    completion_date: Optional[str] = None


class RiskQuestionnaire:
    """Risk assessment questionnaire for colorectal cancer screening."""
    
    def __init__(self, questionnaire_type: str = "standard"):
        """Initialize risk questionnaire.
        
        Args:
            questionnaire_type: Type of questionnaire ('standard', 'asia_pacific', 'custom')
        """
        self.questionnaire_type = questionnaire_type
        self.questions = self._load_questions(questionnaire_type)
        self.scoring_weights = self._load_scoring_weights(questionnaire_type)
        
    def _load_questions(self, questionnaire_type: str) -> List[QuestionnaireQuestion]:
        """Load questions based on questionnaire type.
        
        Args:
            questionnaire_type: Type of questionnaire
            
        Returns:
            List of questions
        """
        if questionnaire_type == "asia_pacific":
            return self._get_asia_pacific_questions()
        elif questionnaire_type == "custom":
            return self._get_custom_questions()
        else:
            return self._get_standard_questions()
            
    def _get_standard_questions(self) -> List[QuestionnaireQuestion]:
        """Get standard risk assessment questions."""
        return [
            QuestionnaireQuestion(
                id="age",
                text="What is your age?",
                question_type="numeric",
                weight=2.0,
                risk_multiplier={"50-59": 1.0, "60-69": 1.5, "70+": 2.0}
            ),
            QuestionnaireQuestion(
                id="family_history",
                text="Do you have a family history of colorectal cancer?",
                question_type="yes_no",
                weight=3.0,
                risk_multiplier={"yes": 2.0, "no": 1.0}
            ),
            QuestionnaireQuestion(
                id="personal_history",
                text="Have you ever been diagnosed with colorectal polyps or cancer?",
                question_type="yes_no",
                weight=4.0,
                risk_multiplier={"yes": 3.0, "no": 1.0}
            ),
            QuestionnaireQuestion(
                id="ibd_history",
                text="Do you have inflammatory bowel disease (IBD)?",
                question_type="yes_no",
                weight=3.5,
                risk_multiplier={"yes": 2.5, "no": 1.0}
            ),
            QuestionnaireQuestion(
                id="smoking",
                text="Do you currently smoke or have you smoked in the past?",
                question_type="multiple_choice",
                options=["never", "former", "current"],
                weight=1.5,
                risk_multiplier={"never": 1.0, "former": 1.3, "current": 1.6}
            ),
            QuestionnaireQuestion(
                id="alcohol",
                text="How often do you consume alcohol?",
                question_type="multiple_choice",
                options=["never", "occasional", "moderate", "heavy"],
                weight=1.2,
                risk_multiplier={"never": 1.0, "occasional": 1.0, "moderate": 1.2, "heavy": 1.5}
            ),
            QuestionnaireQuestion(
                id="physical_activity",
                text="How would you describe your physical activity level?",
                question_type="multiple_choice",
                options=["sedentary", "light", "moderate", "vigorous"],
                weight=1.0,
                risk_multiplier={"sedentary": 1.3, "light": 1.1, "moderate": 1.0, "vigorous": 0.8}
            ),
            QuestionnaireQuestion(
                id="diet",
                text="How often do you eat red or processed meat?",
                question_type="multiple_choice",
                options=["rarely", "1-2_times_week", "3-4_times_week", "daily"],
                weight=1.0,
                risk_multiplier={"rarely": 1.0, "1-2_times_week": 1.1, "3-4_times_week": 1.3, "daily": 1.5}
            ),
            QuestionnaireQuestion(
                id="symptoms",
                text="Have you experienced any of the following symptoms? (rectal bleeding, change in bowel habits, abdominal pain)",
                question_type="yes_no",
                weight=2.5,
                risk_multiplier={"yes": 2.0, "no": 1.0}
            )
        ]
        
    def _get_asia_pacific_questions(self) -> List[QuestionnaireQuestion]:
        """Get Asia-Pacific specific risk assessment questions."""
        # Based on Asia-Pacific Colorectal Screening score
        return [
            QuestionnaireQuestion(
                id="age",
                text="Age",
                question_type="numeric",
                weight=3.0,
                risk_multiplier={"50-69": 1.0, "70+": 1.5}
            ),
            QuestionnaireQuestion(
                id="gender",
                text="Gender",
                question_type="multiple_choice",
                options=["male", "female"],
                weight=1.0,
                risk_multiplier={"male": 1.2, "female": 1.0}
            ),
            QuestionnaireQuestion(
                id="family_history",
                text="Family history of colorectal cancer",
                question_type="yes_no",
                weight=4.0,
                risk_multiplier={"yes": 2.5, "no": 1.0}
            ),
            QuestionnaireQuestion(
                id="smoking",
                text="Smoking status",
                question_type="multiple_choice",
                options=["never", "former", "current"],
                weight=2.0,
                risk_multiplier={"never": 1.0, "former": 1.4, "current": 1.8}
            ),
            QuestionnaireQuestion(
                id="bmi",
                text="Body Mass Index category",
                question_type="multiple_choice",
                options=["normal", "overweight", "obese"],
                weight=1.5,
                risk_multiplier={"normal": 1.0, "overweight": 1.2, "obese": 1.4}
            )
        ]
        
    def _get_custom_questions(self) -> List[QuestionnaireQuestion]:
        """Get custom risk assessment questions."""
        # Placeholder for custom questionnaire
        return self._get_standard_questions()
        
    def _load_scoring_weights(self, questionnaire_type: str) -> Dict[str, float]:
        """Load scoring weights for questionnaire type."""
        if questionnaire_type == "asia_pacific":
            return {
                "age": 0.3,
                "gender": 0.1,
                "family_history": 0.4,
                "smoking": 0.15,
                "bmi": 0.05
            }
        else:
            return {
                "age": 0.25,
                "family_history": 0.3,
                "personal_history": 0.2,
                "ibd_history": 0.15,
                "smoking": 0.05,
                "alcohol": 0.02,
                "physical_activity": 0.01,
                "diet": 0.01,
                "symptoms": 0.01
            }
            
    def administer_questionnaire(self, patient: Patient) -> QuestionnaireResponse:
        """Administer questionnaire to patient and calculate risk.
        
        Args:
            patient: Patient to assess
            
        Returns:
            QuestionnaireResponse: Patient's responses and risk assessment
        """
        response = QuestionnaireResponse(patient_id=patient.id)
        
        # Simulate patient responses based on their characteristics
        for question in self.questions:
            answer = self._simulate_patient_response(patient, question)
            response.responses[question.id] = answer
            
        # Calculate risk score
        response.risk_score = self._calculate_risk_score(response.responses)
        response.risk_level = self._determine_risk_level(response.risk_score)
        response.recommendation = self._generate_recommendation(response.risk_level)
        
        return response
        
    def _simulate_patient_response(self, patient: Patient, question: QuestionnaireQuestion) -> Any:
        """Simulate patient response to a question based on their characteristics.
        
        Args:
            patient: Patient responding
            question: Question being asked
            
        Returns:
            Simulated response
        """
        if question.id == "age":
            return patient.age
            
        elif question.id == "gender":
            return "male" if patient.gender == "M" else "female"
            
        elif question.id == "family_history":
            # Based on patient's family history risk factor
            family_risk = patient.risk_factors.get('family_history', 1.0)
            return "yes" if family_risk > 1.5 else "no"
            
        elif question.id == "personal_history":
            # Check if patient has polyps or cancer
            has_polyps = len(patient.polyps) > 0
            has_cancer = len(patient.cancers) > 0
            return "yes" if (has_polyps or has_cancer) else "no"
            
        elif question.id == "ibd_history":
            # Based on IBD risk factor
            ibd_risk = patient.risk_factors.get('ibd', 1.0)
            return "yes" if ibd_risk > 1.5 else "no"
            
        elif question.id == "smoking":
            smoking_risk = patient.risk_factors.get('smoking', 1.0)
            if smoking_risk <= 1.0:
                return "never"
            elif smoking_risk <= 1.3:
                return "former"
            else:
                return "current"
                
        elif question.id == "alcohol":
            alcohol_risk = patient.risk_factors.get('alcohol', 1.0)
            if alcohol_risk <= 1.0:
                return "never"
            elif alcohol_risk <= 1.1:
                return "occasional"
            elif alcohol_risk <= 1.3:
                return "moderate"
            else:
                return "heavy"
                
        elif question.id == "physical_activity":
            activity_risk = patient.risk_factors.get('physical_activity', 1.0)
            if activity_risk >= 1.3:
                return "sedentary"
            elif activity_risk >= 1.1:
                return "light"
            elif activity_risk >= 1.0:
                return "moderate"
            else:
                return "vigorous"
                
        elif question.id == "diet":
            diet_risk = patient.risk_factors.get('diet', 1.0)
            if diet_risk <= 1.0:
                return "rarely"
            elif diet_risk <= 1.2:
                return "1-2_times_week"
            elif diet_risk <= 1.4:
                return "3-4_times_week"
            else:
                return "daily"
                
        elif question.id == "bmi":
            bmi_risk = patient.risk_factors.get('bmi', 1.0)
            if bmi_risk <= 1.0:
                return "normal"
            elif bmi_risk <= 1.3:
                return "overweight"
            else:
                return "obese"
                
        elif question.id == "symptoms":
            # Simulate symptoms based on disease status
            has_advanced_polyps = any(p.is_advanced for p in patient.polyps)
            has_cancer = len(patient.cancers) > 0
            
            if has_cancer:
                return "yes" if np.random.random() < 0.7 else "no"
            elif has_advanced_polyps:
                return "yes" if np.random.random() < 0.3 else "no"
            else:
                return "yes" if np.random.random() < 0.1 else "no"
                
        # Default response
        if question.question_type == "yes_no":
            return "no"
        elif question.question_type == "multiple_choice" and question.options:
            return question.options[0]
        else:
            return 0
            
    def _calculate_risk_score(self, responses: Dict[str, Any]) -> float:
        """Calculate overall risk score from responses.
        
        Args:
            responses: Patient responses
            
        Returns:
            Risk score
        """
        total_score = 0.0
        
        for question in self.questions:
            response = responses.get(question.id)
            if response is None:
                continue
                
            # Get risk multiplier for this response
            if question.id == "age":
                if response < 50:
                    multiplier = 0.5
                elif response < 60:
                    multiplier = 1.0
                elif response < 70:
                    multiplier = 1.5
                else:
                    multiplier = 2.0
            else:
                multiplier = question.risk_multiplier.get(str(response), 1.0)
                
            # Weight the score
            weight = self.scoring_weights.get(question.id, question.weight)
            total_score += multiplier * weight
            
        return total_score
        
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level from risk score.
        
        Args:
            risk_score: Calculated risk score
            
        Returns:
            Risk level
        """
        if risk_score < 1.5:
            return RiskLevel.LOW
        elif risk_score < 2.5:
            return RiskLevel.MODERATE
        elif risk_score < 4.0:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH
            
    def _generate_recommendation(self, risk_level: RiskLevel) -> str:
        """Generate screening recommendation based on risk level.
        
        Args:
            risk_level: Patient's risk level
            
        Returns:
            Screening recommendation
        """
        recommendations = {
            RiskLevel.LOW: "Continue routine screening as per guidelines",
            RiskLevel.MODERATE: "Consider FIT testing annually or colonoscopy every 10 years",
            RiskLevel.HIGH: "Recommend colonoscopy every 5-10 years",
            RiskLevel.VERY_HIGH: "Recommend immediate colonoscopy and genetic counseling"
        }
        
        return recommendations.get(risk_level, "Consult with healthcare provider")
        
    def get_screening_recommendation(self, patient: Patient) -> Tuple[str, float]:
        """Get screening recommendation for patient.
        
        Args:
            patient: Patient to assess
            
        Returns:
            Tuple of (recommendation, risk_score)
        """
        response = self.administer_questionnaire(patient)
        return response.recommendation, response.risk_score
