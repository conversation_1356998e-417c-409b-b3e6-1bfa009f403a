# CMOST项目概述

## 项目目的
CMOST (Colorectal Microsimulation Outcomes Screening Tool) 是一个综合性的微观仿真框架，用于建模结直肠癌进展、筛查策略和健康经济学结果。这是原MATLAB版本的完整Python重新实现，具有增强功能、改进性能和现代软件工程实践。

## 核心功能
- **自然史建模**: 详细模拟腺瘤-癌变序列
- **筛查策略**: 评估各种筛查方式（结肠镜检查、FIT等）
- **健康经济学**: 计算成本、QALYs和成本效益指标
- **人群建模**: 模拟不同风险特征的多样化人群
- **校准工具**: 针对流行病学数据的自动校准
- **可视化**: 全面的结果可视化和报告
- **集群计算**: 在计算集群间分布仿真
- **用户界面**: GUI和命令行界面

## 技术栈
- **语言**: Python 3.8+
- **核心依赖**: numpy, pandas, scipy, matplotlib, seaborn
- **机器学习**: scikit-learn, torch (可选)
- **数据处理**: h5py, pyyaml, statsmodels
- **并行计算**: joblib, dask (可选)
- **可视化**: plotly, kaleido (可选)
- **测试**: pytest, pytest-cov
- **代码质量**: flake8, black, mypy, bandit

## 项目结构
- `cmost/core/`: 核心仿真逻辑
- `cmost/models/`: 数据模型（患者、息肉、癌症等）
- `cmost/calibration/`: 校准功能
- `cmost/ml/`: 机器学习自适应校准
- `cmost/screening/`: 筛查策略管理
- `cmost/economics/`: 健康经济学评价
- `cmost/utils/`: 工具函数
- `cmost/config/`: 配置管理
- `cmost/ui/`: 用户界面
- `cmost/cluster/`: 集群计算
- `tests/`: 测试套件（单元测试、集成测试、性能测试）