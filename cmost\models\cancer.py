"""
Cancer model for CMOST simulation.
"""
from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import numpy as np
from datetime import datetime

from .molecular_features import MolecularProfile, MolecularProfileGenerator


@dataclass
class Cancer:
    """Represents a colorectal cancer in the simulation."""
    
    id: int
    location: int  # Anatomical location (1-6 representing different colon segments)
    stage: int     # Stage (7-10, corresponding to CancerStage enum)
    patient_id: int
    patient_gender: str
    source_polyp_id: Optional[int] = None  # ID of polyp that progressed to cancer, if applicable
    
    # Tracking information
    year_created: int = field(default_factory=lambda: datetime.now().year)
    discovery_year: Optional[int] = None
    discovery_method: Optional[str] = None
    
    # Treatment information
    treatment_applied: bool = False
    treatment_year: Optional[int] = None
    treatment_type: Optional[str] = None
    
    # Survival information
    survival_months: Optional[int] = None  # Estimated survival in months if untreated

    # Molecular profile
    molecular_profile: Optional[MolecularProfile] = None

    # Additional properties
    properties: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Initialize molecular profile if not provided."""
        if self.molecular_profile is None:
            generator = MolecularProfileGenerator()
            self.molecular_profile = generator.generate_profile(
                lesion_type="cancer",
                stage=self.stage,
                location=self.location,
                patient_age=65  # Default age, should be updated with actual patient age
            )
    
    @classmethod
    def from_polyp(cls, polyp) -> 'Cancer':
        """Create a cancer instance from a polyp that has progressed to cancer.

        Args:
            polyp: The polyp that has progressed to cancer

        Returns:
            Cancer: New cancer instance
        """
        cancer = cls(
            id=np.random.randint(10000, 99999),  # Generate a new ID
            location=polyp.location,
            stage=7,  # Start at stage 7 (Stage I)
            patient_id=polyp.patient_id,
            patient_gender=polyp.patient_gender,
            source_polyp_id=polyp.id,
            year_created=datetime.now().year
        )

        # Inherit molecular profile from polyp
        if hasattr(polyp, 'molecular_profile') and polyp.molecular_profile:
            cancer.molecular_profile = polyp.molecular_profile

        return cancer
    
    def progress(self, years: int, progression_model=None) -> None:
        """Progress the cancer by a number of years.
        
        Args:
            years: Number of years to progress
            progression_model: Optional model that defines progression rates
        """
        # If treatment has been applied, slower progression
        progression_modifier = 0.3 if self.treatment_applied else 1.0

        # Apply molecular progression modifier
        if self.molecular_profile:
            molecular_modifier = self.molecular_profile.get_progression_modifier()
            progression_modifier *= molecular_modifier
        
        for _ in range(years):
            # Skip progression if already at stage 10 (metastatic)
            if self.stage >= 10:
                break
                
            # Determine if cancer progresses this year
            if progression_model:
                new_stage = progression_model.progress_cancer(
                    self.stage, 
                    self.treatment_applied
                )
                self.stage = new_stage
            else:
                # Simple progression model if no model provided
                # Base annual progression probabilities
                progression_probs = {
                    7: 0.4,  # Stage I to II
                    8: 0.5,  # Stage II to III
                    9: 0.6   # Stage III to IV
                }
                
                # Apply treatment modifier
                prob = progression_probs.get(self.stage, 0) * progression_modifier
                
                # Check if cancer progresses
                if np.random.random() < prob:
                    self.stage += 1
    
    def apply_treatment(self, year: int, treatment_type: str) -> None:
        """Apply treatment to the cancer.
        
        Args:
            year: Year when treatment is applied
            treatment_type: Type of treatment (e.g., 'surgery', 'chemo', 'radiation')
        """
        self.treatment_applied = True
        self.treatment_year = year
        self.treatment_type = treatment_type
        
        # Treatment may cause regression in early stages
        if self.stage == 7 and treatment_type in ['surgery', 'surgery+chemo']:
            # 90% chance of complete removal for Stage I with surgery
            self.properties['removed'] = np.random.random() < 0.9
        
    def estimate_survival(self, mortality_rates: Dict[int, float] = None) -> int:
        """Estimate survival time in months based on cancer stage.
        
        Args:
            mortality_rates: Optional dictionary of mortality rates by stage
            
        Returns:
            int: Estimated survival time in months
        """
        if not mortality_rates:
            # Default 5-year survival rates by stage (%)
            five_year_survival = {
                7: 0.92,  # Stage I: 92%
                8: 0.87,  # Stage II: 87%
                9: 0.71,  # Stage III: 71%
                10: 0.14  # Stage IV: 14%
            }
            
            # Convert to mortality rates
            mortality_rates = {k: 1 - v for k, v in five_year_survival.items()}
        
        # Get mortality rate for current stage
        mortality = mortality_rates.get(self.stage, 0.5)
        
        # Adjust for treatment
        if self.treatment_applied:
            mortality *= 0.7  # Treatment reduces mortality by 30%
        
        # Convert to median survival in months
        # Using exponential survival model: median = -ln(0.5)/rate
        monthly_rate = -np.log(1 - mortality) / 60  # Convert 5-year to monthly rate
        median_survival = -np.log(0.5) / monthly_rate
        
        # Add some variability
        self.survival_months = int(np.random.normal(median_survival, median_survival * 0.2))
        return self.survival_months
    
    @property
    def is_metastatic(self) -> bool:
        """Check if cancer is metastatic (Stage IV).
        
        Returns:
            bool: True if cancer is metastatic, False otherwise
        """
        return self.stage >= 10
    
    @property
    def is_advanced(self) -> bool:
        """Check if cancer is advanced (Stage III or IV).
        
        Returns:
            bool: True if cancer is advanced, False otherwise
        """
        return self.stage >= 9
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert cancer to dictionary for serialization.
        
        Returns:
            Dict[str, Any]: Dictionary representation of cancer
        """
        return {
            'id': self.id,
            'location': self.location,
            'stage': self.stage,
            'patient_id': self.patient_id,
            'patient_gender': self.patient_gender,
            'source_polyp_id': self.source_polyp_id,
            'year_created': self.year_created,
            'discovery_year': self.discovery_year,
            'discovery_method': self.discovery_method,
            'treatment_applied': self.treatment_applied,
            'treatment_year': self.treatment_year,
            'treatment_type': self.treatment_type,
            'survival_months': self.survival_months,
            'properties': self.properties
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Cancer':
        """Create cancer from dictionary.
        
        Args:
            data: Dictionary containing cancer data
            
        Returns:
            Cancer: New cancer instance
        """
        return cls(
            id=data['id'],
            location=data['location'],
            stage=data['stage'],
            patient_id=data['patient_id'],
            patient_gender=data['patient_gender'],
            source_polyp_id=data.get('source_polyp_id'),
            year_created=data.get('year_created', datetime.now().year),
            discovery_year=data.get('discovery_year'),
            discovery_method=data.get('discovery_method'),
            treatment_applied=data.get('treatment_applied', False),
            treatment_year=data.get('treatment_year'),
            treatment_type=data.get('treatment_type'),
            survival_months=data.get('survival_months'),
            properties=data.get('properties', {})
        )