# 建议的开发命令

## 测试命令
```bash
# 运行所有测试
pytest

# 运行单元测试
pytest -m unit

# 运行集成测试
pytest -m integration

# 运行性能测试
pytest -m performance

# 运行测试并生成覆盖率报告
pytest --cov=cmost --cov-report=html --cov-report=term-missing

# 运行特定测试文件
pytest tests/unit/test_simulation.py

# 并行运行测试
pytest -n auto
```

## 代码质量检查
```bash
# 代码格式化
black cmost/ tests/

# 代码风格检查
flake8 cmost/ tests/

# 类型检查
mypy cmost/

# 安全检查
bandit -r cmost/

# 导入排序
isort cmost/ tests/
```

## 应用程序运行
```bash
# 命令行界面
cmost --help

# 运行基本仿真
cmost simulate --patients 10000 --output results.json

# 运行筛查策略比较
cmost compare --strategies colonoscopy,fit,sigmoidoscopy --output comparison.json

# 校准模型参数
cmost calibrate --target-data seer_data.csv --output calibrated_params.json

# 分析结果
cmost analyze --input results.json --output analysis.json
```

## 开发环境设置
```bash
# 安装开发依赖
pip install -e .[dev]

# 安装所有可选依赖
pip install -e .[all]

# 安装pre-commit钩子
pre-commit install
```

## Windows系统工具命令
```cmd
# 文件操作
dir          # 列出目录内容
cd           # 改变目录
copy         # 复制文件
del          # 删除文件
mkdir        # 创建目录

# 搜索
findstr      # 在文件中搜索文本
where        # 查找可执行文件位置

# Git操作
git status
git add .
git commit -m "message"
git push
git pull
```