"""
Configuration module for CMOST application.

This module provides configuration management for the CMOST application,
including default parameters, settings loading/saving, and configuration access.
"""

from .settings import (
    Settings,
    settings,
    initialize_settings,
    get_setting,
    set_setting,
    save_settings,
    load_settings
)

from .defaults import (
    DEFAULT_SETTINGS,
    SIMULATION_SETTINGS,
    M<PERSON>EL_PARAMETERS,
    SCREENING_SETTINGS,
    UI_SETTINGS,
    EXCEL_SETTINGS,
    POPULATION_PARAMETERS,
    RISK_FACTORS,
    BENCHMARKS,
    get_all_defaults
)

__all__ = [
    # Settings classes and functions
    'Settings',
    'settings',
    'initialize_settings',
    'get_setting',
    'set_setting',
    'save_settings',
    'load_settings',

    # Default parameter constants
    'DEFAULT_SETTINGS',
    'SIMULATION_SETTINGS',
    'MODEL_PARAMETERS',
    'SCREENING_SETTINGS',
    'UI_SETTINGS',
    'EXCEL_SETTINGS',
    'POPULATION_PARAMETERS',
    'RISK_FACTORS',
    'BENCHMARKS',
    'get_all_defaults'
]

# Initialize settings with defaults
def init_config(config_path=None):
    """
    Initialize configuration with optional config file.
    
    Args:
        config_path: Path to configuration file (optional)
    
    Returns:
        The initialized settings object
    """
    initialize_settings(config_path)
    return settings