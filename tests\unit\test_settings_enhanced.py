"""
Enhanced unit tests for settings module.
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, mock_open

from cmost.config.settings import Settings


class TestSettingsEnhanced:
    """Enhanced test cases for Settings class."""
    
    def test_initialization_with_path(self):
        """Test settings initialization with config path."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump({'test_key': 'test_value'}, f)
            temp_path = f.name
        
        try:
            settings = Settings(temp_path)
            assert settings.config_path == temp_path
            assert settings.get('test_key') == 'test_value'
        finally:
            os.unlink(temp_path)
    
    def test_initialization_without_path(self):
        """Test settings initialization without config path."""
        settings = Settings()
        assert settings.config_path is None
        assert isinstance(settings.settings, dict)
    
    def test_load_json_settings(self):
        """Test loading JSON settings file."""
        test_data = {
            'simulation': {'years': 50, 'patients': 10000},
            'model': {'name': 'CMOST', 'version': '2.0'}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_path = f.name
        
        try:
            settings = Settings()
            result = settings.load_settings(temp_path)
            
            assert result is True
            assert settings.get('simulation.years') == 50
            assert settings.get('model.name') == 'CMOST'
        finally:
            os.unlink(temp_path)
    
    def test_load_yaml_settings(self):
        """Test loading YAML settings file."""
        yaml_content = '''
        simulation:
          years: 50
          patients: 10000
        model:
          name: CMOST
          version: "2.0"
        '''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(yaml_content)
            temp_path = f.name
        
        try:
            settings = Settings()
            with patch('yaml.safe_load') as mock_yaml:
                mock_yaml.return_value = {
                    'simulation': {'years': 50, 'patients': 10000},
                    'model': {'name': 'CMOST', 'version': '2.0'}
                }
                result = settings.load_settings(temp_path)
                
                assert result is True
                assert settings.get('simulation.years') == 50
        finally:
            os.unlink(temp_path)
    
    def test_load_settings_file_not_found(self):
        """Test loading non-existent settings file."""
        settings = Settings()
        result = settings.load_settings('non_existent_file.json')
        assert result is False
    
    def test_load_settings_permission_error(self):
        """Test loading settings with permission error."""
        settings = Settings()
        
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = settings.load_settings('test.json')
            assert result is False
    
    def test_load_settings_invalid_json(self):
        """Test loading invalid JSON file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"invalid": json}')  # Invalid JSON
            temp_path = f.name
        
        try:
            settings = Settings()
            result = settings.load_settings(temp_path)
            assert result is False
        finally:
            os.unlink(temp_path)
    
    def test_load_settings_encoding_error(self):
        """Test loading file with encoding error."""
        settings = Settings()
        
        with patch('builtins.open', side_effect=UnicodeDecodeError('utf-8', b'', 0, 1, 'invalid')):
            result = settings.load_settings('test.json')
            assert result is False
    
    def test_load_settings_unsupported_format(self):
        """Test loading unsupported file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write('some text')
            temp_path = f.name
        
        try:
            settings = Settings()
            result = settings.load_settings(temp_path)
            assert result is False
        finally:
            os.unlink(temp_path)
    
    def test_save_settings_json(self):
        """Test saving settings to JSON file."""
        settings = Settings()
        settings.set('test.key', 'test_value')
        settings.set('number', 42)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            result = settings.save_settings(temp_path)
            assert result is True
            
            # Verify saved content
            with open(temp_path, 'r') as f:
                saved_data = json.load(f)
                assert saved_data['test']['key'] == 'test_value'
                assert saved_data['number'] == 42
        finally:
            os.unlink(temp_path)
    
    def test_save_settings_yaml(self):
        """Test saving settings to YAML file."""
        settings = Settings()
        settings.set('test.key', 'test_value')
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_path = f.name
        
        try:
            with patch('yaml.dump') as mock_yaml:
                result = settings.save_settings(temp_path)
                assert result is True
                mock_yaml.assert_called_once()
        finally:
            os.unlink(temp_path)
    
    def test_save_settings_permission_error(self):
        """Test saving settings with permission error."""
        settings = Settings()
        
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = settings.save_settings('test.json')
            assert result is False
    
    def test_get_nested_setting_with_default(self):
        """Test getting nested setting with default value."""
        settings = Settings()
        settings.set('level1.level2.level3', 'deep_value')
        
        # Test existing nested setting
        assert settings.get('level1.level2.level3') == 'deep_value'
        
        # Test non-existing nested setting with default
        assert settings.get('level1.level2.nonexistent', 'default') == 'default'
        
        # Test partially existing path
        assert settings.get('level1.nonexistent.level3', 'default') == 'default'
    
    def test_set_nested_setting_complex(self):
        """Test setting complex nested settings."""
        settings = Settings()
        
        # Set multiple nested values
        settings.set('simulation.parameters.population.size', 10000)
        settings.set('simulation.parameters.population.demographics.age_range', [18, 85])
        settings.set('simulation.parameters.screening.methods', ['colonoscopy', 'FIT'])
        
        # Verify structure
        assert settings.get('simulation.parameters.population.size') == 10000
        assert settings.get('simulation.parameters.population.demographics.age_range') == [18, 85]
        assert settings.get('simulation.parameters.screening.methods') == ['colonoscopy', 'FIT']
    
    def test_has_setting_via_get(self):
        """Test checking if setting exists using get method."""
        settings = Settings()
        settings.set('existing.setting', 'value')

        # Test existing setting
        assert settings.get('existing.setting') == 'value'
        assert settings.get('existing.setting') is not None

        # Test non-existing setting
        assert settings.get('nonexistent.setting') is None
        assert settings.get('nonexistent.setting', 'default') == 'default'

    def test_setting_operations(self):
        """Test basic setting operations."""
        settings = Settings()
        settings.set('test.key', 'test_value')

        # Test getting existing setting
        assert settings.get('test.key') == 'test_value'

        # Test overwriting setting
        settings.set('test.key', 'new_value')
        assert settings.get('test.key') == 'new_value'

        # Test nested setting access
        settings.set('level1.level2.key', 'nested_value')
        assert settings.get('level1.level2.key') == 'nested_value'
    
    def test_get_all_settings(self):
        """Test getting all settings via direct access."""
        settings = Settings()
        settings.set('key1', 'value1')
        settings.set('nested.key2', 'value2')

        # Access settings directly
        all_settings = settings.settings
        assert isinstance(all_settings, dict)
        assert all_settings['key1'] == 'value1'
        assert all_settings['nested']['key2'] == 'value2'

    def test_settings_structure(self):
        """Test settings internal structure."""
        settings = Settings()
        settings.set('key1', 'value1')
        settings.set('key2', 'value2')

        # Check internal structure
        assert len(settings.settings) >= 2
        assert settings.get('key1') == 'value1'
        assert settings.get('key2') == 'value2'
    
    def test_settings_manual_merge(self):
        """Test manually merging settings by setting individual values."""
        settings1 = Settings()
        settings1.set('key1', 'value1')
        settings1.set('nested.key2', 'value2')

        settings2 = Settings()
        settings2.set('key3', 'value3')
        settings2.set('nested.key4', 'value4')

        # Manual merge by setting individual values
        settings1.set('key3', settings2.get('key3'))
        settings1.set('nested.key4', settings2.get('nested.key4'))

        assert settings1.get('key1') == 'value1'
        assert settings1.get('key3') == 'value3'
        assert settings1.get('nested.key2') == 'value2'
        assert settings1.get('nested.key4') == 'value4'

    def test_settings_validation_basic(self):
        """Test basic settings validation by checking values."""
        settings = Settings()
        settings.set('Number_patients', 10000)
        settings.set('Simulation.StartYear', 2000)
        settings.set('Simulation.EndYear', 2050)

        # Manual validation
        num_patients = settings.get('Number_patients')
        start_year = settings.get('Simulation.StartYear')
        end_year = settings.get('Simulation.EndYear')

        assert isinstance(num_patients, int)
        assert num_patients > 0
        assert isinstance(start_year, int)
        assert isinstance(end_year, int)
        assert end_year > start_year

    def test_settings_as_dict(self):
        """Test accessing settings as dictionary."""
        settings = Settings()
        settings.set('key1', 'value1')
        settings.set('nested.key2', {'subkey': 'subvalue'})

        # Access internal dictionary
        result_dict = settings.settings

        assert isinstance(result_dict, dict)
        assert result_dict['key1'] == 'value1'
        assert result_dict['nested']['key2']['subkey'] == 'subvalue'

    def test_settings_initialization_from_data(self):
        """Test initializing settings with data."""
        settings = Settings()

        # Manually set nested data
        settings.set('key1', 'value1')
        settings.set('nested.key2', 'value2')
        settings.set('nested.deep.key3', 'value3')

        assert settings.get('key1') == 'value1'
        assert settings.get('nested.key2') == 'value2'
        assert settings.get('nested.deep.key3') == 'value3'

    def test_settings_path_handling(self):
        """Test settings path handling."""
        settings = Settings()
        settings.set('path', 'test/data')

        # Basic path handling
        path_value = settings.get('path')
        assert path_value == 'test/data'

        # Test with environment-like variables (manual substitution)
        settings.set('base_path', '/home/<USER>')
        settings.set('data_path', settings.get('base_path') + '/data')
        assert settings.get('data_path') == '/home/<USER>/data'