"""
Visualization utilities for CMOST.
"""
from typing import Dict, List, Optional, Tuple, Any, Union
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.figure import Figure
from matplotlib.axes import Axes
import os
from pandas.plotting import bootstrap_plot

def set_plotting_style() -> None:
    """Set default plotting style."""
    sns.set_style("whitegrid")
    plt.rcParams['figure.figsize'] = (10, 6)
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12
    plt.rcParams['figure.titlesize'] = 18

def plot_age_distribution(ages: List[int], title: str = "Age Distribution", 
                          save_path: Optional[str] = None) -> <PERSON><PERSON>[Figure, Axes]:
    """Plot age distribution histogram.
    
    Args:
        ages: List of ages
        title: Plot title
        save_path: Path to save figure (optional)
        
    Returns:
        Tuple of (Figure, Axes)
    """
    set_plotting_style()
    
    fig, ax = plt.subplots()
    sns.histplot(ages, bins=range(0, 101, 5), kde=True, ax=ax)
    
    ax.set_title(title)
    ax.set_xlabel("Age (years)")
    ax.set_ylabel("Count")
    ax.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig, ax

def plot_stage_distribution(stage_counts: Dict[str, int], 
                           title: str = "Disease Stage Distribution",
                           save_path: Optional[str] = None) -> Tuple[Figure, Axes]:
    """Plot disease stage distribution.
    
    Args:
        stage_counts: Dictionary of stage counts
        title: Plot title
        save_path: Path to save figure (optional)
        
    Returns:
        Tuple of (Figure, Axes)
    """
    set_plotting_style()
    
    # Convert to DataFrame for plotting
    stages = list(stage_counts.keys())
    counts = list(stage_counts.values())
    
    df = pd.DataFrame({
        'Stage': stages,
        'Count': counts
    })
    
    # Sort by stage
    df['Stage_Num'] = df['Stage'].str.extract(r'(\d+)').astype(int)
    df = df.sort_values('Stage_Num')
    
    fig, ax = plt.subplots()
    sns.barplot(x='Stage', y='Count', data=df, ax=ax)
    
    ax.set_title(title)
    ax.set_xlabel("Disease Stage")
    ax.set_ylabel("Count")
    ax.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig, ax

def plot_incidence_by_age(age_specific_rates: Dict[str, Dict[str, float]],
                         title: str = "Cancer Incidence by Age Group",
                         save_path: Optional[str] = None) -> Tuple[Figure, Axes]:
    """Plot cancer incidence by age group.
    
    Args:
        age_specific_rates: Dictionary of age-specific rates
        title: Plot title
        save_path: Path to save figure (optional)
        
    Returns:
        Tuple of (Figure, Axes)
    """
    set_plotting_style()
    
    # Convert to DataFrame for plotting
    age_groups = list(age_specific_rates.keys())
    incidence_rates = [data['incidence'] for data in age_specific_rates.values()]
    
    df = pd.DataFrame({
        'Age Group': age_groups,
        'Incidence Rate': incidence_rates
    })
    
    fig, ax = plt.subplots()
    sns.barplot(x='Age Group', y='Incidence Rate', data=df, ax=ax)
    
    ax.set_title(title)
    ax.set_xlabel("Age Group")
    ax.set_ylabel("Incidence Rate (per 100,000)")
    ax.grid(True, alpha=0.3)
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig, ax

def plot_comparison_with_benchmarks(simulation_results: Dict[str, float],
                                   benchmark_results: Dict[str, float],
                                   metric: str = "Incidence",
                                   title: str = "Comparison with Benchmarks",
                                   save_path: Optional[str] = None) -> Tuple[Figure, Axes]:
    """Plot comparison between simulation results and benchmarks.
    
    Args:
        simulation_results: Dictionary of simulation results
        benchmark_results: Dictionary of benchmark results
        metric: Metric to compare
        title: Plot title
        save_path: Path to save figure (optional)
        
    Returns:
        Tuple of (Figure, Axes)
    """
    set_plotting_style()
    
    # Convert to DataFrame for plotting
    categories = list(simulation_results.keys())
    sim_values = list(simulation_results.values())
    benchmark_values = [benchmark_results.get(cat, 0) for cat in categories]
    
    df = pd.DataFrame({
        'Category': categories,
        'Simulation': sim_values,
        'Benchmark': benchmark_values
    })
    
    # Reshape for grouped bar plot
    df_melted = pd.melt(df, id_vars=['Category'], 
                        value_vars=['Simulation', 'Benchmark'],
                        var_name='Source', value_name=metric)
    
    fig, ax = plt.subplots()
    sns.barplot(x='Category', y=metric, hue='Source', data=df_melted, ax=ax)
    
    ax.set_title(title)
    ax.set_xlabel("Category")
    ax.set_ylabel(f"{metric} Rate (per 100,000)")
    ax.grid(True, alpha=0.3)
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig, ax

def plot_progression_curves(progression_model, ages: List[int] = None,
                           title: str = "Disease Progression Curves",
                           save_path: Optional[str] = None) -> Tuple[Figure, Axes]:
    """Plot disease progression curves.
    
    Args:
        progression_model: ProgressionModel instance
        ages: List of ages to plot (default: 20-100)
        title: Plot title
        save_path: Path to save figure (optional)
        
    Returns:
        Tuple of (Figure, Axes)
    """
    set_plotting_style()
    
    if ages is None:
        ages = list(range(20, 101))
    
    # Get progression rates for different stages
    from ..core.progression import PolypStage, CancerStage
    
    progression_rates = {}
    for stage in list(PolypStage) + list(CancerStage):
        rates = []
        for age in ages:
            # Use a reference patient (male, average risk, middle colon)
            rate = progression_model.get_progression_probability(
                stage=stage, 
                location=3,  # Middle colon
                gender='M',
                individual_risk=1.0,
                age=age
            )
            rates.append(rate)
        progression_rates[stage.name] = rates
    
    # Plot
    fig, ax = plt.subplots()
    
    for stage, rates in progression_rates.items():
        ax.plot(ages, rates, label=stage)
    
    ax.set_title(title)
    ax.set_xlabel("Age (years)")
    ax.set_ylabel("Annual Progression Probability")
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig, ax

def plot_bootstrap_analysis(data: pd.Series, 
                           title: str = "Bootstrap Analysis",
                           size: int = 50,
                           samples: int = 500,
                           save_path: Optional[str] = None) -> Figure:
    """Plot bootstrap analysis of data.
    
    Args:
        data: Series of data to analyze
        title: Plot title
        size: Bootstrap sample size
        samples: Number of bootstrap samples
        save_path: Path to save figure (optional)
        
    Returns:
        Figure object
    """
    set_plotting_style()
    
    # Create bootstrap plot
    fig = bootstrap_plot(data, size=size, samples=samples)
    
    # Add title
    fig.suptitle(title, fontsize=16)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig

def plot_calibration_results(calibration_results: Dict[str, Any],
                            benchmark_data: Dict[str, Any],
                            title: str = "Calibration Results",
                            save_path: Optional[str] = None) -> List[Figure]:
    """Plot calibration results compared to benchmarks.
    
    Args:
        calibration_results: Dictionary of calibration results
        benchmark_data: Dictionary of benchmark data
        title: Plot title
        save_path: Path to save figure (optional)
        
    Returns:
        List of Figure objects
    """
    set_plotting_style()
    figures = []
    
    # Plot incidence comparison
    if 'incidence' in calibration_results and 'incidence' in benchmark_data:
        fig, ax = plt.subplots()
        
        # Extract age groups and rates
        age_groups = list(calibration_results['incidence'].keys())
        sim_rates = [calibration_results['incidence'][age] for age in age_groups]
        bench_rates = [benchmark_data['incidence'][age] for age in age_groups]
        
        # Create DataFrame
        df = pd.DataFrame({
            'Age Group': age_groups,
            'Simulation': sim_rates,
            'Benchmark': bench_rates
        })
        
        # Reshape for grouped bar plot
        df_melted = pd.melt(df, id_vars=['Age Group'], 
                            value_vars=['Simulation', 'Benchmark'],
                            var_name='Source', value_name='Incidence Rate')
        
        # Plot
        sns.barplot(x='Age Group', y='Incidence Rate', hue='Source', data=df_melted, ax=ax)
        
        ax.set_title(f"{title} - Incidence Comparison")
        ax.set_xlabel("Age Group")
        ax.set_ylabel("Incidence Rate (per 100,000)")
        ax.grid(True, alpha=0.3)
        
        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45)
        
        if save_path:
            plt.savefig(f"{save_path}_incidence.png", dpi=300, bbox_inches='tight')
        
        figures.append(fig)
    
    # Plot mortality comparison
    if 'mortality' in calibration_results and 'mortality' in benchmark_data:
        fig, ax = plt.subplots()
        
        # Extract age groups and rates
        age_groups = list(calibration_results['mortality'].keys())
        sim_rates = [calibration_results['mortality'][age] for age in age_groups]
        bench_rates = [benchmark_data['mortality'][age] for age in age_groups]
        
        # Create DataFrame
        df = pd.DataFrame({
            'Age Group': age_groups,
            'Simulation': sim_rates,
            'Benchmark': bench_rates
        })
        
        # Reshape for grouped bar plot
        df_melted = pd.melt(df, id_vars=['Age Group'], 
                            value_vars=['Simulation', 'Benchmark'],
                            var_name='Source', value_name='Mortality Rate')
        
        # Plot
        sns.barplot(x='Age Group', y='Mortality Rate', hue='Source', data=df_melted, ax=ax)
        
        ax.set_title(f"{title} - Mortality Comparison")
        ax.set_xlabel("Age Group")
        ax.set_ylabel("Mortality Rate (per 100,000)")
        ax.grid(True, alpha=0.3)
        
        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45)
        
        if save_path:
            plt.savefig(f"{save_path}_mortality.png", dpi=300, bbox_inches='tight')
        
        figures.append(fig)
    
    # Plot stage distribution comparison
    if 'stage_distribution' in calibration_results and 'stage_distribution' in benchmark_data:
        fig, ax = plt.subplots()
        
        # Extract stages and percentages
        stages = list(calibration_results['stage_distribution'].keys())
        sim_pcts = [calibration_results['stage_distribution'][stage] for stage in stages]
        bench_pcts = [benchmark_data['stage_distribution'][stage] for stage in stages]
        
        # Create DataFrame
        df = pd.DataFrame({
            'Stage': stages,
            'Simulation': sim_pcts,
            'Benchmark': bench_pcts
        })
        
        # Reshape for grouped bar plot
        df_melted = pd.melt(df, id_vars=['Stage'], 
                            value_vars=['Simulation', 'Benchmark'],
                            var_name='Source', value_name='Percentage')
        
        # Plot
        sns.barplot(x='Stage', y='Percentage', hue='Source', data=df_melted, ax=ax)
        
        ax.set_title(f"{title} - Stage Distribution Comparison")
        ax.set_xlabel("Cancer Stage")
        ax.set_ylabel("Percentage")
        ax.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(f"{save_path}_stages.png", dpi=300, bbox_inches='tight')
        
        figures.append(fig)
    
    return figures


def plot_results(results: Dict[str, Any], save_path: Optional[str] = None) -> List[Tuple[Figure, Axes]]:
    """Plot comprehensive simulation results.

    Args:
        results: Dictionary containing simulation results
        save_path: Optional path to save plots

    Returns:
        List of (figure, axes) tuples
    """
    figures = []

    # Plot 1: Summary statistics
    if 'summary' in results:
        summary = results['summary']
        fig, ax = plt.subplots(figsize=(12, 8))

        # Create bar plot of key metrics
        metrics = ['cancer_incidence', 'cancer_mortality', 'polyps_detected', 'screening_tests']
        values = [summary.get(metric, 0) for metric in metrics]
        labels = ['Cancer Incidence', 'Cancer Mortality', 'Polyps Detected', 'Screening Tests']

        bars = ax.bar(labels, values, color=['red', 'darkred', 'blue', 'green'])
        ax.set_title('Simulation Summary Statistics')
        ax.set_ylabel('Count')

        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:,.0f}', ha='center', va='bottom')

        plt.xticks(rotation=45)
        plt.tight_layout()

        if save_path:
            plt.savefig(f"{save_path}_summary.png", dpi=300, bbox_inches='tight')

        figures.append((fig, ax))

    # Plot 2: Age distribution if patient data available
    if 'patients' in results:
        patients_data = results['patients']
        if patients_data:
            ages = [p.get('final_age', 0) for p in patients_data]
            fig, ax = plot_age_distribution(ages, "Patient Age Distribution", save_path)
            figures.append((fig, ax))

    return figures


def plot_incidence_curves(patients: List, title: str = "Cancer Incidence Curves",
                         save_path: Optional[str] = None) -> Tuple[Figure, Axes]:
    """Plot cancer incidence curves by age.

    Args:
        patients: List of patient objects
        title: Plot title
        save_path: Optional path to save plot

    Returns:
        Tuple of (figure, axes)
    """
    set_plotting_style()

    fig, ax = plt.subplots(figsize=(12, 8))

    # Calculate incidence by age
    age_groups = list(range(20, 101, 5))
    incidence_rates = []

    for age in age_groups:
        age_patients = [p for p in patients if age <= p.age < age + 5]
        cancer_patients = [p for p in age_patients if len(p.cancers) > 0]

        rate = len(cancer_patients) / len(age_patients) * 100 if age_patients else 0
        incidence_rates.append(rate)

    ax.plot(age_groups, incidence_rates, marker='o', linewidth=2, markersize=6)
    ax.set_xlabel('Age Group')
    ax.set_ylabel('Cancer Incidence Rate (%)')
    ax.set_title(title)
    ax.grid(True, alpha=0.3)

    if save_path:
        plt.savefig(f"{save_path}_incidence.png", dpi=300, bbox_inches='tight')

    return fig, ax


def plot_survival_curves(patients: List, title: str = "Survival Curves",
                        save_path: Optional[str] = None) -> Tuple[Figure, Axes]:
    """Plot survival curves for cancer patients.

    Args:
        patients: List of patient objects
        title: Plot title
        save_path: Optional path to save plot

    Returns:
        Tuple of (figure, axes)
    """
    set_plotting_style()

    fig, ax = plt.subplots(figsize=(12, 8))

    # Get cancer patients
    cancer_patients = [p for p in patients if len(p.cancers) > 0]

    if not cancer_patients:
        ax.text(0.5, 0.5, 'No cancer patients found', ha='center', va='center', transform=ax.transAxes)
        ax.set_title(title)
        return fig, ax

    # Simple survival analysis by stage
    stages = [1, 2, 3, 4]  # Cancer stages
    colors = ['green', 'yellow', 'orange', 'red']

    for stage, color in zip(stages, colors):
        stage_patients = [p for p in cancer_patients if any(c.stage == stage + 6 for c in p.cancers)]

        if stage_patients:
            # Calculate survival times (simplified)
            survival_times = []
            for p in stage_patients:
                if p.death_cause == 'cancer' and p.death_year:
                    # Find when cancer was diagnosed (simplified)
                    cancer_year = min(c.year_created for c in p.cancers)
                    survival_time = p.death_year - cancer_year
                    survival_times.append(max(0, survival_time))
                else:
                    survival_times.append(10)  # Assume 10 years if alive

            # Plot survival curve (simplified Kaplan-Meier style)
            survival_times.sort()
            n = len(survival_times)
            survival_prob = [(n - i) / n for i in range(n)]

            ax.step(survival_times, survival_prob, where='post',
                   label=f'Stage {stage}', color=color, linewidth=2)

    ax.set_xlabel('Years')
    ax.set_ylabel('Survival Probability')
    ax.set_title(title)
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 1)

    if save_path:
        plt.savefig(f"{save_path}_survival.png", dpi=300, bbox_inches='tight')

    return fig, ax


def plot_cost_effectiveness(cost_data: Dict[str, float], title: str = "Cost-Effectiveness Analysis",
                           save_path: Optional[str] = None) -> Tuple[Figure, Axes]:
    """Plot cost-effectiveness analysis.

    Args:
        cost_data: Dictionary containing cost and effectiveness data
        title: Plot title
        save_path: Optional path to save plot

    Returns:
        Tuple of (figure, axes)
    """
    set_plotting_style()

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Plot 1: Cost breakdown
    costs = ['screening_costs', 'treatment_costs']
    cost_values = [cost_data.get(cost, 0) for cost in costs]
    cost_labels = ['Screening', 'Treatment']

    ax1.pie(cost_values, labels=cost_labels, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Cost Breakdown')

    # Plot 2: Cost per life year
    cost_per_ly = cost_data.get('cost_per_life_year', 0)

    # Compare to common thresholds
    thresholds = [50000, 100000, 150000]  # Common cost-effectiveness thresholds
    threshold_labels = ['$50K', '$100K', '$150K']

    bars = ax2.bar(['Cost per Life Year'] + threshold_labels,
                   [cost_per_ly] + thresholds,
                   color=['red'] + ['gray'] * 3)

    ax2.set_ylabel('Cost per Life Year ($)')
    ax2.set_title('Cost-Effectiveness')
    ax2.tick_params(axis='x', rotation=45)

    # Add value labels
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height,
                f'${height:,.0f}', ha='center', va='bottom')

    plt.tight_layout()

    if save_path:
        plt.savefig(f"{save_path}_cost_effectiveness.png", dpi=300, bbox_inches='tight')

    return fig, (ax1, ax2)


def create_summary_report(results: Dict[str, Any], save_path: Optional[str] = None) -> str:
    """Create a comprehensive summary report.

    Args:
        results: Dictionary containing simulation results
        save_path: Optional path to save report

    Returns:
        String containing the report
    """
    report = []
    report.append("CMOST Simulation Summary Report")
    report.append("=" * 40)
    report.append("")

    if 'summary' in results:
        summary = results['summary']
        report.append("Key Statistics:")
        report.append(f"  Total Patients: {summary.get('total_patients', 'N/A'):,}")
        report.append(f"  Years Simulated: {summary.get('years_simulated', 'N/A')}")
        report.append(f"  Cancer Incidence: {summary.get('cancer_incidence', 'N/A')}")
        report.append(f"  Cancer Mortality: {summary.get('cancer_mortality', 'N/A')}")
        report.append(f"  Polyps Detected: {summary.get('polyps_detected', 'N/A'):,}")
        report.append(f"  Screening Tests: {summary.get('screening_tests', 'N/A'):,}")
        report.append("")

    if 'settings' in results:
        settings = results['settings']
        report.append("Simulation Settings:")
        report.append(f"  Settings Name: {settings.get('Settings_Name', 'N/A')}")
        report.append(f"  Comment: {settings.get('Comment', 'N/A')}")
        report.append("")

    report_text = "\n".join(report)

    if save_path:
        with open(f"{save_path}_report.txt", 'w') as f:
            f.write(report_text)

    return report_text