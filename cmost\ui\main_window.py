"""
Main window for the CMOST application.

This module implements the main window of the CMOST application,
providing the primary user interface for the simulation software.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

from ..config import settings, get_setting, set_setting, save_settings, load_settings


class MainWindow:
    """
    Main window for the CMOST application.
    
    This class implements the main window of the CMOST application,
    providing tabs for general settings, screening settings, and output options.
    """
    
    def __init__(self, root):
        """
        Initialize the main window.
        
        Args:
            root: Tkinter root window
        """
        self.root = root
        self.root.title("CMOST - Colorectal Cancer Simulation")
        self.root.geometry("900x700")
        
        # Set application icon if available
        try:
            icon_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'icon.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass  # Icon not critical, continue without it
        
        # Create main frame with padding
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create the UI components
        self.create_menu()
        self.create_notebook()
        self.create_status_bar()
        self.create_bottom_buttons()
        
        # Update UI with current settings
        self.update_ui_from_settings()
    
    def create_menu(self):
        """Create the application menu bar."""
        self.menu_bar = tk.Menu(self.root)
        self.root.config(menu=self.menu_bar)
        
        # File menu
        file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Settings", command=self.new_settings)
        file_menu.add_command(label="Load Settings...", command=self.load_settings)
        file_menu.add_command(label="Save Settings", command=self.save_settings)
        file_menu.add_command(label="Save Settings As...", command=self.save_settings_as)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Simulation menu
        sim_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="Simulation", menu=sim_menu)
        sim_menu.add_command(label="Start Simulation", command=self.start_simulation)
        sim_menu.add_command(label="Start Batch Simulation", command=self.start_batch_simulation)
        sim_menu.add_separator()
        sim_menu.add_command(label="View Results", command=self.view_results)
        
        # Settings menu
        settings_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(label="Colonoscopy Settings", command=self.open_colonoscopy_settings)
        settings_menu.add_command(label="Risk Settings", command=self.open_risk_settings)
        settings_menu.add_command(label="Location Settings", command=self.open_location_settings)
        settings_menu.add_command(label="Cost Settings", command=self.open_cost_settings)
        settings_menu.add_command(label="Mortality Settings", command=self.open_mortality_settings)
        
        # Calibration menu
        calibration_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="Calibration", menu=calibration_menu)
        calibration_menu.add_command(label="Run ML Calibration", command=self.open_ml_calibration)
        calibration_menu.add_command(label="Calibration Settings", command=self.open_calibration_settings)
        calibration_menu.add_separator()
        calibration_menu.add_command(label="View Calibration Results", command=self.view_calibration_results)
        calibration_menu.add_command(label="Export Calibration Report", command=self.export_calibration_report)
        calibration_menu.add_separator()
        calibration_menu.add_command(label="Default Benchmarks", command=self.reset_to_default_benchmarks)
        
        # Help menu
        help_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="Documentation", command=self.open_documentation)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_notebook(self):
        """Create the tabbed notebook interface."""
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create tabs
        self.general_tab = ttk.Frame(self.notebook)
        self.screening_tab = ttk.Frame(self.notebook)
        self.output_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.general_tab, text="General")
        self.notebook.add(self.screening_tab, text="Screening & Surveillance")
        self.notebook.add(self.output_tab, text="Output")
        
        # Set up the content for each tab
        self.setup_general_tab()
        self.setup_screening_tab()
        self.setup_output_tab()
    
    def setup_general_tab(self):
        """Set up the General tab content."""
        # Create frames for organization
        settings_frame = ttk.LabelFrame(self.general_tab, text="Settings", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        simulation_frame = ttk.LabelFrame(self.general_tab, text="Simulation Parameters", padding=10)
        simulation_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Settings name and comment
        ttk.Label(settings_frame, text="Settings Name:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.settings_name = ttk.Entry(settings_frame, width=30)
        self.settings_name.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.settings_name.bind("<FocusOut>", self.settings_name_callback)
        
        ttk.Label(settings_frame, text="Comment:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.comment = ttk.Entry(settings_frame, width=50)
        self.comment.grid(row=1, column=1, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)
        self.comment.bind("<FocusOut>", self.comment_callback)
        
        # Number of patients
        ttk.Label(simulation_frame, text="Number of Patients:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.number_patients = ttk.Combobox(simulation_frame, width=15, state="readonly")
        self.number_patients.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.number_patients.bind("<<ComboboxSelected>>", self.number_patients_callback)
        
        # Age range
        ttk.Label(simulation_frame, text="Start Age:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.start_age = ttk.Spinbox(simulation_frame, from_=0, to=50, width=5)
        self.start_age.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.start_age.bind("<FocusOut>", self.start_age_callback)
        
        ttk.Label(simulation_frame, text="Max Age:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.max_age = ttk.Spinbox(simulation_frame, from_=50, to=120, width=5)
        self.max_age.grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)
        self.max_age.bind("<FocusOut>", self.max_age_callback)
        
        # Time step
        ttk.Label(simulation_frame, text="Time Step (years):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.time_step = ttk.Spinbox(simulation_frame, from_=0.1, to=5.0, increment=0.1, width=5)
        self.time_step.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.time_step.bind("<FocusOut>", self.time_step_callback)
        
        # Random seed
        ttk.Label(simulation_frame, text="Random Seed:").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.random_seed = ttk.Entry(simulation_frame, width=10)
        self.random_seed.grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)
        self.random_seed.bind("<FocusOut>", self.random_seed_callback)
        
        # Results path
        ttk.Label(simulation_frame, text="Results Path:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.results_path = ttk.Entry(simulation_frame, width=50)
        self.results_path.grid(row=3, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        browse_button = ttk.Button(simulation_frame, text="Browse...", command=self.browse_results_path)
        browse_button.grid(row=3, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Multiprocessing options
        self.enable_multiprocessing = tk.BooleanVar()
        multiprocessing_check = ttk.Checkbutton(
            simulation_frame, 
            text="Enable Multiprocessing", 
            variable=self.enable_multiprocessing,
            command=self.multiprocessing_callback
        )
        multiprocessing_check.grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(simulation_frame, text="Number of Processes:").grid(row=4, column=2, sticky=tk.W, padx=5, pady=5)
        self.num_processes = ttk.Spinbox(simulation_frame, from_=0, to=64, width=5)
        self.num_processes.grid(row=4, column=3, sticky=tk.W, padx=5, pady=5)
        self.num_processes.bind("<FocusOut>", self.num_processes_callback)
        
        # Add a visualization frame
        viz_frame = ttk.LabelFrame(self.general_tab, text="Model Visualization", padding=10)
        viz_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create a matplotlib figure for visualization
        self.fig, self.ax = plt.subplots(figsize=(8, 4), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, master=viz_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add visualization controls
        controls_frame = ttk.Frame(viz_frame)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(controls_frame, text="Visualization:").pack(side=tk.LEFT, padx=5)
        
        self.viz_type = ttk.Combobox(
            controls_frame, 
            values=["Age Distribution", "Polyp Prevalence", "Cancer Incidence"],
            width=20,
            state="readonly"
        )
        self.viz_type.pack(side=tk.LEFT, padx=5)
        self.viz_type.current(0)
        self.viz_type.bind("<<ComboboxSelected>>", self.update_visualization)
        
        update_button = ttk.Button(controls_frame, text="Update", command=self.update_visualization)
        update_button.pack(side=tk.LEFT, padx=5)
    
    def setup_screening_tab(self):
        """Set up the Screening & Surveillance tab content."""
        # Create frames for organization
        screening_frame = ttk.LabelFrame(self.screening_tab, text="Screening Strategy", padding=10)
        screening_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tests_frame = ttk.LabelFrame(self.screening_tab, text="Screening Tests", padding=10)
        tests_frame.pack(fill=tk.X, padx=10, pady=5)
        
        followup_frame = ttk.LabelFrame(self.screening_tab, text="Follow-up & Surveillance", padding=10)
        followup_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Screening options
        self.enable_screening = tk.BooleanVar()
        screening_check = ttk.Checkbutton(
            screening_frame, 
            text="Enable Screening", 
            variable=self.enable_screening,
            command=self.screening_callback
        )
        screening_check.grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(screening_frame, text="Screening Ages:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.screening_ages = ttk.Entry(screening_frame, width=30)
        self.screening_ages.grid(row=1, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        self.screening_ages.bind("<FocusOut>", self.screening_ages_callback)
        
        ttk.Label(screening_frame, text="Screening Compliance (%):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.screening_compliance = ttk.Spinbox(screening_frame, from_=0, to=100, width=5)
        self.screening_compliance.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.screening_compliance.bind("<FocusOut>", self.screening_compliance_callback)
        
        # Screening tests
        ttk.Label(tests_frame, text="Primary Screening Test:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.primary_test = ttk.Combobox(
            tests_frame, 
            values=["Colonoscopy", "FIT", "Sigmoidoscopy", "CT Colonography"],
            width=20,
            state="readonly"
        )
        self.primary_test.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.primary_test.current(0)
        self.primary_test.bind("<<ComboboxSelected>>", self.primary_test_callback)
        
        test_settings_button = ttk.Button(tests_frame, text="Test Settings...", command=self.open_test_settings)
        test_settings_button.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Follow-up options
        ttk.Label(followup_frame, text="Follow-up Years:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.followup_years = ttk.Spinbox(followup_frame, from_=1, to=20, width=5)
        self.followup_years.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.followup_years.bind("<FocusOut>", self.followup_years_callback)
        
        ttk.Label(followup_frame, text="Follow-up Compliance (%):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.followup_compliance = ttk.Spinbox(followup_frame, from_=0, to=100, width=5)
        self.followup_compliance.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.followup_compliance.bind("<FocusOut>", self.followup_compliance_callback)
        
        # Surveillance intervals
        ttk.Label(followup_frame, text="Surveillance Intervals (years):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(followup_frame, text="Normal:").grid(row=3, column=0, sticky=tk.E, padx=5, pady=5)
        self.normal_interval = ttk.Spinbox(followup_frame, from_=1, to=20, width=5)
        self.normal_interval.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(followup_frame, text="Low Risk:").grid(row=4, column=0, sticky=tk.E, padx=5, pady=5)
        self.low_risk_interval = ttk.Spinbox(followup_frame, from_=1, to=10, width=5)
        self.low_risk_interval.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(followup_frame, text="High Risk:").grid(row=5, column=0, sticky=tk.E, padx=5, pady=5)
        self.high_risk_interval = ttk.Spinbox(followup_frame, from_=1, to=5, width=5)
        self.high_risk_interval.grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)
    
    def setup_output_tab(self):
        """Set up the Output tab content."""
        # Create frames for organization
        output_frame = ttk.LabelFrame(self.output_tab, text="Output Options", padding=10)
        output_frame.pack(fill=tk.X, padx=10, pady=5)
        
        display_frame = ttk.LabelFrame(self.output_tab, text="Display Options", padding=10)
        display_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Output options
        self.save_results = tk.BooleanVar()
        save_results_check = ttk.Checkbutton(
            output_frame, 
            text="Save Results to File", 
            variable=self.save_results,
            command=self.save_results_callback
        )
        save_results_check.grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        self.export_excel = tk.BooleanVar()
        export_excel_check = ttk.Checkbutton(
            output_frame, 
            text="Export to Excel", 
            variable=self.export_excel,
            command=self.export_excel_callback
        )
        export_excel_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(output_frame, text="File Name Prefix:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.file_prefix = ttk.Entry(output_frame, width=30)
        self.file_prefix.grid(row=2, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        self.file_prefix.bind("<FocusOut>", self.file_prefix_callback)
        
        # Display options
        self.show_results = tk.BooleanVar()
        show_results_check = ttk.Checkbutton(
            display_frame, 
            text="Show Results After Simulation", 
            variable=self.show_results,
            command=self.show_results_callback
        )
        show_results_check.grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        self.show_progress = tk.BooleanVar()
        show_progress_check = ttk.Checkbutton(
            display_frame, 
            text="Show Progress During Simulation", 
            variable=self.show_progress,
            command=self.show_progress_callback
        )
        show_progress_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(display_frame, text="Chart Type:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.chart_type = ttk.Combobox(
            display_frame, 
            values=["Bar Chart", "Line Chart", "Pie Chart", "Table"],
            width=20,
            state="readonly"
        )
        self.chart_type.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.chart_type.current(0)
        self.chart_type.bind("<<ComboboxSelected>>", self.chart_type_callback)
    
    def create_status_bar(self):
        """Create the status bar at the bottom of the window."""
        self.status_bar = ttk.Frame(self.root, relief=tk.SUNKEN, padding=(2, 2))
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_text = ttk.Label(self.status_bar, text="Ready")
        self.status_text.pack(side=tk.LEFT)
        
        self.progress_bar = ttk.Progressbar(self.status_bar, orient=tk.HORIZONTAL, length=200, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, padx=5)
    
    def create_bottom_buttons(self):
        """Create the buttons at the bottom of the window."""
        button_frame = ttk.Frame(self.main_frame, padding="10")
        button_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.start_button = ttk.Button(button_frame, text="Start Simulation", command=self.start_simulation)
        self.start_button.pack(side=tk.RIGHT, padx=5)
        
        self.batch_button = ttk.Button(button_frame, text="Start Batch", command=self.start_batch_simulation)
        self.batch_button.pack(side=tk.RIGHT, padx=5)
        
        self.load_button = ttk.Button(button_frame, text="Load Settings", command=self.load_settings)
        self.load_button.pack(side=tk.RIGHT, padx=5)
        
        self.save_button = ttk.Button(button_frame, text="Save Settings", command=self.save_settings)
        self.save_button.pack(side=tk.RIGHT, padx=5)
    
    def update_ui_from_settings(self):
        """Update the UI components with values from settings."""
        # General tab
        self.settings_name.delete(0, tk.END)
        self.settings_name.insert(0, get_setting('Settings_Name', 'Default'))
        
        self.comment.delete(0, tk.END)
        self.comment.insert(0, get_setting('Comment', 'no comment please'))
        
        # Update number of patients combobox
        patient_values = get_setting('NumberPatientsValues', [10000, 25000, 50000, 100000, 1000000, 10000000])
        self.number_patients.config(values=[str(val) for val in patient_values])
        
        # Find the current value in the list
        current_patients = get_setting('Number_patients', 10000)
        try:
            idx = patient_values.index(current_patients)
            self.number_patients.current(idx)
        except (ValueError, tk.TclError):
            self.number_patients.current(0)
        
        # Simulation parameters
        self.start_age.delete(0, tk.END)
        self.start_age.insert(0, str(get_setting('Simulation.StartAge', 20)))
        
        self.max_age.delete(0, tk.END)
        self.max_age.insert(0, str(get_setting('Simulation.MaxAge', 100)))
        
        self.time_step.delete(0, tk.END)
        self.time_step.insert(0, str(get_setting('Simulation.TimeStep', 1.0)))
        
        self.random_seed.delete(0, tk.END)
        self.random_seed.insert(0, str(get_setting('Simulation.RandomSeed', 42)))
        
        self.results_path.delete(0, tk.END)
        self.results_path.insert(0, get_setting('ResultsPath', os.path.join(os.path.dirname(__file__), '..', 'Results')))
        
        self.enable_multiprocessing.set(get_setting('Simulation.EnableMultiprocessing', True))
        
        self.num_processes.delete(0, tk.END)
        self.num_processes.insert(0, str(get_setting('Simulation.NumProcesses', 0)))
        
        # Screening tab
        self.enable_screening.set(get_setting('Screening.EnableScreening', True))
        
        screening_ages = get_setting('Screening.ScreeningAges', [50, 55, 60, 65, 70, 75])
        self.screening_ages.delete(0, tk.END)
        self.screening_ages.insert(0, ', '.join(map(str, screening_ages)))
        
        self.screening_compliance.delete(0, tk.END)
        self.screening_compliance.insert(0, str(int(get_setting('Screening.ScreeningCompliance', 0.6) * 100)))
        
        # Set primary test
        primary_test = get_setting('Screening.PrimaryTest', 'Colonoscopy')
        try:
            idx = self.primary_test['values'].index(primary_test)
            self.primary_test.current(idx)
        except (ValueError, tk.TclError):
            self.primary_test.current(0)
        
        self.followup_years.delete(0, tk.END)
        self.followup_years.insert(0, str(get_setting('Screening.FollowupYears', 10)))
        
        self.followup_compliance.delete(0, tk.END)
        self.followup_compliance.insert(0, str(int(get_setting('Screening.FollowupCompliance', 0.8) * 100)))
        
        self.normal_interval.delete(0, tk.END)
        self.normal_interval.insert(0, str(get_setting('Screening.NormalInterval', 10)))
        
        self.low_risk_interval.delete(0, tk.END)
        self.low_risk_interval.insert(0, str(get_setting('Screening.LowRiskInterval', 5)))
        
        self.high_risk_interval.delete(0, tk.END)
        self.high_risk_interval.insert(0, str(get_setting('Screening.HighRiskInterval', 3)))
        
        # Output tab
        self.save_results.set(get_setting('ResultsFlag', True))
        self.export_excel.set(get_setting('ExcelFlag', False))
        
        self.file_prefix.delete(0, tk.END)
        self.file_prefix.insert(0, get_setting('FilePrefix', 'CMOST_Simulation'))
        
        self.show_results.set(get_setting('DispFlag', True))
        self.show_progress.set(get_setting('ShowProgress', True))
        
        # Set chart type
        chart_type = get_setting('ChartType', 'Bar Chart')
        try:
            idx = self.chart_type['values'].index(chart_type)
            self.chart_type.current(idx)
        except (ValueError, tk.TclError):
            self.chart_type.current(0)
        
        # Update visualization
        self.update_visualization()
    
    def update_settings_from_ui(self):
        """Update settings with values from the UI."""
        # General tab
        set_setting('Settings_Name', self.settings_name.get())
        set_setting('Comment', self.comment.get())
        
        # Get the selected number of patients
        try:
            idx = self.number_patients.current()
            if idx >= 0:
                patient_values = get_setting('NumberPatientsValues', [10000, 25000, 50000, 100000, 1000000, 10000000])
                set_setting('Number_patients', patient_values[idx])
        except tk.TclError:
            pass
        
        # Simulation parameters
        try:
            set_setting('Simulation.StartAge', int(self.start_age.get()))
            set_setting('Simulation.MaxAge', int(self.max_age.get()))
            set_setting('Simulation.TimeStep', float(self.time_step.get()))
            set_setting('Simulation.RandomSeed', int(self.random_seed.get()))
        except ValueError:
            pass
        
        set_setting('ResultsPath', self.results_path.get())
        set_setting('Simulation.EnableMultiprocessing', self.enable_multiprocessing.get())
        
        try:
            set_setting('Simulation.NumProcesses', int(self.num_processes.get()))
        except ValueError:
            pass
        
        # Screening tab
        set_setting('Screening.EnableScreening', self.enable_screening.get())
        
        try:
            screening_ages = [int(age.strip()) for age in self.screening_ages.get().split(',')]
            set_setting('Screening.ScreeningAges', screening_ages)
        except ValueError:
            pass
        
        set_setting('Screening.ScreeningCompliance', float(self.screening_compliance.get()) / 100)
        
        # Set primary test
        primary_test = self.primary_test.get()
        set_setting('Screening.PrimaryTest', primary_test)
        
        set_setting('Screening.FollowupYears', int(self.followup_years.get()))
        set_setting('Screening.FollowupCompliance', float(self.followup_compliance.get()) / 100)
        
        set_setting('Screening.NormalInterval', int(self.normal_interval.get()))
        set_setting('Screening.LowRiskInterval', int(self.low_risk_interval.get()))
        set_setting('Screening.HighRiskInterval', int(self.high_risk_interval.get()))
        
        # Output tab
        set_setting('ResultsFlag', self.save_results.get())
        set_setting('ExcelFlag', self.export_excel.get())
        
        set_setting('FilePrefix', self.file_prefix.get())
        
        set_setting('DispFlag', self.show_results.get())
        set_setting('ShowProgress', self.show_progress.get())
        
        # Set chart type
        chart_type = self.chart_type.get()
        set_setting('ChartType', chart_type)
    
    def open_ml_calibration(self):
        """Open the machine learning calibration dialog."""
        self.set_status("Opening ML calibration dialog...")
        try:
            # Import here to avoid circular imports
            from ui.calibration.ml_calibration_dialog import MLCalibrationDialog
            dialog = MLCalibrationDialog(self.root)
            self.root.wait_window(dialog)
            self.set_status("ML calibration completed")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open ML calibration: {str(e)}")
            self.set_status("Error opening ML calibration")
    
    def open_calibration_settings(self):
        """Open the calibration settings dialog."""
        self.set_status("Opening calibration settings...")
        try:
            # Import here to avoid circular imports
            from ui.calibration.calibration_settings import CalibrationSettingsDialog
            dialog = CalibrationSettingsDialog(self.root)
            self.root.wait_window(dialog)
            self.set_status("Calibration settings updated")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open calibration settings: {str(e)}")
            self.set_status("Error opening calibration settings")
    
    def view_calibration_results(self):
        """View the results of the most recent calibration."""
        self.set_status("Loading calibration results...")
        try:
            # Import here to avoid circular imports
            from ui.calibration.calibration_results import CalibrationResultsViewer
            viewer = CalibrationResultsViewer(self.root)
            self.root.wait_window(viewer)
            self.set_status("Calibration results viewed")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to view calibration results: {str(e)}")
            self.set_status("Error viewing calibration results")
    
    def export_calibration_report(self):
        """Export a report of the calibration results."""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("HTML files", "*.html"), ("All files", "*.*")],
            title="Export Calibration Report"
        )
        
        if not file_path:
            return
        
        self.set_status(f"Exporting calibration report to {file_path}...")
        try:
            # Import here to avoid circular imports
            from cmost.calibration.report import generate_calibration_report
            generate_calibration_report(file_path)
            self.set_status("Calibration report exported successfully")
            messagebox.showinfo("Success", f"Calibration report exported to {file_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export calibration report: {str(e)}")
            self.set_status("Error exporting calibration report")
    
    def set_status(self, message):
        """Set the status bar message."""
        self.status_text.config(text=message)
        self.root.update_idletasks()

    def new_settings(self):
        """Create new settings (reset to defaults)."""
        result = messagebox.askyesno(
            "New Settings",
            "This will reset all settings to default values. Continue?"
        )
        if result:
            # Reset settings to defaults
            from ..config.settings import settings
            settings._load_default_settings()
            self.update_ui_from_settings()
            self.set_status("Settings reset to defaults")
            messagebox.showinfo("Success", "Settings have been reset to default values")

    def load_settings(self):
        """Load settings from a file."""
        file_path = filedialog.askopenfilename(
            title="Load Settings",
            filetypes=[
                ("JSON Files", "*.json"),
                ("YAML Files", "*.yaml"),
                ("Excel Files", "*.xlsx"),
                ("MATLAB Files", "*.mat"),
                ("All Files", "*.*")
            ],
            initialdir=get_setting("SettingsPath", "./")
        )

        if not file_path:
            return

        try:
            from ..config.settings import settings
            success = settings.load_settings(file_path)
            if success:
                self.update_ui_from_settings()
                self.set_status(f"Settings loaded from {file_path}")
                messagebox.showinfo("Success", f"Settings loaded from {file_path}")
            else:
                messagebox.showerror("Error", "Failed to load settings")
                self.set_status("Error loading settings")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load settings: {str(e)}")
            self.set_status("Error loading settings")

    def save_settings(self):
        """Save current settings."""
        try:
            from ..config.settings import settings
            success = settings.save_settings()
            if success:
                self.set_status("Settings saved")
                messagebox.showinfo("Success", "Settings saved successfully")
            else:
                messagebox.showerror("Error", "Failed to save settings")
                self.set_status("Error saving settings")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")
            self.set_status("Error saving settings")

    def save_settings_as(self):
        """Save settings to a new file."""
        file_path = filedialog.asksaveasfilename(
            title="Save Settings As",
            defaultextension=".json",
            filetypes=[
                ("JSON Files", "*.json"),
                ("YAML Files", "*.yaml"),
                ("Excel Files", "*.xlsx"),
                ("MATLAB Files", "*.mat"),
                ("All Files", "*.*")
            ],
            initialdir=get_setting("SettingsPath", "./")
        )

        if not file_path:
            return

        try:
            from ..config.settings import settings
            success = settings.save_settings(file_path)
            if success:
                self.set_status(f"Settings saved to {file_path}")
                messagebox.showinfo("Success", f"Settings saved to {file_path}")
            else:
                messagebox.showerror("Error", "Failed to save settings")
                self.set_status("Error saving settings")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")
            self.set_status("Error saving settings")

