"""
Excel模板生成器和处理工具

此模块提供Excel配置文件的模板生成和处理功能，
包括创建标准的Excel配置模板和验证Excel配置文件。
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, List

try:
    import pandas as pd
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.utils.dataframe import dataframe_to_rows
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

from .defaults import (
    DEFAULT_SETTINGS,
    SIMULATION_SETTINGS,
    MODEL_PARAMETERS,
    SCREENING_SETTINGS,
    UI_SETTINGS,
    EXCEL_SETTINGS
)


class ExcelTemplateGenerator:
    """Excel配置模板生成器"""
    
    def __init__(self):
        """初始化模板生成器"""
        if not EXCEL_AVAILABLE:
            raise ImportError(
                "Excel功能需要安装pandas和openpyxl:\n"
                "pip install pandas openpyxl"
            )
    
    def create_basic_template(self, output_path: str) -> bool:
        """
        创建基础配置模板
        
        Args:
            output_path: 输出Excel文件路径
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主设置表
                self._create_settings_sheet(writer)
                
                # 模型参数表
                self._create_model_parameters_sheet(writer)
                
                # 筛查设置表
                self._create_screening_sheet(writer)
                
                # 仿真设置表
                self._create_simulation_sheet(writer)
                
                # UI设置表
                self._create_ui_sheet(writer)
                
                # 说明表
                self._create_instructions_sheet(writer)
            
            # 格式化Excel文件
            self._format_excel_file(output_path)
            
            print(f"Excel配置模板已创建: {output_path}")
            return True
            
        except Exception as e:
            print(f"创建Excel模板失败: {e}")
            return False
    
    def _create_settings_sheet(self, writer: pd.ExcelWriter) -> None:
        """创建主设置表"""
        settings_data = []
        
        for key, value in DEFAULT_SETTINGS.items():
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value, ensure_ascii=False, indent=2)
            else:
                value_str = str(value)
            
            settings_data.append({
                'Parameter': key,
                'Value': value_str,
                'Description': self._get_parameter_description(key),
                'Type': type(value).__name__
            })
        
        df = pd.DataFrame(settings_data)
        df.to_excel(writer, sheet_name='Settings', index=False)
    
    def _create_model_parameters_sheet(self, writer: pd.ExcelWriter) -> None:
        """创建模型参数表"""
        model_data = []
        
        for key, value in MODEL_PARAMETERS.items():
            if isinstance(value, dict):
                # 处理嵌套字典
                for sub_key, sub_value in value.items():
                    model_data.append({
                        'Parameter': f"{key}.{sub_key}",
                        'Value': sub_value,
                        'Description': self._get_model_parameter_description(key, sub_key),
                        'Type': type(sub_value).__name__
                    })
            else:
                model_data.append({
                    'Parameter': key,
                    'Value': value,
                    'Description': self._get_model_parameter_description(key),
                    'Type': type(value).__name__
                })
        
        df = pd.DataFrame(model_data)
        df.to_excel(writer, sheet_name='ModelParameters', index=False)
    
    def _create_screening_sheet(self, writer: pd.ExcelWriter) -> None:
        """创建筛查设置表"""
        screening_data = []
        
        for key, value in SCREENING_SETTINGS.items():
            if isinstance(value, list):
                value_str = ', '.join(str(x) for x in value)
            elif isinstance(value, dict):
                value_str = json.dumps(value, ensure_ascii=False, indent=2)
            else:
                value_str = str(value)
            
            screening_data.append({
                'Parameter': key,
                'Value': value_str,
                'Description': self._get_screening_parameter_description(key),
                'Type': type(value).__name__
            })
        
        df = pd.DataFrame(screening_data)
        df.to_excel(writer, sheet_name='Screening', index=False)
    
    def _create_simulation_sheet(self, writer: pd.ExcelWriter) -> None:
        """创建仿真设置表"""
        sim_data = []
        
        for key, value in SIMULATION_SETTINGS.items():
            sim_data.append({
                'Parameter': key,
                'Value': value,
                'Description': self._get_simulation_parameter_description(key),
                'Type': type(value).__name__
            })
        
        df = pd.DataFrame(sim_data)
        df.to_excel(writer, sheet_name='Simulation', index=False)
    
    def _create_ui_sheet(self, writer: pd.ExcelWriter) -> None:
        """创建UI设置表"""
        ui_data = []
        
        for key, value in UI_SETTINGS.items():
            ui_data.append({
                'Parameter': key,
                'Value': value,
                'Description': self._get_ui_parameter_description(key),
                'Type': type(value).__name__
            })
        
        df = pd.DataFrame(ui_data)
        df.to_excel(writer, sheet_name='UI', index=False)
    
    def _create_instructions_sheet(self, writer: pd.ExcelWriter) -> None:
        """创建说明表"""
        instructions = [
            {'Section': '使用说明', 'Content': 'CMOST Excel配置文件使用指南'},
            {'Section': '文件格式', 'Content': '此Excel文件包含多个工作表，每个表对应不同的配置类别'},
            {'Section': 'Settings表', 'Content': '主要应用设置，包括基本配置参数'},
            {'Section': 'ModelParameters表', 'Content': '疾病模型参数，控制疾病进展和风险因子'},
            {'Section': 'Screening表', 'Content': '筛查策略设置，包括筛查年龄、依从性等'},
            {'Section': 'Simulation表', 'Content': '仿真运行参数，如最大年龄、时间步长等'},
            {'Section': 'UI表', 'Content': '用户界面设置，如主题、字体大小等'},
            {'Section': '修改方法', 'Content': '直接修改Value列的值，保持Parameter列不变'},
            {'Section': '数据类型', 'Content': 'Type列显示参数的数据类型，请确保输入正确格式'},
            {'Section': '列表格式', 'Content': '列表类型参数使用逗号分隔，如: 50, 55, 60, 65'},
            {'Section': '布尔值', 'Content': '布尔类型使用True或False（区分大小写）'},
            {'Section': '保存提示', 'Content': '修改后保存为.xlsx格式，然后在CMOST中加载'}
        ]
        
        df = pd.DataFrame(instructions)
        df.to_excel(writer, sheet_name='Instructions', index=False)
    
    def _format_excel_file(self, file_path: str) -> None:
        """格式化Excel文件样式"""
        try:
            wb = openpyxl.load_workbook(file_path)
            
            # 定义样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            center_alignment = Alignment(horizontal="center", vertical="center")
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                
                # 格式化标题行
                for cell in ws[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                # 自动调整列宽
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width
            
            wb.save(file_path)
            
        except Exception as e:
            print(f"格式化Excel文件时出错: {e}")
    
    def _get_parameter_description(self, key: str) -> str:
        """获取参数描述"""
        descriptions = {
            'Settings_Name': '配置名称',
            'Comment': '配置注释',
            'Identification': '文件标识符',
            'NumberPatientsValues': '可选的患者数量列表',
            'ResultsPath': '结果输出路径',
            'SettingsPath': '设置文件路径',
            'DispFlag': '显示标志',
            'Version': '版本号'
        }
        return descriptions.get(key, '参数描述')
    
    def _get_model_parameter_description(self, key: str, sub_key: str = None) -> str:
        """获取模型参数描述"""
        descriptions = {
            'early_mult': '早期腺瘤发生率倍数',
            'early_width': '早期腺瘤年龄分布宽度',
            'early_center': '早期腺瘤发生中心年龄',
            'adv_mult': '进展期腺瘤发生率倍数',
            'adv_width': '进展期腺瘤年龄分布宽度',
            'adv_center': '进展期腺瘤发生中心年龄',
            'cancer_mult': '癌症发生率倍数',
            'cancer_width': '癌症年龄分布宽度',
            'cancer_center': '癌症发生中心年龄',
            'preclinical_dwell_time': '临床前停留时间',
            'location_factors': '部位特异性风险因子',
            'gender_factors': '性别特异性风险因子'
        }
        
        if sub_key:
            return descriptions.get(f"{key}.{sub_key}", descriptions.get(key, '模型参数'))
        return descriptions.get(key, '模型参数')
    
    def _get_screening_parameter_description(self, key: str) -> str:
        """获取筛查参数描述"""
        descriptions = {
            'EnableScreening': '是否启用筛查',
            'ScreeningAges': '筛查年龄列表',
            'ScreeningCompliance': '筛查依从性',
            'FollowupYears': '随访年数',
            'FollowupCompliance': '随访依从性',
            'Tests': '筛查测试配置'
        }
        return descriptions.get(key, '筛查参数')
    
    def _get_simulation_parameter_description(self, key: str) -> str:
        """获取仿真参数描述"""
        descriptions = {
            'MaxAge': '最大仿真年龄',
            'StartAge': '起始仿真年龄',
            'TimeStep': '时间步长',
            'RandomSeed': '随机种子',
            'EnableMultiprocessing': '是否启用多进程',
            'NumProcesses': '进程数量'
        }
        return descriptions.get(key, '仿真参数')
    
    def _get_ui_parameter_description(self, key: str) -> str:
        """获取UI参数描述"""
        descriptions = {
            'Theme': '界面主题',
            'FontSize': '字体大小',
            'ShowToolbar': '是否显示工具栏',
            'DefaultView': '默认视图',
            'AutoSave': '是否自动保存',
            'AutoSaveInterval': '自动保存间隔（分钟）'
        }
        return descriptions.get(key, 'UI参数')


def create_excel_template(template_type: str = 'basic', output_path: str = None) -> bool:
    """
    创建Excel配置模板
    
    Args:
        template_type: 模板类型 ('basic', 'advanced')
        output_path: 输出文件路径
        
    Returns:
        bool: 创建成功返回True
    """
    if not EXCEL_AVAILABLE:
        print("错误: Excel功能需要安装pandas和openpyxl")
        print("请运行: pip install pandas openpyxl")
        return False
    
    if output_path is None:
        output_path = f"cmost_config_template_{template_type}.xlsx"
    
    generator = ExcelTemplateGenerator()
    
    if template_type == 'basic':
        return generator.create_basic_template(output_path)
    else:
        print(f"不支持的模板类型: {template_type}")
        return False


def validate_excel_config(file_path: str) -> Dict[str, Any]:
    """
    验证Excel配置文件
    
    Args:
        file_path: Excel文件路径
        
    Returns:
        dict: 验证结果
    """
    if not EXCEL_AVAILABLE:
        return {
            'valid': False,
            'errors': ['Excel功能需要安装pandas和openpyxl'],
            'warnings': []
        }
    
    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'sheets_found': [],
        'parameters_count': 0
    }
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            result['valid'] = False
            result['errors'].append(f"文件不存在: {file_path}")
            return result
        
        # 读取Excel文件
        excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
        result['sheets_found'] = list(excel_data.keys())
        
        # 检查必需的工作表
        required_sheets = ['Settings']
        for sheet in required_sheets:
            if sheet not in excel_data:
                result['errors'].append(f"缺少必需的工作表: {sheet}")
                result['valid'] = False
        
        # 验证每个工作表的结构
        for sheet_name, df in excel_data.items():
            if sheet_name == 'Instructions':
                continue
                
            # 检查必需的列
            required_columns = ['Parameter', 'Value']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                result['errors'].append(
                    f"工作表 '{sheet_name}' 缺少必需的列: {missing_columns}"
                )
                result['valid'] = False
            else:
                # 统计参数数量
                result['parameters_count'] += len(df)
                
                # 检查空值
                empty_params = df[df['Parameter'].isna() | (df['Parameter'] == '')].index.tolist()
                if empty_params:
                    result['warnings'].append(
                        f"工作表 '{sheet_name}' 中有空的参数名: 行 {empty_params}"
                    )
        
        if result['parameters_count'] == 0:
            result['warnings'].append("未找到任何配置参数")
        
    except Exception as e:
        result['valid'] = False
        result['errors'].append(f"读取Excel文件时出错: {str(e)}")
    
    return result
