"""
Demonstration of integrated calibration functionality in CMOST.

This script shows how to use the integrated calibration module that combines
multiple machine learning approaches and automatically selects the best method.
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path so we can import cmost
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from cmost.ml.integrated_calibration import (
    IntegratedCalibrator,
    CalibrationMethod,
    run_integrated_calibration
)
from cmost.ml.adaptive_calibration import (
    CalibrationData,
    ParameterSpace,
    OptimizationMethod
)


def setup_logging():
    """Set up logging for the demo."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('calibration_demo.log')
        ]
    )


def demo_basic_calibration():
    """Demonstrate basic calibration functionality."""
    print("=" * 60)
    print("CMOST Integrated Calibration Demo")
    print("=" * 60)
    
    # Create output directory
    output_dir = f"calibration_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"Output directory: {output_dir}")
    
    # Initialize integrated calibrator
    print("\n1. Initializing Integrated Calibrator...")
    calibrator = IntegratedCalibrator(output_dir=output_dir)
    
    print(f"   - Parameter spaces: {len(calibrator.parameter_spaces)}")
    for ps in calibrator.parameter_spaces:
        print(f"     * {ps.name}: [{ps.min_value}, {ps.max_value}] (current: {ps.current_value})")
    
    # Demonstrate adaptive calibration (Random Forest)
    print("\n2. Running Adaptive Calibration (Random Forest)...")
    try:
        rf_result = calibrator.calibrate_with_adaptive(
            method=OptimizationMethod.RANDOM_FOREST,
            max_iterations=10  # Quick demo
        )
        
        print(f"   - Method: {rf_result.method.value}")
        print(f"   - Error: {rf_result.error:.6f}")
        print(f"   - Execution time: {rf_result.execution_time:.2f}s")
        print(f"   - Converged: {rf_result.convergence_info.get('converged', False) if rf_result.convergence_info else False}")
        print(f"   - Parameters:")
        for param, value in rf_result.parameters.items():
            print(f"     * {param}: {value:.4f}")
        
    except Exception as e:
        print(f"   - Error: {e}")
    
    # Demonstrate DNN calibration (if available)
    print("\n3. Testing DNN Calibration availability...")
    try:
        dnn_result = calibrator.calibrate_with_dnn(
            n_samples=100,  # Very small for demo
            n_patients=100,
            epochs=5
        )
        
        if dnn_result.error == float('inf'):
            print("   - DNN calibration not available (missing dependencies)")
            print(f"   - Error: {dnn_result.convergence_info.get('error', 'Unknown error')}")
        else:
            print(f"   - Method: {dnn_result.method.value}")
            print(f"   - Error: {dnn_result.error:.6f}")
            print(f"   - Execution time: {dnn_result.execution_time:.2f}s")
            print(f"   - Parameters:")
            for param, value in dnn_result.parameters.items():
                print(f"     * {param}: {value:.4f}")
        
    except Exception as e:
        print(f"   - Error: {e}")
    
    # Generate comparison report
    print("\n4. Generating Comparison Report...")
    report = calibrator.get_comparison_report()
    
    if "message" in report:
        print(f"   - {report['message']}")
    else:
        print(f"   - Total methods tried: {report['total_methods_tried']}")
        print(f"   - Best method: {report['best_method'] or 'None'}")
        if report['best_error'] is not None:
            print(f"   - Best error: {report['best_error']:.6f}")
        else:
            print("   - Best error: None")

        print("   - Results summary:")
        for result in report['results']:
            print(f"     * {result['method']}: error={result['error']:.6f}, "
                  f"time={result['execution_time']:.2f}s, "
                  f"converged={result['converged']}")
    
    # Save results
    print("\n5. Saving Results...")
    results_file = os.path.join(output_dir, "demo_results.json")
    calibrator.save_results(results_file)
    print(f"   - Results saved to: {results_file}")
    
    return output_dir


def demo_auto_selection():
    """Demonstrate automatic method selection."""
    print("\n" + "=" * 60)
    print("Auto-Selection Demo")
    print("=" * 60)
    
    # Create output directory
    output_dir = f"auto_selection_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"Output directory: {output_dir}")
    
    print("\n1. Running Auto-Selection (Quick Evaluation)...")
    try:
        result = run_integrated_calibration(
            method=CalibrationMethod.AUTO_SELECT,
            output_dir=output_dir,
            quick_evaluation=True
        )
        
        print(f"   - Selected method: {result.method.value}")
        print(f"   - Final error: {result.error:.6f}")
        print(f"   - Total execution time: {result.execution_time:.2f}s")
        print(f"   - Converged: {result.convergence_info.get('converged', False) if result.convergence_info else False}")
        
        if result.parameters:
            print(f"   - Optimized parameters:")
            for param, value in result.parameters.items():
                print(f"     * {param}: {value:.4f}")
        
    except Exception as e:
        print(f"   - Error: {e}")
    
    return output_dir


def demo_specific_method():
    """Demonstrate running with a specific method."""
    print("\n" + "=" * 60)
    print("Specific Method Demo")
    print("=" * 60)
    
    # Create output directory
    output_dir = f"specific_method_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"Output directory: {output_dir}")
    
    print("\n1. Running Random Forest Calibration...")
    try:
        result = run_integrated_calibration(
            method=CalibrationMethod.RANDOM_FOREST,
            output_dir=output_dir,
            quick_evaluation=True
        )
        
        print(f"   - Method: {result.method.value}")
        print(f"   - Error: {result.error:.6f}")
        print(f"   - Execution time: {result.execution_time:.2f}s")
        print(f"   - Converged: {result.convergence_info.get('converged', False) if result.convergence_info else False}")
        
        if result.parameters:
            print(f"   - Parameters:")
            for param, value in result.parameters.items():
                print(f"     * {param}: {value:.4f}")
        
    except Exception as e:
        print(f"   - Error: {e}")
    
    return output_dir


def demo_custom_parameter_spaces():
    """Demonstrate using custom parameter spaces."""
    print("\n" + "=" * 60)
    print("Custom Parameter Spaces Demo")
    print("=" * 60)
    
    # Create custom parameter spaces
    custom_spaces = [
        ParameterSpace("custom_param1", 0.1, 2.0, 1.0),
        ParameterSpace("custom_param2", 10.0, 100.0, 50.0),
        ParameterSpace("custom_param3", 0.01, 0.1, 0.05, parameter_type="discrete", 
                      discrete_values=[0.01, 0.02, 0.05, 0.1])
    ]
    
    print(f"\n1. Custom parameter spaces:")
    for ps in custom_spaces:
        print(f"   - {ps.name}: [{ps.min_value}, {ps.max_value}] "
              f"(type: {ps.parameter_type}, current: {ps.current_value})")
        if ps.discrete_values:
            print(f"     Discrete values: {ps.discrete_values}")
    
    # Create output directory
    output_dir = f"custom_params_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"\nOutput directory: {output_dir}")
    
    # Initialize calibrator with custom parameter spaces
    print("\n2. Running calibration with custom parameter spaces...")
    try:
        calibrator = IntegratedCalibrator(
            parameter_spaces=custom_spaces,
            output_dir=output_dir
        )
        
        result = calibrator.calibrate_with_adaptive(
            method=OptimizationMethod.RANDOM_FOREST,
            max_iterations=5
        )
        
        print(f"   - Method: {result.method.value}")
        print(f"   - Error: {result.error:.6f}")
        print(f"   - Execution time: {result.execution_time:.2f}s")
        
        if result.parameters:
            print(f"   - Optimized parameters:")
            for param, value in result.parameters.items():
                print(f"     * {param}: {value:.4f}")
        
    except Exception as e:
        print(f"   - Error: {e}")
    
    return output_dir


def main():
    """Main demo function."""
    setup_logging()
    
    print("CMOST Integrated Calibration Demonstration")
    print("This demo shows the integrated calibration functionality")
    print("that combines multiple machine learning approaches.\n")
    
    try:
        # Run different demos
        demo_basic_calibration()
        demo_auto_selection()
        demo_specific_method()
        demo_custom_parameter_spaces()
        
        print("\n" + "=" * 60)
        print("Demo completed successfully!")
        print("=" * 60)
        
        print("\nKey features demonstrated:")
        print("1. Multiple calibration methods (Random Forest, DNN)")
        print("2. Automatic method selection")
        print("3. Custom parameter spaces")
        print("4. Error comparison and reporting")
        print("5. Graceful handling of missing dependencies")
        
        print("\nNext steps:")
        print("- Install optional dependencies (pyDOE2, torch, etc.) for full DNN support")
        print("- Customize parameter spaces for your specific use case")
        print("- Use real benchmark data for calibration")
        print("- Integrate with your simulation workflow")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
