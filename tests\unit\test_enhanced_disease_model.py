"""
Unit tests for enhanced disease natural history model in CMOST.
"""

import unittest
import numpy as np

from cmost.models.molecular_features import (
    MolecularProfile, MolecularProfileGenerator,
    MSIStatus, CIMPStatus, KRASStatus, BRAFStatus
)
from cmost.models.genetic_factors import (
    GeneticProfile, GeneticProfileGenerator,
    SyndromeType, MMRGene
)
from cmost.models.patient import Patient
from cmost.models.polyp import Polyp
from cmost.models.serrated_lesion import SerratedLesion


class TestMolecularProfile(unittest.TestCase):
    """Test cases for MolecularProfile class."""
    
    def test_default_molecular_profile(self):
        """Test default molecular profile creation."""
        profile = MolecularProfile()
        
        self.assertEqual(profile.msi_status, MSIStatus.MSS)
        self.assertEqual(profile.cimp_status, CIMPStatus.CIMP_NEGATIVE)
        self.assertEqual(profile.kras_status, KRASStatus.WILD_TYPE)
        self.assertEqual(profile.braf_status, BRAFStatus.WILD_TYPE)
        self.assertFalse(profile.tp53_mutated)
        self.assertFalse(profile.apc_mutated)
        self.assertEqual(profile.pathway, "traditional")
    
    def test_serrated_pathway_classification(self):
        """Test serrated pathway classification."""
        profile = MolecularProfile(
            braf_status=BRAFStatus.MUTATED,
            cimp_status=CIMPStatus.CIMP_HIGH
        )
        
        self.assertEqual(profile.pathway, "serrated")
    
    def test_traditional_pathway_classification(self):
        """Test traditional pathway classification."""
        profile = MolecularProfile(
            apc_mutated=True,
            kras_status=KRASStatus.MUTATED,
            chromosomal_instability=True
        )
        
        self.assertEqual(profile.pathway, "traditional")
    
    def test_molecular_interactions(self):
        """Test molecular interactions (BRAF/KRAS mutual exclusivity)."""
        profile = MolecularProfile(
            braf_status=BRAFStatus.MUTATED,
            kras_status=KRASStatus.MUTATED  # Should be overridden
        )
        
        # BRAF and KRAS should be mutually exclusive
        self.assertEqual(profile.braf_status, BRAFStatus.MUTATED)
        self.assertEqual(profile.kras_status, KRASStatus.WILD_TYPE)
    
    def test_progression_modifier(self):
        """Test progression rate modifier calculation."""
        # MSI-high profile (slower progression)
        msi_profile = MolecularProfile(msi_status=MSIStatus.MSI_HIGH)
        msi_modifier = msi_profile.get_progression_modifier()
        self.assertLess(msi_modifier, 1.0)
        
        # KRAS mutated profile (faster progression)
        kras_profile = MolecularProfile(kras_status=KRASStatus.MUTATED)
        kras_modifier = kras_profile.get_progression_modifier()
        self.assertGreater(kras_modifier, 1.0)
        
        # TP53 mutated profile (much faster progression)
        tp53_profile = MolecularProfile(tp53_mutated=True)
        tp53_modifier = tp53_profile.get_progression_modifier()
        self.assertGreater(tp53_modifier, 1.0)
    
    def test_treatment_response_modifier(self):
        """Test treatment response modifier calculation."""
        # MSI-high profile (better immunotherapy response)
        msi_profile = MolecularProfile(msi_status=MSIStatus.MSI_HIGH)
        modifiers = msi_profile.get_treatment_response_modifier()
        
        self.assertGreater(modifiers['immunotherapy'], 1.0)
        self.assertLess(modifiers['chemotherapy'], 1.0)
        
        # BRAF mutated profile (better targeted therapy response)
        braf_profile = MolecularProfile(braf_status=BRAFStatus.MUTATED)
        braf_modifiers = braf_profile.get_treatment_response_modifier()
        
        self.assertGreater(braf_modifiers['targeted_therapy'], 1.0)
    
    def test_serialization(self):
        """Test molecular profile serialization."""
        profile = MolecularProfile(
            msi_status=MSIStatus.MSI_HIGH,
            kras_status=KRASStatus.MUTATED,
            tp53_mutated=True
        )
        
        # Test to_dict
        profile_dict = profile.to_dict()
        self.assertEqual(profile_dict['msi_status'], 'msi_high')
        self.assertEqual(profile_dict['kras_status'], 'mutated')
        self.assertTrue(profile_dict['tp53_mutated'])
        
        # Test from_dict
        reconstructed = MolecularProfile.from_dict(profile_dict)
        self.assertEqual(reconstructed.msi_status, profile.msi_status)
        self.assertEqual(reconstructed.kras_status, profile.kras_status)
        self.assertEqual(reconstructed.tp53_mutated, profile.tp53_mutated)


class TestGeneticProfile(unittest.TestCase):
    """Test cases for GeneticProfile class."""
    
    def test_default_genetic_profile(self):
        """Test default genetic profile creation."""
        profile = GeneticProfile()
        
        self.assertEqual(profile.syndrome, SyndromeType.NONE)
        self.assertFalse(profile.apc_mutation)
        self.assertEqual(profile.mutyh_mutations, 0)
        self.assertEqual(profile.family_history_strength, "none")
        self.assertEqual(profile.affected_relatives, 0)
    
    def test_fap_syndrome_classification(self):
        """Test FAP syndrome classification."""
        profile = GeneticProfile(apc_mutation=True)
        
        # Should be classified as FAP or attenuated FAP
        self.assertIn(profile.syndrome, [SyndromeType.FAP, SyndromeType.ATTENUATED_FAP])
    
    def test_lynch_syndrome_classification(self):
        """Test Lynch syndrome classification."""
        profile = GeneticProfile()
        profile.mmr_mutations[MMRGene.MLH1] = True
        profile._update_syndrome_classification()
        
        self.assertEqual(profile.syndrome, SyndromeType.LYNCH)
    
    def test_map_syndrome_classification(self):
        """Test MAP syndrome classification."""
        profile = GeneticProfile(mutyh_mutations=2)
        
        self.assertEqual(profile.syndrome, SyndromeType.MAP)
    
    def test_age_specific_risk_fap(self):
        """Test age-specific risk for FAP."""
        profile = GeneticProfile(apc_mutation=True)
        # Force FAP classification
        profile.syndrome = SyndromeType.FAP
        
        # FAP should have high risk at young age
        risk_20 = profile.get_age_specific_risk(20)
        risk_40 = profile.get_age_specific_risk(40)
        
        self.assertGreater(risk_20, 0)
        self.assertGreater(risk_40, risk_20)
    
    def test_age_specific_risk_lynch(self):
        """Test age-specific risk for Lynch syndrome."""
        profile = GeneticProfile()
        profile.mmr_mutations[MMRGene.MLH1] = True
        profile._update_syndrome_classification()
        
        # Lynch should have elevated risk compared to sporadic
        risk_40 = profile.get_age_specific_risk(40)
        risk_60 = profile.get_age_specific_risk(60)
        
        self.assertGreater(risk_40, 0.005)  # Higher than sporadic
        self.assertGreater(risk_60, risk_40)
    
    def test_polyp_risk_modifier(self):
        """Test polyp risk modifier."""
        # FAP should have very high polyp risk
        fap_profile = GeneticProfile(apc_mutation=True)
        fap_profile.syndrome = SyndromeType.FAP
        fap_modifier = fap_profile.get_polyp_risk_modifier()
        self.assertGreater(fap_modifier, 10.0)
        
        # Lynch should have moderate increase
        lynch_profile = GeneticProfile()
        lynch_profile.mmr_mutations[MMRGene.MLH1] = True
        lynch_profile._update_syndrome_classification()
        lynch_modifier = lynch_profile.get_polyp_risk_modifier()
        self.assertGreater(lynch_modifier, 1.0)
        self.assertLess(lynch_modifier, fap_modifier)
    
    def test_screening_recommendations(self):
        """Test screening recommendations."""
        # FAP recommendations
        fap_profile = GeneticProfile(apc_mutation=True)
        fap_profile.syndrome = SyndromeType.FAP
        fap_recs = fap_profile.get_screening_recommendations()
        
        self.assertLess(fap_recs['start_age'], 20)
        self.assertEqual(fap_recs['interval'], 1)  # Annual
        
        # Lynch recommendations
        lynch_profile = GeneticProfile()
        lynch_profile.mmr_mutations[MMRGene.MLH1] = True
        lynch_profile._update_syndrome_classification()
        lynch_recs = lynch_profile.get_screening_recommendations()
        
        self.assertLess(lynch_recs['start_age'], 30)
        self.assertLessEqual(lynch_recs['interval'], 2)


class TestMolecularProfileGenerator(unittest.TestCase):
    """Test cases for MolecularProfileGenerator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.generator = MolecularProfileGenerator()
    
    def test_adenoma_profile_generation(self):
        """Test molecular profile generation for adenomas."""
        profile = self.generator.generate_profile(
            lesion_type="adenoma",
            stage=1,
            location=1,
            patient_age=60
        )
        
        self.assertIsInstance(profile, MolecularProfile)
        # Traditional pathway more likely for adenomas
        self.assertIn(profile.pathway, ["traditional", "mixed"])
    
    def test_serrated_profile_generation(self):
        """Test molecular profile generation for serrated lesions."""
        profile = self.generator.generate_profile(
            lesion_type="serrated",
            stage=2,
            location=5,  # Proximal location
            patient_age=65
        )
        
        self.assertIsInstance(profile, MolecularProfile)
        # Serrated pathway more likely for serrated lesions
        # Note: Due to randomness, we can't guarantee pathway, but can check structure
    
    def test_age_effect_on_mutations(self):
        """Test that older age increases mutation probability."""
        # Set random seed for reproducible test
        np.random.seed(42)

        # Generate multiple profiles to test statistical tendency
        young_profiles = [
            self.generator.generate_profile(patient_age=30)
            for _ in range(500)  # Larger sample for better statistics
        ]

        np.random.seed(43)  # Different seed for old profiles
        old_profiles = [
            self.generator.generate_profile(patient_age=80)
            for _ in range(500)
        ]

        # Count mutations in each group
        young_mutations = sum(1 for p in young_profiles if p.tp53_mutated or p.apc_mutated)
        old_mutations = sum(1 for p in old_profiles if p.tp53_mutated or p.apc_mutated)

        # Calculate mutation rates
        young_rate = young_mutations / len(young_profiles)
        old_rate = old_mutations / len(old_profiles)

        # Older patients should have higher mutation rate (allow for some statistical variation)
        self.assertGreaterEqual(old_rate, young_rate * 0.9)  # At least 90% of expected difference


class TestPatientGeneticIntegration(unittest.TestCase):
    """Test cases for patient-genetic integration."""
    
    def test_patient_genetic_profile_initialization(self):
        """Test patient genetic profile initialization."""
        patient = Patient(
            id=1,
            age=55,
            gender='M',
            risk_factors={'family_history': 2.0}  # Strong family history
        )
        
        self.assertIsNotNone(patient.genetic_profile)
        # Should have elevated family history
        self.assertIn(patient.genetic_profile.family_history_strength, 
                     ["moderate", "strong"])
    
    def test_genetic_risk_calculation(self):
        """Test genetic risk calculation in individual risk."""
        # Patient with Lynch syndrome
        lynch_patient = Patient(
            id=1,
            age=45,
            gender='F',
            risk_factors={}
        )
        
        # Manually set Lynch syndrome
        lynch_patient.genetic_profile.mmr_mutations[MMRGene.MLH1] = True
        lynch_patient.genetic_profile._update_syndrome_classification()
        
        # Recalculate risk
        lynch_risk = lynch_patient._calculate_individual_risk()
        
        # Should have elevated risk
        self.assertGreater(lynch_risk, 1.0)
    
    def test_polyp_molecular_profile_integration(self):
        """Test polyp molecular profile integration."""
        polyp = Polyp(
            id=1,
            location=3,
            size=0.8,
            stage=2,
            patient_id=1,
            patient_gender='M'
        )
        
        self.assertIsNotNone(polyp.molecular_profile)
        self.assertIsInstance(polyp.molecular_profile, MolecularProfile)
    
    def test_serrated_lesion_molecular_profile_integration(self):
        """Test serrated lesion molecular profile integration."""
        lesion = SerratedLesion(
            id=1,
            location=5,
            size=1.2,
            stage=2,
            patient_id=1,
            patient_gender='F'
        )
        
        self.assertIsNotNone(lesion.molecular_profile)
        self.assertIsInstance(lesion.molecular_profile, MolecularProfile)


if __name__ == '__main__':
    unittest.main()
