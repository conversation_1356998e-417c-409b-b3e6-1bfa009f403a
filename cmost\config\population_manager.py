"""
人口参数管理模块

该模块提供人口参数的加载、验证和管理功能，支持Excel和JSON格式的人口参数文件。
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class PopulationManager:
    """人口参数管理器"""
    
    def __init__(self):
        """初始化人口参数管理器"""
        self.population_data = None
        self.population_type = 'natural'  # 'natural' or 'birth_cohort'
        self.file_path = None
        
    def load_population_file(self, file_path: str) -> bool:
        """
        加载人口参数文件
        
        Args:
            file_path: 人口参数文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.json':
                return self._load_json_file(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                return self._load_excel_file(file_path)
            else:
                logger.error(f"不支持的文件格式: {file_ext}")
                return False
                
        except Exception as e:
            logger.error(f"加载人口参数文件失败: {e}")
            return False
    
    def _load_json_file(self, file_path: str) -> bool:
        """加载JSON格式的人口参数文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if self._validate_population_data(data):
                self.population_data = data
                self.file_path = file_path
                self.population_type = data.get('population_type', 'natural')
                logger.info(f"成功加载JSON人口参数文件: {file_path}")
                return True
            else:
                logger.error("人口参数数据验证失败")
                return False
                
        except Exception as e:
            logger.error(f"加载JSON文件失败: {e}")
            return False
    
    def _load_excel_file(self, file_path: str) -> bool:
        """加载Excel格式的人口参数文件"""
        try:
            import pandas as pd
            
            # 读取Excel文件的所有工作表
            excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            
            # 转换为标准格式
            data = self._convert_excel_to_standard_format(excel_data)
            
            if self._validate_population_data(data):
                self.population_data = data
                self.file_path = file_path
                self.population_type = data.get('population_type', 'natural')
                logger.info(f"成功加载Excel人口参数文件: {file_path}")
                return True
            else:
                logger.error("人口参数数据验证失败")
                return False
                
        except ImportError:
            logger.error("需要安装pandas和openpyxl来支持Excel文件")
            return False
        except Exception as e:
            logger.error(f"加载Excel文件失败: {e}")
            return False
    
    def _convert_excel_to_standard_format(self, excel_data: Dict[str, Any]) -> Dict[str, Any]:
        """将Excel数据转换为标准格式"""
        data = {
            'population_type': 'natural',
            'male_proportion': 0.51,
            'age_distribution': {},
            'birth_cohort': {}
        }
        
        # 处理基本信息工作表
        if 'BasicInfo' in excel_data or '基本信息' in excel_data:
            basic_sheet = excel_data.get('BasicInfo', excel_data.get('基本信息'))
            if not basic_sheet.empty:
                for _, row in basic_sheet.iterrows():
                    param = str(row.iloc[0]).strip()
                    value = row.iloc[1]
                    
                    if param in ['population_type', '人口类型']:
                        data['population_type'] = str(value).strip()
                    elif param in ['male_proportion', '男性比例']:
                        data['male_proportion'] = float(value)
        
        # 处理年龄分布工作表
        if 'AgeDistribution' in excel_data or '年龄分布' in excel_data:
            age_sheet = excel_data.get('AgeDistribution', excel_data.get('年龄分布'))
            if not age_sheet.empty:
                for _, row in age_sheet.iterrows():
                    age_group = str(row.iloc[0]).strip()
                    proportion = float(row.iloc[1])
                    data['age_distribution'][age_group] = proportion
        
        # 处理出生队列工作表
        if 'BirthCohort' in excel_data or '出生队列' in excel_data:
            cohort_sheet = excel_data.get('BirthCohort', excel_data.get('出生队列'))
            if not cohort_sheet.empty:
                for _, row in cohort_sheet.iterrows():
                    param = str(row.iloc[0]).strip()
                    value = row.iloc[1]
                    
                    if param in ['birth_year', '出生年份']:
                        data['birth_cohort']['birth_year'] = int(value)
                    elif param in ['cohort_size', '队列大小']:
                        data['birth_cohort']['cohort_size'] = int(value)
                    elif param in ['follow_years', '随访年数']:
                        data['birth_cohort']['follow_years'] = int(value)
        
        return data
    
    def _validate_population_data(self, data: Dict[str, Any]) -> bool:
        """验证人口参数数据的有效性"""
        try:
            # 检查必需字段
            if 'population_type' not in data:
                logger.error("缺少population_type字段")
                return False
            
            if data['population_type'] not in ['natural', 'birth_cohort']:
                logger.error(f"无效的人口类型: {data['population_type']}")
                return False
            
            # 检查男性比例
            if 'male_proportion' in data:
                male_prop = data['male_proportion']
                if not (0 <= male_prop <= 1):
                    logger.error(f"男性比例必须在0-1之间: {male_prop}")
                    return False
            
            # 检查年龄分布
            if 'age_distribution' in data and data['age_distribution']:
                total_prop = sum(data['age_distribution'].values())
                if abs(total_prop - 1.0) > 0.01:  # 允许1%的误差
                    logger.warning(f"年龄分布总和不等于1: {total_prop}")
            
            # 检查出生队列参数
            if data['population_type'] == 'birth_cohort':
                if 'birth_cohort' not in data:
                    logger.error("出生队列类型缺少birth_cohort参数")
                    return False
                
                cohort = data['birth_cohort']
                if 'birth_year' not in cohort or 'cohort_size' not in cohort:
                    logger.error("出生队列缺少必需参数")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证人口参数数据时出错: {e}")
            return False
    
    def get_population_parameters(self) -> Dict[str, Any]:
        """获取当前的人口参数"""
        if self.population_data:
            return self.population_data.copy()
        else:
            # 返回默认参数
            from .defaults import POPULATION_PARAMETERS
            return POPULATION_PARAMETERS['DefaultPopulation'].copy()
    
    def get_population_type(self) -> str:
        """获取人口类型"""
        return self.population_type
    
    def create_population_template(self, file_path: str, format_type: str = 'excel') -> bool:
        """
        创建人口参数模板文件
        
        Args:
            file_path: 输出文件路径
            format_type: 文件格式 ('excel' 或 'json')
            
        Returns:
            bool: 创建是否成功
        """
        try:
            if format_type.lower() == 'json':
                return self._create_json_template(file_path)
            elif format_type.lower() == 'excel':
                return self._create_excel_template(file_path)
            else:
                logger.error(f"不支持的模板格式: {format_type}")
                return False
                
        except Exception as e:
            logger.error(f"创建人口参数模板失败: {e}")
            return False
    
    def _create_json_template(self, file_path: str) -> bool:
        """创建JSON格式的人口参数模板"""
        template = {
            "population_type": "natural",
            "male_proportion": 0.51,
            "age_distribution": {
                "0-4": 0.055,
                "5-9": 0.055,
                "10-14": 0.055,
                "15-19": 0.055,
                "20-24": 0.065,
                "25-29": 0.075,
                "30-34": 0.085,
                "35-39": 0.085,
                "40-44": 0.080,
                "45-49": 0.075,
                "50-54": 0.070,
                "55-59": 0.065,
                "60-64": 0.055,
                "65-69": 0.045,
                "70-74": 0.035,
                "75-79": 0.025,
                "80-84": 0.015,
                "85-89": 0.008,
                "90-94": 0.003,
                "95-99": 0.001,
                "100+": 0.0002
            },
            "birth_cohort": {
                "birth_year": 1970,
                "cohort_size": 100000,
                "follow_years": 80
            }
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)
        
        logger.info(f"成功创建JSON人口参数模板: {file_path}")
        return True
    
    def _create_excel_template(self, file_path: str) -> bool:
        """创建Excel格式的人口参数模板"""
        try:
            import pandas as pd
            
            # 基本信息工作表
            basic_info = pd.DataFrame({
                '参数名称': ['population_type', 'male_proportion'],
                '参数值': ['natural', 0.51],
                '说明': ['人口类型(natural/birth_cohort)', '男性比例(0-1)']
            })
            
            # 年龄分布工作表
            age_groups = ['0-4', '5-9', '10-14', '15-19', '20-24', '25-29', '30-34', 
                         '35-39', '40-44', '45-49', '50-54', '55-59', '60-64', 
                         '65-69', '70-74', '75-79', '80-84', '85-89', '90-94', 
                         '95-99', '100+']
            proportions = [0.055, 0.055, 0.055, 0.055, 0.065, 0.075, 0.085, 
                          0.085, 0.080, 0.075, 0.070, 0.065, 0.055, 0.045, 
                          0.035, 0.025, 0.015, 0.008, 0.003, 0.001, 0.0002]
            
            age_distribution = pd.DataFrame({
                '年龄组': age_groups,
                '比例': proportions,
                '说明': ['各年龄组在总人口中的比例'] * len(age_groups)
            })
            
            # 出生队列工作表
            birth_cohort = pd.DataFrame({
                '参数名称': ['birth_year', 'cohort_size', 'follow_years'],
                '参数值': [1970, 100000, 80],
                '说明': ['出生年份', '队列大小', '随访年数']
            })
            
            # 写入Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                basic_info.to_excel(writer, sheet_name='基本信息', index=False)
                age_distribution.to_excel(writer, sheet_name='年龄分布', index=False)
                birth_cohort.to_excel(writer, sheet_name='出生队列', index=False)
            
            logger.info(f"成功创建Excel人口参数模板: {file_path}")
            return True
            
        except ImportError:
            logger.error("需要安装pandas和openpyxl来创建Excel模板")
            return False


# 全局人口管理器实例
population_manager = PopulationManager()
