"""
Benchmark data management for CMOST calibration.
"""
from typing import Dict, List, Optional, Tuple, Any, Union
import pandas as pd
import numpy as np
import os
import json
from datetime import datetime

class BenchmarkManager:
    """Manager for benchmark data used in calibration."""
    
    def __init__(self, data_path: Optional[str] = None):
        """Initialize benchmark manager.
        
        Args:
            data_path: Path to benchmark data file (optional)
        """
        self.benchmarks = {
            # Early adenoma prevalence by age and gender
            'early_adenoma_prevalence': {
                'M': {},  # Male prevalence by age
                'F': {}   # Female prevalence by age
            },
            
            # Advanced adenoma prevalence by age and gender
            'advanced_adenoma_prevalence': {
                'M': {},  # Male prevalence by age
                'F': {}   # Female prevalence by age
            },
            
            # Preclinical cancer dwell time (years)
            'preclinical_dwell_time': 3.0,
            
            # Cancer incidence by age and gender (per 100,000)
            'cancer_incidence': {
                'M': {},  # Male incidence by age
                'F': {}   # Female incidence by age
            },
            
            # Cancer mortality by age and gender (per 100,000)
            'cancer_mortality': {
                'M': {},  # Male mortality by age
                'F': {}   # Female mortality by age
            },
            
            # Fraction of rectal cancer by age and gender
            'rectal_cancer_fraction': {
                'M': {},  # Male rectal fraction by age
                'F': {}   # Female rectal fraction by age
            }
        }
        
        # Load data if provided
        if data_path and os.path.exists(data_path):
            self.load_benchmarks(data_path)
    
    def set_early_adenoma_prevalence(self, gender: str, age: int, prevalence: float) -> None:
        """Set early adenoma prevalence for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            prevalence: Prevalence as percentage (0-100)
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        self.benchmarks['early_adenoma_prevalence'][gender][age] = prevalence
    
    def set_advanced_adenoma_prevalence(self, gender: str, age: int, prevalence: float) -> None:
        """Set advanced adenoma prevalence for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            prevalence: Prevalence as percentage (0-100)
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        self.benchmarks['advanced_adenoma_prevalence'][gender][age] = prevalence
    
    def set_preclinical_dwell_time(self, years: float) -> None:
        """Set preclinical cancer dwell time.
        
        Args:
            years: Dwell time in years
        """
        self.benchmarks['preclinical_dwell_time'] = years
    
    def set_cancer_incidence(self, gender: str, age: int, incidence: float) -> None:
        """Set cancer incidence for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            incidence: Incidence rate per 100,000
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        self.benchmarks['cancer_incidence'][gender][age] = incidence
    
    def set_cancer_mortality(self, gender: str, age: int, mortality: float) -> None:
        """Set cancer mortality for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            mortality: Mortality rate per 100,000
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        self.benchmarks['cancer_mortality'][gender][age] = mortality
    
    def set_rectal_cancer_fraction(self, gender: str, age: int, fraction: float) -> None:
        """Set fraction of rectal cancer for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            fraction: Fraction as percentage (0-100)
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        if not 0 <= fraction <= 100:
            raise ValueError("Fraction must be between 0 and 100")
        
        self.benchmarks['rectal_cancer_fraction'][gender][age] = fraction
    
    def get_early_adenoma_prevalence(self, gender: str, age: int) -> float:
        """Get early adenoma prevalence for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            
        Returns:
            Prevalence as percentage
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        # Return closest age if exact age not available
        if age not in self.benchmarks['early_adenoma_prevalence'][gender]:
            ages = list(self.benchmarks['early_adenoma_prevalence'][gender].keys())
            if not ages:
                return 0.0
            closest_age = min(ages, key=lambda x: abs(x - age))
            return self.benchmarks['early_adenoma_prevalence'][gender][closest_age]
        
        return self.benchmarks['early_adenoma_prevalence'][gender][age]
    
    def get_advanced_adenoma_prevalence(self, gender: str, age: int) -> float:
        """Get advanced adenoma prevalence for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            
        Returns:
            Prevalence as percentage
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        # Return closest age if exact age not available
        if age not in self.benchmarks['advanced_adenoma_prevalence'][gender]:
            ages = list(self.benchmarks['advanced_adenoma_prevalence'][gender].keys())
            if not ages:
                return 0.0
            closest_age = min(ages, key=lambda x: abs(x - age))
            return self.benchmarks['advanced_adenoma_prevalence'][gender][closest_age]
        
        return self.benchmarks['advanced_adenoma_prevalence'][gender][age]
    
    def get_preclinical_dwell_time(self) -> float:
        """Get preclinical cancer dwell time.
        
        Returns:
            Dwell time in years
        """
        return self.benchmarks['preclinical_dwell_time']
    
    def get_cancer_incidence(self, gender: str, age: int) -> float:
        """Get cancer incidence for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            
        Returns:
            Incidence rate per 100,000
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        # Return closest age if exact age not available
        if age not in self.benchmarks['cancer_incidence'][gender]:
            ages = list(self.benchmarks['cancer_incidence'][gender].keys())
            if not ages:
                return 0.0
            closest_age = min(ages, key=lambda x: abs(x - age))
            return self.benchmarks['cancer_incidence'][gender][closest_age]
        
        return self.benchmarks['cancer_incidence'][gender][age]
    
    def get_cancer_mortality(self, gender: str, age: int) -> float:
        """Get cancer mortality for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            
        Returns:
            Mortality rate per 100,000
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        # Return closest age if exact age not available
        if age not in self.benchmarks['cancer_mortality'][gender]:
            ages = list(self.benchmarks['cancer_mortality'][gender].keys())
            if not ages:
                return 0.0
            closest_age = min(ages, key=lambda x: abs(x - age))
            return self.benchmarks['cancer_mortality'][gender][closest_age]
        
        return self.benchmarks['cancer_mortality'][gender][age]
    
    def get_rectal_cancer_fraction(self, gender: str, age: int) -> float:
        """Get fraction of rectal cancer for specific gender and age.
        
        Args:
            gender: Gender ('M' or 'F')
            age: Age in years
            
        Returns:
            Fraction as percentage
        """
        if gender not in ['M', 'F']:
            raise ValueError("Gender must be 'M' or 'F'")
        
        # Return closest age if exact age not available
        if age not in self.benchmarks['rectal_cancer_fraction'][gender]:
            ages = list(self.benchmarks['rectal_cancer_fraction'][gender].keys())
            if not ages:
                return 0.0
            closest_age = min(ages, key=lambda x: abs(x - age))
            return self.benchmarks['rectal_cancer_fraction'][gender][closest_age]
        
        return self.benchmarks['rectal_cancer_fraction'][gender][age]
    
    def load_benchmarks(self, filepath: str) -> None:
        """Load benchmarks from file.
        
        Args:
            filepath: Path to benchmark file (JSON or CSV)
        """
        _, ext = os.path.splitext(filepath)
        
        if ext.lower() == '.json':
            with open(filepath, 'r') as f:
                self.benchmarks = json.load(f)
        elif ext.lower() == '.csv':
            df = pd.read_csv(filepath)
            self._parse_benchmark_csv(df)
        else:
            raise ValueError(f"Unsupported file format: {ext}")
    
    def save_benchmarks(self, filepath: str) -> None:
        """Save benchmarks to file.
        
        Args:
            filepath: Path to save benchmark file
        """
        _, ext = os.path.splitext(filepath)
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
        
        if ext.lower() == '.json':
            with open(filepath, 'w') as f:
                json.dump(self.benchmarks, f, indent=2)
        elif ext.lower() == '.csv':
            df = self._convert_to_dataframe()
            df.to_csv(filepath, index=False)
        else:
            raise ValueError(f"Unsupported file format: {ext}")
    
    def _parse_benchmark_csv(self, df: pd.DataFrame) -> None:
        """Parse benchmark data from DataFrame.
        
        Args:
            df: DataFrame containing benchmark data
        """
        # Expected columns:
        # metric, gender, age, value
        
        for _, row in df.iterrows():
            metric = row['metric']
            gender = row['gender']
            age = int(row['age'])
            value = float(row['value'])
            
            if metric == 'early_adenoma_prevalence':
                self.set_early_adenoma_prevalence(gender, age, value)
            elif metric == 'advanced_adenoma_prevalence':
                self.set_advanced_adenoma_prevalence(gender, age, value)
            elif metric == 'preclinical_dwell_time':
                self.set_preclinical_dwell_time(value)
            elif metric == 'cancer_incidence':
                self.set_cancer_incidence(gender, age, value)
            elif metric == 'cancer_mortality':
                self.set_cancer_mortality(gender, age, value)
            elif metric == 'rectal_cancer_fraction':
                self.set_rectal_cancer_fraction(gender, age, value)
    
    def _convert_to_dataframe(self) -> pd.DataFrame:
        """Convert benchmarks to DataFrame.
        
        Returns:
            DataFrame containing benchmark data
        """
        data = []
        
        # Early adenoma prevalence
        for gender in ['M', 'F']:
            for age, value in self.benchmarks['early_adenoma_prevalence'][gender].items():
                data.append({
                    'metric': 'early_adenoma_prevalence',
                    'gender': gender,
                    'age': age,
                    'value': value
                })
        
        # Advanced adenoma prevalence
        for gender in ['M', 'F']:
            for age, value in self.benchmarks['advanced_adenoma_prevalence'][gender].items():
                data.append({
                    'metric': 'advanced_adenoma_prevalence',
                    'gender': gender,
                    'age': age,
                    'value': value
                })
        
        # Preclinical dwell time
        data.append({
            'metric': 'preclinical_dwell_time',
            'gender': 'All',
            'age': 0,
            'value': self.benchmarks['preclinical_dwell_time']
        })
        
        # Cancer incidence
        for gender in ['M', 'F']:
            for age, value in self.benchmarks['cancer_incidence'][gender].items():
                data.append({
                    'metric': 'cancer_incidence',
                    'gender': gender,
                    'age': age,
                    'value': value
                })
        
        # Cancer mortality
        for gender in ['M', 'F']:
            for age, value in self.benchmarks['cancer_mortality'][gender].items():
                data.append({
                    'metric': 'cancer_mortality',
                    'gender': gender,
                    'age': age,
                    'value': value
                })
        
        # Rectal cancer fraction
        for gender in ['M', 'F']:
            for age, value in self.benchmarks['rectal_cancer_fraction'][gender].items():
                data.append({
                    'metric': 'rectal_cancer_fraction',
                    'gender': gender,
                    'age': age,
                    'value': value
                })
        
        return pd.DataFrame(data)
    
    def import_from_matlab(self, filepath: str) -> None:
        """Import benchmarks from MATLAB file.
        
        Args:
            filepath: Path to MATLAB file
        """
        from scipy.io import loadmat
        
        # Load MATLAB file
        mat_data = loadmat(filepath)
        
        # Extract benchmark data
        if 'Variables' in mat_data and 'Benchmarks' in mat_data['Variables']:
            benchmarks = mat_data['Variables']['Benchmarks']
            
            # Early adenoma prevalence
            if 'EarlyPolyp' in benchmarks:
                early_polyp = benchmarks['EarlyPolyp']
                
                # Male prevalence
                if 'Male_y' in early_polyp and 'Male_perc' in early_polyp:
                    male_ages = early_polyp['Male_y'].flatten()
                    male_prev = early_polyp['Male_perc'].flatten()
                    
                    for age, prev in zip(male_ages, male_prev):
                        self.set_early_adenoma_prevalence('M', int(age), float(prev))
                
                # Female prevalence
                if 'Female_y' in early_polyp and 'Female_perc' in early_polyp:
                    female_ages = early_polyp['Female_y'].flatten()
                    female_prev = early_polyp['Female_perc'].flatten()
                    
                    for age, prev in zip(female_ages, female_prev):
                        self.set_early_adenoma_prevalence('F', int(age), float(prev))
            
            # Advanced adenoma prevalence
            if 'AdvPolyp' in benchmarks:
                adv_polyp = benchmarks['AdvPolyp']
                
                # Male prevalence
                if 'Male_y' in adv_polyp and 'Male_perc' in adv_polyp:
                    male_ages = adv_polyp['Male_y'].flatten()
                    male_prev = adv_polyp['Male_perc'].flatten()
                    
                    for age, prev in zip(male_ages, male_prev):
                        self.set_advanced_adenoma_prevalence('M', int(age), float(prev))
                
                # Female prevalence
                if 'Female_y' in adv_polyp and 'Female_perc' in adv_polyp:
                    female_ages = adv_polyp['Female_y'].flatten()
                    female_prev = adv_polyp['Female_perc'].flatten()
                    
                    for age, prev in zip(female_ages, female_prev):
                        self.set_advanced_adenoma_prevalence('F', int(age), float(prev))
            
            # Cancer incidence
            if 'Cancer' in benchmarks:
                cancer = benchmarks['Cancer']
                
                # Male incidence
                if 'Male_y' in cancer and 'Male_inc' in cancer:
                    male_ages = cancer['Male_y'].flatten()
                    male_inc = cancer['Male_inc'].flatten()
                    
                    for age, inc in zip(male_ages, male_inc):
                        self.set_cancer_incidence('M', int(age), float(inc))
                
                # Female incidence
                if 'Female_y' in cancer and 'Female_inc' in cancer:
                    female_ages = cancer['Female_y'].flatten()
                    female_inc = cancer['Female_inc'].flatten()
                    
                    for age, inc in zip(female_ages, female_inc):
                        self.set_cancer_incidence('F', int(age), float(inc))
                
                # Male mortality
                if 'Male_y' in cancer and 'Male_mort' in cancer:
                    male_ages = cancer['Male_y'].flatten()
                    male_mort = cancer['Male_mort'].flatten()
                    
                    for age, mort in zip(male_ages, male_mort):
                        self.set_cancer_mortality('M', int(age), float(mort))
                
                # Female mortality
                if 'Female_y' in cancer and 'Female_mort' in cancer:
                    female_ages = cancer['Female_y'].flatten()
                    female_mort = cancer['Female_mort'].flatten()
                    
                    for age, mort in zip(female_ages, female_mort):
                        self.set_cancer_mortality('F', int(age), float(mort))
                
                # Rectal cancer fraction
                if 'LocationRectumMale' in cancer and 'LocationRectumFemale' in cancer and 'LocationRectumYear' in cancer:
                    male_fractions = cancer['LocationRectumMale'].flatten()
                    female_fractions = cancer['LocationRectumFemale'].flatten()
                    
                    # Extract age ranges
                    age_ranges = []
                    for age_range in cancer['LocationRectumYear']:
                        if isinstance(age_range, np.ndarray):
                            # Take middle of range
                            age = int(np.mean(age_range))
                            age_ranges.append(age)
                    
                    # Set male fractions
                    for age, fraction in zip(age_ranges, male_fractions):
                        self.set_rectal_cancer_fraction('M', age, float(fraction))
                    
                    # Set female fractions
                    for age, fraction in zip(age_ranges, female_fractions):
                        self.set_rectal_cancer_fraction('F', age, float(fraction))
    
    def export_to_matlab(self, filepath: str) -> None:
        """Export benchmarks to MATLAB file.
        
        Args:
            filepath: Path to save MATLAB file
        """
        from scipy.io import savemat
        
        # Create MATLAB-compatible structure
        matlab_data = {
            'Variables': {
                'Benchmarks': {
                    'EarlyPolyp': {
                        'Male_y': [],
                        'Male_perc': [],
                        'Female_y': [],
                        'Female_perc': []
                    },
                    'AdvPolyp': {
                        'Male_y': [],
                        'Male_perc': [],
                        'Female_y': [],
                        'Female_perc': []
                    },
                    'Cancer': {
                        'Male_y': [],
                        'Male_inc': [],
                        'Female_y': [],
                        'Female_inc': [],
                        'Male_mort': [],
                        'Female_mort': [],
                        'LocationRectumMale': [],
                        'LocationRectumFemale': [],
                        'LocationRectumYear': []
                    }
                }
            }
        }
        
        # Early adenoma prevalence
        male_early_ages = sorted(self.benchmarks['early_adenoma_prevalence']['M'].keys())
        male_early_prev = [self.benchmarks['early_adenoma_prevalence']['M'][age] for age in male_early_ages]
        
        female_early_ages = sorted(self.benchmarks['early_adenoma_prevalence']['F'].keys())
        female_early_prev = [self.benchmarks['early_adenoma_prevalence']['F'][age] for age in female_early_ages]
        
        matlab_data['Variables']['Benchmarks']['EarlyPolyp']['Male_y'] = male_early_ages
        matlab_data['Variables']['Benchmarks']['EarlyPolyp']['Male_perc'] = male_early_prev
        matlab_data['Variables']['Benchmarks']['EarlyPolyp']['Female_y'] = female_early_ages
        matlab_data['Variables']['Benchmarks']['EarlyPolyp']['Female_perc'] = female_early_prev
        
        # Advanced adenoma prevalence
        male_adv_ages = sorted(self.benchmarks['advanced_adenoma_prevalence']['M'].keys())
        male_adv_prev = [self.benchmarks['advanced_adenoma_prevalence']['M'][age] for age in male_adv_ages]
        
        female_adv_ages = sorted(self.benchmarks['advanced_adenoma_prevalence']['F'].keys())
        female_adv_prev = [self.benchmarks['advanced_adenoma_prevalence']['F'][age] for age in female_adv_ages]
        
        matlab_data['Variables']['Benchmarks']['AdvPolyp']['Male_y'] = male_adv_ages
        matlab_data['Variables']['Benchmarks']['AdvPolyp']['Male_perc'] = male_adv_prev
        matlab_data['Variables']['Benchmarks']['AdvPolyp']['Female_y'] = female_adv_ages
        matlab_data['Variables']['Benchmarks']['AdvPolyp']['Female_perc'] = female_adv_prev
        
        # Cancer incidence
        male_inc_ages = sorted(self.benchmarks['cancer_incidence']['M'].keys())
        male_inc = [self.benchmarks['cancer_incidence']['M'][age] for age in male_inc_ages]
        
        female_inc_ages = sorted(self.benchmarks['cancer_incidence']['F'].keys())
        female_inc = [self.benchmarks['cancer_incidence']['F'][age] for age in female_inc_ages]
        
        matlab_data['Variables']['Benchmarks']['Cancer']['Male_y'] = male_inc_ages
        matlab_data['Variables']['Benchmarks']['Cancer']['Male_inc'] = male_inc
        matlab_data['Variables']['Benchmarks']['Cancer']['Female_y'] = female_inc_ages
        matlab_data['Variables']['Benchmarks']['Cancer']['Female_inc'] = female_inc
        
        # Cancer mortality
        male_mort_ages = sorted(self.benchmarks['cancer_mortality']['M'].keys())
        male_mort = [self.benchmarks['cancer_mortality']['M'][age] for age in male_mort_ages]
        
        female_mort_ages = sorted(self.benchmarks['cancer_mortality']['F'].keys())
        female_mort = [self.benchmarks['cancer_mortality']['F'][age] for age in female_mort_ages]
        
        matlab_data['Variables']['Benchmarks']['Cancer']['Male_mort'] = male_mort
        matlab_data['Variables']['Benchmarks']['Cancer']['Female_mort'] = female_mort
        
        # Rectal cancer fraction
        male_rect_ages = sorted(self.benchmarks['rectal_cancer_fraction']['M'].keys())
        male_rect = [self.benchmarks['rectal_cancer_fraction']['M'][age] for age in male_rect_ages]
        
        female_rect_ages = sorted(self.benchmarks['rectal_cancer_fraction']['F'].keys())
        female_rect = [self.benchmarks['rectal_cancer_fraction']['F'][age] for age in female_rect_ages]
        
        matlab_data['Variables']['Benchmarks']['Cancer']['LocationRectumMale'] = male_rect
        matlab_data['Variables']['Benchmarks']['Cancer']['LocationRectumFemale'] = female_rect
        matlab_data['Variables']['Benchmarks']['Cancer']['LocationRectumYear'] = [[age] for age in male_rect_ages]
        
        # Save to MATLAB file
        savemat(filepath, matlab_data)
    
    def load_default_benchmarks(self) -> None:
        """Load default benchmark values."""
        # Early adenoma prevalence
        for gender in ['M', 'F']:
            for age in range(20, 81, 10):
                # Default values based on literature
                if gender == 'M':
                    # Male prevalence increases with age
                    prevalence = max(0, (age - 20) * 0.8)
                else:
                    # Female prevalence slightly lower
                    prevalence = max(0, (age - 20) * 0.6)
                
                self.set_early_adenoma_prevalence(gender, age, prevalence)
        
        # Advanced adenoma prevalence
        for gender in ['M', 'F']:
            for age in range(20, 81, 10):
                # Default values based on literature
                if gender == 'M':
                    # Male prevalence increases with age
                    prevalence = max(0, (age - 30) * 0.2)
                else:
                    # Female prevalence slightly lower
                    prevalence = max(0, (age - 30) * 0.15)
                
                self.set_advanced_adenoma_prevalence(gender, age, prevalence)
        
        # Preclinical dwell time
        self.set_preclinical_dwell_time(3.0)
        
        # Cancer incidence
        for gender in ['M', 'F']:
            for age in range(30, 81, 10):
                # Default values based on literature
                if gender == 'M':
                    # Male incidence increases with age
                    incidence = max(0, (age - 30) * 5)
                else:
                    # Female incidence slightly lower
                    incidence = max(0, (age - 30) * 4)
                
                self.set_cancer_incidence(gender, age, incidence)
        
        # Cancer mortality
        for gender in ['M', 'F']:
            for age in range(30, 81, 10):
                # Default values based on literature
                if gender == 'M':
                    # Male mortality increases with age
                    mortality = max(0, (age - 30) * 2)
                else:
                    # Female mortality slightly lower
                    mortality = max(0, (age - 30) * 1.6)
                
                self.set_cancer_mortality(gender, age, mortality)
        
        # Rectal cancer fraction
        for gender in ['M', 'F']:
            for age in range(50, 81, 10):
                # Default values based on literature
                if gender == 'M':
                    # Male rectal fraction decreases with age
                    fraction = max(15, 40 - (age - 50) * 0.5)
                else:
                    # Female rectal fraction slightly lower
                    fraction = max(12, 35 - (age - 50) * 0.5)
                
                self.set_rectal_cancer_fraction(gender, age, fraction)

def create_benchmark_template(output_path: str) -> None:
    """Create a template CSV file for benchmark data.
    
    Args:
        output_path: Path to save template file
    """
    # Create template data
    data = []
    
    # Early adenoma prevalence
    for gender in ['M', 'F']:
        for age in range(20, 81, 10):
            data.append({
                'metric': 'early_adenoma_prevalence',
                'gender': gender,
                'age': age,
                'value': 0.0
            })
    
    # Advanced adenoma prevalence
    for gender in ['M', 'F']:
        for age in range(20, 81, 10):
            data.append({
                'metric': 'advanced_adenoma_prevalence',
                'gender': gender,
                'age': age,
                'value': 0.0
            })
    
    # Preclinical dwell time
    data.append({
        'metric': 'preclinical_dwell_time',
        'gender': 'All',
        'age': 0,
        'value': 3.0
    })
    
    # Cancer incidence
    for gender in ['M', 'F']:
        for age in range(30, 81, 10):
            data.append({
                'metric': 'cancer_incidence',
                'gender': gender,
                'age': age,
                'value': 0.0
            })
    
    # Cancer mortality
    for gender in ['M', 'F']:
        for age in range(30, 81, 10):
            data.append({
                'metric': 'cancer_mortality',
                'gender': gender,
                'age': age,
                'value': 0.0
            })
    
    # Rectal cancer fraction
    for gender in ['M', 'F']:
        for age in range(50, 81, 10):
            data.append({
                'metric': 'rectal_cancer_fraction',
                'gender': gender,
                'age': age,
                'value': 0.0
            })
    
    # Create DataFrame and save to CSV
    df = pd.DataFrame(data)
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
    
    # Save to CSV
    df.to_csv(output_path, index=False)
    print(f"Benchmark template created at {output_path}")

def load_benchmarks_from_file(filepath: str) -> Dict[str, Any]:
    """Load benchmarks from file and convert to format expected by calibration.
    
    Args:
        filepath: Path to benchmark file
        
    Returns:
        Dictionary of benchmark metrics in format expected by calibration
    """
    # Create benchmark manager and load file
    bm = BenchmarkManager()
    bm.load_benchmarks(filepath)
    
    # Convert to format expected by calibration
    metrics = {}
    
    # Early adenoma prevalence by age and gender
    for gender in ['M', 'F']:
        for age, value in bm.benchmarks['early_adenoma_prevalence'][gender].items():
            key = f'early_prev_{gender}_{age}'
            metrics[key] = value
    
    # Advanced adenoma prevalence by age and gender
    for gender in ['M', 'F']:
        for age, value in bm.benchmarks['advanced_adenoma_prevalence'][gender].items():
            key = f'adv_prev_{gender}_{age}'
            metrics[key] = value
    
    # Preclinical dwell time
    metrics['preclinical_dwell_time'] = bm.benchmarks['preclinical_dwell_time']
    
    # Cancer incidence by age and gender
    for gender in ['M', 'F']:
        for age, value in bm.benchmarks['cancer_incidence'][gender].items():
            key = f'cancer_inc_{gender}_{age}'
            metrics[key] = value
    
    # Cancer mortality by age and gender
    for gender in ['M', 'F']:
        for age, value in bm.benchmarks['cancer_mortality'][gender].items():
            key = f'cancer_mort_{gender}_{age}'
            metrics[key] = value
    
    # Rectal cancer fraction by age and gender
    for gender in ['M', 'F']:
        for age, value in bm.benchmarks['rectal_cancer_fraction'][gender].items():
            key = f'rectal_frac_{gender}_{age}'
            metrics[key] = value
    
    return metrics

def convert_to_benchmark_format(metrics: Dict[str, float]) -> Dict[str, Any]:
    """Convert metrics from calibration format to BenchmarkManager format.
    
    Args:
        metrics: Dictionary of metrics in calibration format
        
    Returns:
        Dictionary in BenchmarkManager format
    """
    benchmarks = {
        'early_adenoma_prevalence': {'M': {}, 'F': {}},
        'advanced_adenoma_prevalence': {'M': {}, 'F': {}},
        'preclinical_dwell_time': 3.0,
        'cancer_incidence': {'M': {}, 'F': {}},
        'cancer_mortality': {'M': {}, 'F': {}},
        'rectal_cancer_fraction': {'M': {}, 'F': {}}
    }
    
    # Process each metric
    for key, value in metrics.items():
        parts = key.split('_')
        
        if key == 'preclinical_dwell_time':
            benchmarks['preclinical_dwell_time'] = value
            continue
            
        if len(parts) < 3:
            continue
            
        metric_type = parts[0]
        if metric_type == 'early' and parts[1] == 'prev':
            gender = parts[2]
            age = int(parts[3])
            benchmarks['early_adenoma_prevalence'][gender][age] = value
        elif metric_type == 'adv' and parts[1] == 'prev':
            gender = parts[2]
            age = int(parts[3])
            benchmarks['advanced_adenoma_prevalence'][gender][age] = value
        elif metric_type == 'cancer' and parts[1] == 'inc':
            gender = parts[2]
            age = int(parts[3])
            benchmarks['cancer_incidence'][gender][age] = value
        elif metric_type == 'cancer' and parts[1] == 'mort':
            gender = parts[2]
            age = int(parts[3])
            benchmarks['cancer_mortality'][gender][age] = value
        elif metric_type == 'rectal' and parts[1] == 'frac':
            gender = parts[2]
            age = int(parts[3])
            benchmarks['rectal_cancer_fraction'][gender][age] = value
    
    return benchmarks

def save_benchmarks_to_file(metrics: Dict[str, float], filepath: str) -> None:
    """Save metrics to benchmark file.
    
    Args:
        metrics: Dictionary of metrics in calibration format
        filepath: Path to save benchmark file
    """
    # Convert to BenchmarkManager format
    benchmark_data = convert_to_benchmark_format(metrics)
    
    # Create BenchmarkManager and set data
    bm = BenchmarkManager()
    bm.benchmarks = benchmark_data
    
    # Save to file
    bm.save_benchmarks(filepath)

def import_matlab_benchmarks(matlab_file: str, output_file: str) -> None:
    """Import benchmarks from MATLAB file and save to new format.
    
    Args:
        matlab_file: Path to MATLAB file
        output_file: Path to save benchmark file
    """
    bm = BenchmarkManager()
    bm.import_from_matlab(matlab_file)
    bm.save_benchmarks(output_file)
    print(f"Benchmarks imported from {matlab_file} and saved to {output_file}")

def export_to_matlab_benchmarks(input_file: str, matlab_file: str) -> None:
    """Export benchmarks to MATLAB file.
    
    Args:
        input_file: Path to benchmark file
        matlab_file: Path to save MATLAB file
    """
    bm = BenchmarkManager()
    bm.load_benchmarks(input_file)
    bm.export_to_matlab(matlab_file)
    print(f"Benchmarks exported from {input_file} to MATLAB file {matlab_file}")

def compare_benchmarks(file1: str, file2: str) -> Dict[str, Dict[str, float]]:
    """Compare two benchmark files and return differences.
    
    Args:
        file1: Path to first benchmark file
        file2: Path to second benchmark file
        
    Returns:
        Dictionary of differences
    """
    bm1 = BenchmarkManager()
    bm1.load_benchmarks(file1)
    
    bm2 = BenchmarkManager()
    bm2.load_benchmarks(file2)
    
    # Convert to calibration format for easier comparison
    metrics1 = load_benchmarks_from_file(file1)
    metrics2 = load_benchmarks_from_file(file2)
    
    # Find differences
    differences = {}
    
    for key in set(metrics1.keys()).union(set(metrics2.keys())):
        if key not in metrics1:
            differences[key] = {'file1': None, 'file2': metrics2[key]}
        elif key not in metrics2:
            differences[key] = {'file1': metrics1[key], 'file2': None}
        elif metrics1[key] != metrics2[key]:
            differences[key] = {'file1': metrics1[key], 'file2': metrics2[key]}
    
    return differences

def print_benchmark_summary(filepath: str) -> None:
    """Print summary of benchmark file.
    
    Args:
        filepath: Path to benchmark file
    """
    bm = BenchmarkManager()
    bm.load_benchmarks(filepath)
    
    print(f"Benchmark Summary for {filepath}")
    print("-" * 50)
    
    # Early adenoma prevalence
    print("Early Adenoma Prevalence:")
    print("  Male:")
    for age in sorted(bm.benchmarks['early_adenoma_prevalence']['M'].keys()):
        print(f"    Age {age}: {bm.benchmarks['early_adenoma_prevalence']['M'][age]:.2f}%")
    print("  Female:")
    for age in sorted(bm.benchmarks['early_adenoma_prevalence']['F'].keys()):
        print(f"    Age {age}: {bm.benchmarks['early_adenoma_prevalence']['F'][age]:.2f}%")
    
    # Advanced adenoma prevalence
    print("\nAdvanced Adenoma Prevalence:")
    print("  Male:")
    for age in sorted(bm.benchmarks['advanced_adenoma_prevalence']['M'].keys()):
        print(f"    Age {age}: {bm.benchmarks['advanced_adenoma_prevalence']['M'][age]:.2f}%")
    print("  Female:")
    for age in sorted(bm.benchmarks['advanced_adenoma_prevalence']['F'].keys()):
        print(f"    Age {age}: {bm.benchmarks['advanced_adenoma_prevalence']['F'][age]:.2f}%")
    
    # Preclinical dwell time
    print(f"\nPreclinical Dwell Time: {bm.benchmarks['preclinical_dwell_time']:.2f} years")
    
    # Cancer incidence
    print("\nCancer Incidence (per 100,000):")
    print("  Male:")
    for age in sorted(bm.benchmarks['cancer_incidence']['M'].keys()):
        print(f"    Age {age}: {bm.benchmarks['cancer_incidence']['M'][age]:.2f}")
    print("  Female:")
    for age in sorted(bm.benchmarks['cancer_incidence']['F'].keys()):
        print(f"    Age {age}: {bm.benchmarks['cancer_incidence']['F'][age]:.2f}")
    
    # Cancer mortality
    print("\nCancer Mortality (per 100,000):")
    print("  Male:")
    for age in sorted(bm.benchmarks['cancer_mortality']['M'].keys()):
        print(f"    Age {age}: {bm.benchmarks['cancer_mortality']['M'][age]:.2f}")
    print("  Female:")
    for age in sorted(bm.benchmarks['cancer_mortality']['F'].keys()):
        print(f"    Age {age}: {bm.benchmarks['cancer_mortality']['F'][age]:.2f}")
    
    # Rectal cancer fraction
    print("\nRectal Cancer Fraction (%):")
    print("  Male:")
    for age in sorted(bm.benchmarks['rectal_cancer_fraction']['M'].keys()):
        print(f"    Age {age}: {bm.benchmarks['rectal_cancer_fraction']['M'][age]:.2f}%")
    print("  Female:")
    for age in sorted(bm.benchmarks['rectal_cancer_fraction']['F'].keys()):
        print(f"    Age {age}: {bm.benchmarks['rectal_cancer_fraction']['F'][age]:.2f}%")

def plot_benchmarks(filepath: str, output_dir: str = None) -> None:
    """Plot benchmark data.
    
    Args:
        filepath: Path to benchmark file
        output_dir: Directory to save plots (optional)
    """
    import matplotlib.pyplot as plt
    
    bm = BenchmarkManager()
    bm.load_benchmarks(filepath)
    
    # Create output directory if specified
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # Plot early adenoma prevalence
    plt.figure(figsize=(10, 6))
    
    male_early_ages = sorted(bm.benchmarks['early_adenoma_prevalence']['M'].keys())
    male_early_prev = [bm.benchmarks['early_adenoma_prevalence']['M'][age] for age in male_early_ages]
    
    female_early_ages = sorted(bm.benchmarks['early_adenoma_prevalence']['F'].keys())
    female_early_prev = [bm.benchmarks['early_adenoma_prevalence']['F'][age] for age in female_early_ages]
    
    plt.plot(male_early_ages, male_early_prev, 'b-o', label='Male')
    plt.plot(female_early_ages, female_early_prev, 'r-o', label='Female')
    
    plt.title('Early Adenoma Prevalence by Age and Gender')
    plt.xlabel('Age (years)')
    plt.ylabel('Prevalence (%)')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'early_adenoma_prevalence.png'), dpi=300, bbox_inches='tight')
    else:
        plt.show()
    
    # Plot advanced adenoma prevalence
    plt.figure(figsize=(10, 6))
    
    male_adv_ages = sorted(bm.benchmarks['advanced_adenoma_prevalence']['M'].keys())
    male_adv_prev = [bm.benchmarks['advanced_adenoma_prevalence']['M'][age] for age in male_adv_ages]
    
    female_adv_ages = sorted(bm.benchmarks['advanced_adenoma_prevalence']['F'].keys())
    female_adv_prev = [bm.benchmarks['advanced_adenoma_prevalence']['F'][age] for age in female_adv_ages]
    
    plt.plot(male_adv_ages, male_adv_prev, 'b-o', label='Male')
    plt.plot(female_adv_ages, female_adv_prev, 'r-o', label='Female')
    
    plt.title('Advanced Adenoma Prevalence by Age and Gender')
    plt.xlabel('Age (years)')
    plt.ylabel('Prevalence (%)')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'advanced_adenoma_prevalence.png'), dpi=300, bbox_inches='tight')
    else:
        plt.show()
    
    # Plot cancer incidence
    plt.figure(figsize=(10, 6))
    
    male_inc_ages = sorted(bm.benchmarks['cancer_incidence']['M'].keys())
    male_inc = [bm.benchmarks['cancer_incidence']['M'][age] for age in male_inc_ages]
    
    female_inc_ages = sorted(bm.benchmarks['cancer_incidence']['F'].keys())
    female_inc = [bm.benchmarks['cancer_incidence']['F'][age] for age in female_inc_ages]
    
    plt.plot(male_inc_ages, male_inc, 'b-o', label='Male')
    plt.plot(female_inc_ages, female_inc, 'r-o', label='Female')
    
    plt.title('Cancer Incidence by Age and Gender (per 100,000)')
    plt.xlabel('Age (years)')
    plt.ylabel('Incidence (per 100,000)')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'cancer_incidence.png'), dpi=300, bbox_inches='tight')
    else:
        plt.show()
    
    # Plot cancer mortality
    plt.figure(figsize=(10, 6))
    
    male_mort_ages = sorted(bm.benchmarks['cancer_mortality']['M'].keys())
    male_mort = [bm.benchmarks['cancer_mortality']['M'][age] for age in male_mort_ages]
    
    female_mort_ages = sorted(bm.benchmarks['cancer_mortality']['F'].keys())
    female_mort = [bm.benchmarks['cancer_mortality']['F'][age] for age in female_mort_ages]
    
    plt.plot(male_mort_ages, male_mort, 'b-o', label='Male')
    plt.plot(female_mort_ages, female_mort, 'r-o', label='Female')
    
    plt.title('Cancer Mortality by Age and Gender (per 100,000)')
    plt.xlabel('Age (years)')
    plt.ylabel('Mortality (per 100,000)')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'cancer_mortality.png'), dpi=300, bbox_inches='tight')
    else:
        plt.show()
    
    # Plot rectal cancer fraction
    plt.figure(figsize=(10, 6))
    
    male_rect_ages = sorted(bm.benchmarks['rectal_cancer_fraction']['M'].keys())
    male_rect = [bm.benchmarks['rectal_cancer_fraction']['M'][age] for age in male_rect_ages]
    
    female_rect_ages = sorted(bm.benchmarks['rectal_cancer_fraction']['F'].keys())
    female_rect = [bm.benchmarks['rectal_cancer_fraction']['F'][age] for age in female_rect_ages]
    
    plt.plot(male_rect_ages, male_rect, 'b-o', label='Male')
    plt.plot(female_rect_ages, female_rect, 'r-o', label='Female')
    
    plt.title('Rectal Cancer Fraction by Age and Gender (%)')
    plt.xlabel('Age (years)')
    plt.ylabel('Rectal Cancer Fraction (%)')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'rectal_cancer_fraction.png'), dpi=300, bbox_inches='tight')
    else:
        plt.show()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='CMOST Benchmark Management Tool')
    subparsers = parser.add_subparsers(dest='command', help='Command to execute')
    
    # Create template command
    create_parser = subparsers.add_parser('create', help='Create benchmark template')
    create_parser.add_argument('output', help='Output file path')
    
    # Import from MATLAB command
    import_parser = subparsers.add_parser('import', help='Import benchmarks from MATLAB')
    import_parser.add_argument('matlab_file', help='MATLAB file path')
    import_parser.add_argument('output', help='Output file path')
    
    # Export to MATLAB command
    export_parser = subparsers.add_parser('export', help='Export benchmarks to MATLAB')
    export_parser.add_argument('input', help='Input file path')
    export_parser.add_argument('matlab_file', help='MATLAB file path')
    
    # Compare command
    compare_parser = subparsers.add_parser('compare', help='Compare two benchmark files')
    compare_parser.add_argument('file1', help='First file path')
    compare_parser.add_argument('file2', help='Second file path')
    
    # Summary command
    summary_parser = subparsers.add_parser('summary', help='Print benchmark summary')
    summary_parser.add_argument('file', help='Benchmark file path')
    
    # Plot command
    plot_parser = subparsers.add_parser('plot', help='Plot benchmark data')
    plot_parser.add_argument('file', help='Benchmark file path')
    plot_parser.add_argument('--output', help='Output directory for plots')
    
    args = parser.parse_args()
    
    if args.command == 'create':
        create_benchmark_template(args.output)
    elif args.command == 'import':
        import_matlab_benchmarks(args.matlab_file, args.output)
    elif args.command == 'export':
        export_to_matlab_benchmarks(args.input, args.matlab_file)
    elif args.command == 'compare':
        differences = compare_benchmarks(args.file1, args.file2)
        if differences:
            print(f"Found {len(differences)} differences between {args.file1} and {args.file2}:")
            for key, diff in differences.items():
                print(f"  {key}: {diff['file1']} vs {diff['file2']}")
        else:
            print(f"No differences found between {args.file1} and {args.file2}")
    elif args.command == 'summary':
        print_benchmark_summary(args.file)
    elif args.command == 'plot':
        plot_benchmarks(args.file, args.output)
    else:
        parser.print_help()


def load_benchmarks() -> Dict[str, Any]:
    """Load default benchmarks for calibration.

    Returns:
        Dict[str, Any]: Dictionary containing benchmark data
    """
    # Create a default benchmark manager and return its data
    manager = BenchmarkManager()
    return manager.benchmarks
