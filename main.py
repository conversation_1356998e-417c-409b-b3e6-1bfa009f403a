#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CMOST (Colorectal Microsimulation Outcomes Screening Tool) - 统一入口文件

这是CMOST项目的统一入口文件，提供多种启动方式：
1. 命令行界面 (CLI)
2. 图形用户界面 (GUI)
3. 交互式Python环境
4. 快速仿真示例

使用方法:
    python main.py                    # 显示帮助信息
    python main.py --cli              # 启动命令行界面
    python main.py --gui              # 启动图形界面
    python main.py --interactive      # 启动交互式环境
    python main.py --demo             # 运行快速演示
    python main.py --version          # 显示版本信息

作者: <PERSON><PERSON>, <PERSON><PERSON>, et al.
版本: 1.0.0
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    import cmost
    from cmost import __version__, __author__
    from cmost.config import settings, initialize_settings
except ImportError as e:
    print(f"错误: 无法导入CMOST模块: {e}")
    print("请确保已正确安装CMOST包:")
    print("  pip install -e .")
    sys.exit(1)


def setup_logging(verbose=False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def show_banner():
    """显示CMOST横幅信息"""
    banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║   ██████╗███╗   ███╗ ██████╗ ███████╗████████╗                               ║
║  ██╔════╝████╗ ████║██╔═══██╗██╔════╝╚══██╔══╝                               ║
║  ██║     ██╔████╔██║██║   ██║███████╗   ██║                                  ║
║  ██║     ██║╚██╔╝██║██║   ██║╚════██║   ██║                                  ║
║  ╚██████╗██║ ╚═╝ ██║╚██████╔╝███████║   ██║                                  ║
║   ╚═════╝╚═╝     ╚═╝ ╚═════╝ ╚══════╝   ╚═╝                                  ║
║                                                                              ║
║  Colorectal Microsimulation Outcomes Screening Tool in China                 ║
║  中国结直肠癌微观仿真筛查结果工具                                                 ║
║                                                                              ║
║  版本: {__version__:<20} 作者: {__author__:<30} ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def launch_cli():
    """启动命令行界面"""
    print("启动CMOST命令行界面...")
    print("使用 'cmost --help' 查看所有可用命令")
    print("\n常用命令:")
    print("  cmost simulate --patients 10000 --output results.json")
    print("  cmost compare --strategies colonoscopy,fit --output comparison.json")
    print("  cmost calibrate --target-data data.csv --output params.json")
    print("\n要直接使用CLI，请运行: cmost")
    
    # 导入并启动CLI
    from cmost.cli import main as cli_main
    try:
        cli_main()
    except SystemExit:
        pass


def launch_gui():
    """启动图形用户界面"""
    print("启动CMOST图形用户界面...")

    try:
        import tkinter as tk
        from tkinter import ttk, messagebox

        # 创建简化的GUI
        root = tk.Tk()
        root.title("CMOST - 结直肠癌筛查仿真工具")
        root.geometry("800x600")

        # 设置窗口图标（如果存在）
        try:
            icon_path = project_root / "cmost" / "resources" / "icon.ico"
            if icon_path.exists():
                root.iconbitmap(str(icon_path))
        except Exception:
            pass

        # 创建主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(
            main_frame,
            text="CMOST - 中国结直肠癌微观仿真筛查结果工具",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))

        # 功能按钮
        def show_demo():
            messagebox.showinfo("演示", "请使用命令行运行: python main.py --demo")

        def launch_simulation():
            """启动增强的仿真界面"""
            try:
                # 导入并启动增强UI
                from cmost.ui.enhanced_main_window import EnhancedMainWindow

                # 创建新窗口
                sim_window = tk.Toplevel(root)
                sim_window.title("CMOST - 结直肠癌筛查仿真工具")
                sim_window.geometry("1200x800")

                # 居中显示
                sim_window.update_idletasks()
                x = (sim_window.winfo_screenwidth() // 2) - (1200 // 2)
                y = (sim_window.winfo_screenheight() // 2) - (800 // 2)
                sim_window.geometry(f"1200x800+{x}+{y}")

                # 设置最小尺寸
                sim_window.minsize(1000, 600)

                # 创建增强主窗口
                app = EnhancedMainWindow(sim_window)

                print("CMOST增强仿真界面已启动")

            except ImportError as e:
                messagebox.showerror(
                    "导入错误",
                    f"无法启动增强仿真界面:\n{e}\n\n请确保所有依赖项已正确安装"
                )
            except Exception as e:
                messagebox.showerror(
                    "启动错误",
                    f"启动增强仿真界面时发生错误:\n{e}"
                )

        def show_cli_help():
            messagebox.showinfo(
                "命令行帮助",
                "常用命令:\n"
                "• python main.py --cli\n"
                "• python main.py --demo\n"
                "• python main.py --interactive\n"
                "• python -m cmost.cli simulate --patients 10000"
            )

        def show_config_help():
            messagebox.showinfo(
                "配置帮助",
                "支持的配置格式:\n"
                "• JSON (.json)\n"
                "• YAML (.yaml)\n"
                "• Excel (.xlsx) - 新功能!\n"
                "• MATLAB (.mat)\n\n"
                "生成Excel模板:\n"
                "python -m cmost.cli config --template excel --output config.xlsx"
            )

        def show_about():
            messagebox.showinfo(
                "关于CMOST",
                f"CMOST v{__version__}\n"
                f"作者: {__author__}\n\n"
                "结直肠癌微观仿真筛查结果工具\n"
                "支持双重架构、多筛查工具组合、\n"
                "非固定筛查周期和机器学习校准功能"
            )

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)

        # 功能按钮
        ttk.Button(button_frame, text="运行演示", command=show_demo, width=20).pack(pady=5)
        ttk.Button(button_frame, text="运行模拟", command=launch_simulation, width=20).pack(pady=5)
        ttk.Button(button_frame, text="命令行帮助", command=show_cli_help, width=20).pack(pady=5)
        ttk.Button(button_frame, text="配置帮助", command=show_config_help, width=20).pack(pady=5)
        ttk.Button(button_frame, text="关于CMOST", command=show_about, width=20).pack(pady=5)

        # 说明文本
        info_text = tk.Text(main_frame, height=15, width=80, wrap=tk.WORD)
        info_text.pack(pady=20, fill=tk.BOTH, expand=True)

        info_content = """
CMOST图形界面使用说明:

这是CMOST的图形界面，提供完整的仿真功能和命令行接口。

主要功能:
1. 运行演示 - 查看CMOST的基本功能演示
2. 运行模拟 - 启动完整的图形化仿真界面，包含调参、筛查配置等功能
3. 命令行帮助 - 了解如何使用命令行界面
4. 配置帮助 - 了解配置文件格式和Excel支持
5. 关于CMOST - 查看版本和作者信息

新功能亮点:
• 统一入口main.py，支持多种启动方式
• Excel配置文件支持，包含多工作表结构
• 自动生成Excel配置模板
• 支持JSON、YAML、MATLAB、Excel等多种格式

快速开始:
1. 生成Excel配置模板:
   python -m cmost.cli config --template excel --output my_config.xlsx

2. 运行仿真:
   python -m cmost.cli simulate --patients 10000 --output results.json

3. 比较筛查策略:
   python -m cmost.cli compare --strategies colonoscopy,fit --output comparison.json

更多信息请参考项目文档和README文件。
        """

        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)

        print("图形界面已启动！")
        print("这是一个简化的图形界面，完整功能请使用命令行。")

        # 启动主循环
        root.mainloop()

    except ImportError as e:
        print(f"错误: 无法启动图形界面: {e}")
        print("请确保已安装tkinter:")
        print("  sudo apt-get install python3-tk  # Ubuntu/Debian")
        print("  或使用conda: conda install tk")
        sys.exit(1)
    except Exception as e:
        print(f"启动图形界面时发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def launch_interactive():
    """启动交互式Python环境"""
    print("启动CMOST交互式环境...")
    print("已导入常用模块，您可以直接使用:")
    print("  - simulation: 仿真引擎")
    print("  - settings: 配置管理")
    print("  - Settings: 配置类")
    print("  - np: NumPy")
    print("  - pd: Pandas")
    
    # 导入常用模块到全局命名空间
    import numpy as np
    import pandas as pd
    from cmost.core.simulation import Simulation
    from cmost.config.settings import Settings
    
    # 创建示例设置
    example_settings = Settings()
    example_settings.set('Number_patients', 10000)
    
    print("\n示例代码:")
    print(">>> settings = Settings()")
    print(">>> settings.set('Number_patients', 10000)")
    print(">>> simulation = Simulation(settings)")
    print(">>> results = simulation.run(years=50)")
    print(">>> stats = simulation.get_summary_statistics()")
    
    # 启动交互式环境
    import code
    code.interact(
        banner="",
        local={
            'simulation': Simulation,
            'settings': settings,
            'Settings': Settings,
            'np': np,
            'pd': pd,
            'example_settings': example_settings
        }
    )


def run_demo():
    """运行快速演示"""
    print("运行CMOST快速演示...")

    try:
        # 简化的演示，只展示配置和基本功能
        from cmost.config.settings import Settings

        print("CMOST配置演示:")
        print("=" * 50)

        # 创建演示设置
        demo_settings = Settings()

        print("1. 基本设置:")
        print(f"   版本: {demo_settings.get('Version', 'N/A')}")
        print(f"   患者数量选项: {demo_settings.get('NumberPatientsValues', 'N/A')}")
        print(f"   结果路径: {demo_settings.get('ResultsPath', 'N/A')}")

        print("\n2. 模型参数示例:")
        model_params = demo_settings.get('ModelParameters', {})
        print(f"   早期腺瘤发生率倍数: {model_params.get('early_mult', 'N/A')}")
        print(f"   进展期腺瘤发生率倍数: {model_params.get('adv_mult', 'N/A')}")
        print(f"   癌症发生率倍数: {model_params.get('cancer_mult', 'N/A')}")

        print("\n3. 筛查设置示例:")
        screening_params = demo_settings.get('Screening', {})
        print(f"   启用筛查: {screening_params.get('EnableScreening', 'N/A')}")
        print(f"   筛查年龄: {screening_params.get('ScreeningAges', 'N/A')}")
        print(f"   筛查依从性: {screening_params.get('ScreeningCompliance', 'N/A')}")

        print("\n4. 配置文件支持:")
        print("   支持的格式: JSON, YAML, MATLAB (.mat), Excel (.xlsx)")
        print("   可以通过以下方式加载配置:")
        print("   - settings.load_settings('config.json')")
        print("   - settings.load_settings('config.yaml')")
        print("   - settings.load_settings('config.xlsx')")

        print("\n5. Excel模板生成:")
        try:
            from cmost.config.excel_templates import create_excel_template
            template_path = "demo_config_template.xlsx"
            success = create_excel_template('basic', template_path)
            if success:
                print(f"   ✓ Excel配置模板已生成: {template_path}")
            else:
                print("   ✗ Excel模板生成失败")
        except ImportError:
            print("   ✗ Excel功能需要安装: pip install pandas openpyxl")

        print("\n演示完成！")
        print("\n下一步操作:")
        print("1. 使用图形界面: python main.py --gui")
        print("2. 使用命令行: python main.py --cli")
        print("3. 交互式环境: python main.py --interactive")
        print("4. 生成Excel模板: python -m cmost.cli config --template excel --output my_config.xlsx")

    except Exception as e:
        print(f"演示运行失败: {e}")
        print("\n这可能是由于缺少某些依赖包造成的。")
        print("请确保已安装所有必需的依赖:")
        print("  pip install -e .")
        print("  pip install pandas openpyxl  # 用于Excel支持")
        sys.exit(1)


def show_help():
    """显示帮助信息"""
    help_text = """
CMOST使用指南:

启动选项:
  --cli, -c          启动命令行界面
  --gui, -g          启动图形用户界面
  --interactive, -i  启动交互式Python环境
  --demo, -d         运行快速演示
  --version, -v      显示版本信息
  --help, -h         显示此帮助信息

配置选项:
  --config FILE      指定配置文件路径
  --verbose          启用详细输出

示例:
  python main.py --gui                    # 启动图形界面
  python main.py --cli                    # 启动命令行
  python main.py --demo                   # 运行演示
  python main.py --config my_config.json # 使用指定配置

更多信息:
  文档: https://cmost.readthedocs.io
  源码: https://github.com/misselwitz/cmost
  问题: https://github.com/misselwitz/cmost/issues
    """
    print(help_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="CMOST - 结直肠癌微观仿真筛查结果工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python main.py --gui                    # 启动图形界面
  python main.py --cli                    # 启动命令行
  python main.py --demo                   # 运行演示
  python main.py --config my_config.json # 使用指定配置
        """
    )
    
    # 启动选项
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--cli', '-c', action='store_true', help='启动命令行界面')
    group.add_argument('--gui', '-g', action='store_true', help='启动图形用户界面')
    group.add_argument('--interactive', '-i', action='store_true', help='启动交互式Python环境')
    group.add_argument('--demo', '-d', action='store_true', help='运行快速演示')
    group.add_argument('--version', '-v', action='store_true', help='显示版本信息')
    
    # 配置选项
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--verbose', action='store_true', help='启用详细输出')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 显示横幅
    show_banner()
    
    # 初始化配置
    if args.config:
        if os.path.exists(args.config):
            initialize_settings(args.config)
            print(f"已加载配置文件: {args.config}")
        else:
            print(f"警告: 配置文件不存在: {args.config}")
    
    # 根据参数执行相应操作
    if args.version:
        print(f"CMOST版本: {__version__}")
        print(f"作者: {__author__}")
        return
    
    if args.cli:
        launch_cli()
    elif args.gui:
        launch_gui()
    elif args.interactive:
        launch_interactive()
    elif args.demo:
        run_demo()
    else:
        show_help()


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
