"""
Screening configuration panel for CMOST application.

This module implements a dedicated panel for screening strategy configuration,
including screening tool selection, parameter input, and strategy management.
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, List, Optional


class ScreeningConfigPanel(ttk.Frame):
    """
    Screening configuration panel for CMOST application.
    
    Provides interface for:
    - Screening tool selection
    - Performance parameter input
    - Age range configuration
    - Sequential screening setup
    """
    
    def __init__(self, parent, **kwargs):
        """
        Initialize the screening configuration panel.
        
        Args:
            parent: Parent widget
            **kwargs: Additional keyword arguments
        """
        super().__init__(parent, **kwargs)
        
        # Panel state
        self.screening_config = {}
        
        # Screening tool options
        self.screening_tools = {
            "colonoscopy": "结肠镜检查",
            "fit": "粪便免疫化学试验(FIT)",
            "sigmoidoscopy": "乙状结肠镜检查",
            "ct_colonography": "CT结肠成像",
            "fobt": "粪便潜血试验(FOBT)",
            "mt_sdna": "多靶点粪便DNA检测"
        }
        
        # Create UI components
        self.create_ui()
    
    def create_ui(self):
        """Create the user interface components."""
        # Tool selection frame
        tool_frame = ttk.LabelFrame(self, text="筛查工具选择", padding=10)
        tool_frame.pack(fill=tk.X, pady=5)
        
        # Primary screening tool
        ttk.Label(tool_frame, text="主要筛查工具:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.primary_tool_var = tk.StringVar(value="colonoscopy")
        primary_combo = ttk.Combobox(
            tool_frame,
            textvariable=self.primary_tool_var,
            values=list(self.screening_tools.values()),
            state="readonly",
            width=25
        )
        primary_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        primary_combo.bind("<<ComboboxSelected>>", self._on_primary_tool_change)
        
        # Sequential screening option
        self.sequential_var = tk.BooleanVar(value=False)
        sequential_check = ttk.Checkbutton(
            tool_frame,
            text="启用贯序筛查",
            variable=self.sequential_var,
            command=self._toggle_sequential_screening
        )
        sequential_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Secondary screening tool (initially hidden)
        self.secondary_label = ttk.Label(tool_frame, text="次要筛查工具:")
        self.secondary_tool_var = tk.StringVar(value="fit")
        self.secondary_combo = ttk.Combobox(
            tool_frame,
            textvariable=self.secondary_tool_var,
            values=list(self.screening_tools.values()),
            state="readonly",
            width=25
        )
        
        # Age range frame
        age_frame = ttk.LabelFrame(self, text="筛查年龄范围", padding=10)
        age_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(age_frame, text="起始年龄:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.start_age_var = tk.StringVar(value="50")
        start_age_combo = ttk.Combobox(
            age_frame,
            textvariable=self.start_age_var,
            values=["40", "45", "50", "55"],
            width=10
        )
        start_age_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(age_frame, text="结束年龄:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.end_age_var = tk.StringVar(value="75")
        end_age_combo = ttk.Combobox(
            age_frame,
            textvariable=self.end_age_var,
            values=["70", "75", "80", "85"],
            width=10
        )
        end_age_combo.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(age_frame, text="筛查间隔(年):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.interval_var = tk.StringVar(value="10")
        interval_combo = ttk.Combobox(
            age_frame,
            textvariable=self.interval_var,
            values=["1", "2", "3", "5", "10"],
            width=10
        )
        interval_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Performance parameters frame
        perf_frame = ttk.LabelFrame(self, text="筛查工具性能参数", padding=10)
        perf_frame.pack(fill=tk.X, pady=5)
        
        # Create notebook for different parameter sets
        self.perf_notebook = ttk.Notebook(perf_frame)
        self.perf_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Primary tool parameters
        self.primary_perf_frame = ttk.Frame(self.perf_notebook)
        self.perf_notebook.add(self.primary_perf_frame, text="主要工具参数")
        
        # Secondary tool parameters (initially hidden)
        self.secondary_perf_frame = ttk.Frame(self.perf_notebook)
        
        # Create parameter inputs for primary tool
        self._create_performance_inputs(self.primary_perf_frame, "primary")
        
        # Compliance frame
        compliance_frame = ttk.LabelFrame(self, text="依从性设置", padding=10)
        compliance_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(compliance_frame, text="初次筛查依从性 (%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.initial_compliance_var = tk.StringVar(value="70")
        initial_compliance_entry = ttk.Entry(compliance_frame, textvariable=self.initial_compliance_var, width=10)
        initial_compliance_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(compliance_frame, text="后续筛查依从性 (%):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.followup_compliance_var = tk.StringVar(value="80")
        followup_compliance_entry = ttk.Entry(compliance_frame, textvariable=self.followup_compliance_var, width=10)
        followup_compliance_entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Advanced options frame
        advanced_frame = ttk.LabelFrame(self, text="高级选项", padding=10)
        advanced_frame.pack(fill=tk.X, pady=5)
        
        # Risk stratification
        self.risk_stratification_var = tk.BooleanVar(value=False)
        risk_check = ttk.Checkbutton(
            advanced_frame,
            text="启用风险分层筛查",
            variable=self.risk_stratification_var,
            command=self._toggle_risk_stratification
        )
        risk_check.grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Surveillance option
        self.surveillance_var = tk.BooleanVar(value=True)
        surveillance_check = ttk.Checkbutton(
            advanced_frame,
            text="启用监测随访",
            variable=self.surveillance_var
        )
        surveillance_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Cost consideration
        self.cost_consideration_var = tk.BooleanVar(value=False)
        cost_check = ttk.Checkbutton(
            advanced_frame,
            text="考虑成本因素",
            variable=self.cost_consideration_var
        )
        cost_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Configuration summary
        summary_frame = ttk.LabelFrame(self, text="配置摘要", padding=10)
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.summary_text = tk.Text(summary_frame, height=8, wrap=tk.WORD)
        self.summary_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add scrollbar
        summary_scrollbar = ttk.Scrollbar(summary_frame, orient="vertical", command=self.summary_text.yview)
        summary_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.summary_text.configure(yscrollcommand=summary_scrollbar.set)
        
        # Update summary initially
        self._update_summary()
        
        # Bind events to update summary
        self._bind_update_events()
    
    def _create_performance_inputs(self, parent_frame: ttk.Frame, prefix: str):
        """
        Create performance parameter input widgets.
        
        Args:
            parent_frame: Parent frame for the inputs
            prefix: Prefix for variable names (primary/secondary)
        """
        # Sensitivity for early adenoma
        ttk.Label(parent_frame, text="早期腺瘤灵敏度 (%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        var_name = f"{prefix}_early_adenoma_sensitivity_var"
        setattr(self, var_name, tk.StringVar(value="60"))
        entry = ttk.Entry(parent_frame, textvariable=getattr(self, var_name), width=10)
        entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Sensitivity for advanced adenoma
        ttk.Label(parent_frame, text="进展期腺瘤灵敏度 (%):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        var_name = f"{prefix}_advanced_adenoma_sensitivity_var"
        setattr(self, var_name, tk.StringVar(value="85"))
        entry = ttk.Entry(parent_frame, textvariable=getattr(self, var_name), width=10)
        entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Sensitivity for cancer
        ttk.Label(parent_frame, text="癌症灵敏度 (%):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        var_name = f"{prefix}_cancer_sensitivity_var"
        setattr(self, var_name, tk.StringVar(value="95"))
        entry = ttk.Entry(parent_frame, textvariable=getattr(self, var_name), width=10)
        entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Specificity
        ttk.Label(parent_frame, text="特异度 (%):").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        var_name = f"{prefix}_specificity_var"
        setattr(self, var_name, tk.StringVar(value="90"))
        entry = ttk.Entry(parent_frame, textvariable=getattr(self, var_name), width=10)
        entry.grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Complication rate
        ttk.Label(parent_frame, text="并发症率 (%):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        var_name = f"{prefix}_complication_rate_var"
        setattr(self, var_name, tk.StringVar(value="0.1"))
        entry = ttk.Entry(parent_frame, textvariable=getattr(self, var_name), width=10)
        entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Cost per test
        ttk.Label(parent_frame, text="单次检查成本 ($):").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        var_name = f"{prefix}_cost_var"
        setattr(self, var_name, tk.StringVar(value="1000"))
        entry = ttk.Entry(parent_frame, textvariable=getattr(self, var_name), width=10)
        entry.grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)
    
    def _toggle_sequential_screening(self):
        """Toggle sequential screening options."""
        if self.sequential_var.get():
            # Show secondary tool options
            self.secondary_label.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
            self.secondary_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
            
            # Add secondary tool parameters tab
            self.perf_notebook.add(self.secondary_perf_frame, text="次要工具参数")
            self._create_performance_inputs(self.secondary_perf_frame, "secondary")
        else:
            # Hide secondary tool options
            self.secondary_label.grid_remove()
            self.secondary_combo.grid_remove()
            
            # Remove secondary tool parameters tab
            try:
                self.perf_notebook.forget(self.secondary_perf_frame)
            except:
                pass
        
        self._update_summary()
    
    def _toggle_risk_stratification(self):
        """Toggle risk stratification options."""
        # This could expand to show risk factor configuration
        self._update_summary()
    
    def _on_primary_tool_change(self, event=None):
        """Handle primary tool selection change."""
        # Update default parameters based on selected tool
        tool = self._get_tool_key(self.primary_tool_var.get())
        self._set_default_parameters(tool, "primary")
        self._update_summary()
    
    def _get_tool_key(self, tool_display_name: str) -> str:
        """Get tool key from display name."""
        for key, display in self.screening_tools.items():
            if display == tool_display_name:
                return key
        return "colonoscopy"
    
    def _set_default_parameters(self, tool: str, prefix: str):
        """Set default parameters for a screening tool."""
        defaults = {
            "colonoscopy": {
                "early_adenoma_sensitivity": "60",
                "advanced_adenoma_sensitivity": "85",
                "cancer_sensitivity": "95",
                "specificity": "90",
                "complication_rate": "0.1",
                "cost": "1000"
            },
            "fit": {
                "early_adenoma_sensitivity": "20",
                "advanced_adenoma_sensitivity": "40",
                "cancer_sensitivity": "80",
                "specificity": "95",
                "complication_rate": "0.0",
                "cost": "25"
            },
            "sigmoidoscopy": {
                "early_adenoma_sensitivity": "70",
                "advanced_adenoma_sensitivity": "85",
                "cancer_sensitivity": "90",
                "specificity": "92",
                "complication_rate": "0.05",
                "cost": "300"
            }
        }
        
        if tool in defaults:
            for param, value in defaults[tool].items():
                var_name = f"{prefix}_{param}_var"
                if hasattr(self, var_name):
                    getattr(self, var_name).set(value)
    
    def _bind_update_events(self):
        """Bind events to update summary when values change."""
        # This is a simplified version - in practice you'd bind to all relevant variables
        self.primary_tool_var.trace("w", lambda *args: self._update_summary())
        self.start_age_var.trace("w", lambda *args: self._update_summary())
        self.end_age_var.trace("w", lambda *args: self._update_summary())
    
    def _update_summary(self):
        """Update the configuration summary."""
        summary = "=== 筛查策略配置摘要 ===\n\n"
        
        # Basic configuration
        summary += f"主要筛查工具: {self.primary_tool_var.get()}\n"
        summary += f"筛查年龄: {self.start_age_var.get()} - {self.end_age_var.get()}岁\n"
        summary += f"筛查间隔: {self.interval_var.get()}年\n"
        
        if self.sequential_var.get():
            summary += f"次要筛查工具: {self.secondary_tool_var.get()}\n"
        
        summary += f"\n依从性设置:\n"
        summary += f"  初次筛查: {self.initial_compliance_var.get()}%\n"
        summary += f"  后续筛查: {self.followup_compliance_var.get()}%\n"
        
        # Performance parameters
        summary += f"\n主要工具性能参数:\n"
        if hasattr(self, 'primary_early_adenoma_sensitivity_var'):
            summary += f"  早期腺瘤灵敏度: {self.primary_early_adenoma_sensitivity_var.get()}%\n"
            summary += f"  进展期腺瘤灵敏度: {self.primary_advanced_adenoma_sensitivity_var.get()}%\n"
            summary += f"  癌症灵敏度: {self.primary_cancer_sensitivity_var.get()}%\n"
            summary += f"  特异度: {self.primary_specificity_var.get()}%\n"
        
        # Advanced options
        if self.risk_stratification_var.get():
            summary += f"\n高级选项:\n"
            summary += f"  风险分层筛查: 启用\n"
        
        if self.surveillance_var.get():
            summary += f"  监测随访: 启用\n"
        
        if self.cost_consideration_var.get():
            summary += f"  成本考虑: 启用\n"
        
        # Update text widget
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(tk.END, summary)
    
    def get_screening_config(self) -> Dict[str, Any]:
        """
        Get the current screening configuration.
        
        Returns:
            Dictionary containing screening configuration
        """
        config = {
            "primary_tool": self._get_tool_key(self.primary_tool_var.get()),
            "start_age": int(self.start_age_var.get()),
            "end_age": int(self.end_age_var.get()),
            "interval": int(self.interval_var.get()),
            "initial_compliance": float(self.initial_compliance_var.get()) / 100,
            "followup_compliance": float(self.followup_compliance_var.get()) / 100,
            "sequential_screening": self.sequential_var.get(),
            "risk_stratification": self.risk_stratification_var.get(),
            "surveillance": self.surveillance_var.get(),
            "cost_consideration": self.cost_consideration_var.get()
        }
        
        # Add performance parameters
        if hasattr(self, 'primary_early_adenoma_sensitivity_var'):
            config["primary_performance"] = {
                "early_adenoma_sensitivity": float(self.primary_early_adenoma_sensitivity_var.get()) / 100,
                "advanced_adenoma_sensitivity": float(self.primary_advanced_adenoma_sensitivity_var.get()) / 100,
                "cancer_sensitivity": float(self.primary_cancer_sensitivity_var.get()) / 100,
                "specificity": float(self.primary_specificity_var.get()) / 100,
                "complication_rate": float(self.primary_complication_rate_var.get()) / 100,
                "cost": float(self.primary_cost_var.get())
            }
        
        if self.sequential_var.get() and hasattr(self, 'secondary_early_adenoma_sensitivity_var'):
            config["secondary_tool"] = self._get_tool_key(self.secondary_tool_var.get())
            config["secondary_performance"] = {
                "early_adenoma_sensitivity": float(self.secondary_early_adenoma_sensitivity_var.get()) / 100,
                "advanced_adenoma_sensitivity": float(self.secondary_advanced_adenoma_sensitivity_var.get()) / 100,
                "cancer_sensitivity": float(self.secondary_cancer_sensitivity_var.get()) / 100,
                "specificity": float(self.secondary_specificity_var.get()) / 100,
                "complication_rate": float(self.secondary_complication_rate_var.get()) / 100,
                "cost": float(self.secondary_cost_var.get())
            }
        
        return config
    
    def set_screening_config(self, config: Dict[str, Any]):
        """
        Set screening configuration from dictionary.
        
        Args:
            config: Configuration dictionary
        """
        # This method would set all the UI values from a configuration dictionary
        # Implementation would be the reverse of get_screening_config()
        pass
