"""
Unit tests for birth cohort simulation module.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch
from datetime import datetime

from cmost.core.birth_cohort import Birth<PERSON>ohort, BirthCohortSimulation
from cmost.config.settings import Settings
from cmost.models.patient import Patient


class TestBirthCohort:
    """Test cases for BirthCohort dataclass."""
    
    def test_birth_cohort_initialization(self):
        """Test birth cohort initialization."""
        cohort = BirthCohort(
            birth_year=2000,
            size=1000,
            male_proportion=0.5
        )
        
        assert cohort.birth_year == 2000
        assert cohort.size == 1000
        assert cohort.male_proportion == 0.5
        assert cohort.alive_patients == 1000
        assert len(cohort.patients) == 0
        assert cohort.deaths_by_cause == {'natural': 0, 'cancer': 0, 'other': 0}
    
    def test_birth_cohort_post_init(self):
        """Test birth cohort post-initialization."""
        cohort = BirthCohort(birth_year=2000, size=500)
        
        assert cohort.alive_patients == 500
        assert cohort.created_year == datetime.now().year
        assert isinstance(cohort.deaths_by_cause, dict)


class TestBirthCohortSimulation:
    """Test cases for BirthCohortSimulation class."""
    
    @pytest.fixture
    def settings(self):
        """Create test settings."""
        settings = Settings()
        settings.set('Simulation.StartYear', 2000)
        settings.set('Simulation.EndYear', 2010)
        settings.set('BirthCohort.AnnualBirths', 100)
        settings.set('BirthCohort.BirthRateTrend', 0.01)
        settings.set('ModelParameters.male_proportion', 0.5)
        return settings
    
    @pytest.fixture
    def simulation(self, settings):
        """Create test simulation."""
        with patch('cmost.core.birth_cohort.PopulationGenerator'):
            return BirthCohortSimulation(settings)
    
    def test_initialization(self, simulation, settings):
        """Test simulation initialization."""
        assert simulation.settings == settings
        assert simulation.current_year == 2000
        assert simulation.end_year == 2010
        assert simulation.annual_births == 100
        assert simulation.birth_rate_trend == 0.01
        assert len(simulation.cohorts) == 0
    
    def test_get_setting(self, simulation):
        """Test _get_setting method."""
        # Test existing setting
        result = simulation._get_setting('Simulation.StartYear', 1990)
        assert result == 2000
        
        # Test non-existing setting with default
        result = simulation._get_setting('NonExistent.Setting', 42)
        assert result == 42
        
        # Test nested non-existing setting
        result = simulation._get_setting('NonExistent.Nested.Setting', 'default')
        assert result == 'default'
    
    def test_create_birth_cohort(self, simulation):
        """Test birth cohort creation."""
        cohort = simulation.create_birth_cohort(2000, 50)
        
        assert cohort.birth_year == 2000
        assert cohort.size == 50
        assert cohort.male_proportion == 0.5
        assert len(cohort.patients) == 50
        
        # Check patient properties
        for patient in cohort.patients:
            assert patient.age == 0
            assert patient.birth_year == 2000
            assert patient.gender in ['M', 'F']
            assert isinstance(patient.risk_factors, dict)
    
    def test_generate_birth_risk_factors(self, simulation):
        """Test birth risk factor generation."""
        risk_factors = simulation._generate_birth_risk_factors('M')
        
        assert isinstance(risk_factors, dict)
        assert 'family_history' in risk_factors
        assert 'genetic' in risk_factors
        assert 'smoking' in risk_factors
        assert 'bmi' in risk_factors
        assert 'physical_activity' in risk_factors
        assert 'diet' in risk_factors
        
        # Check value ranges
        assert 0.5 <= risk_factors['genetic'] <= 2.0
        assert risk_factors['family_history'] in [1.0, 2.0]
    
    def test_update_cohort_risk_factors(self, simulation):
        """Test cohort risk factor updates."""
        cohort = simulation.create_birth_cohort(2000, 10)
        
        # Age patients to 20 years old
        simulation.update_cohort_risk_factors(cohort, 2020)
        
        for patient in cohort.patients:
            assert patient.age == 20
            # Risk factors should be updated for adults
            assert patient.risk_factors['smoking'] >= 1.0
            assert patient.risk_factors['bmi'] >= 0.9
    
    def test_add_annual_birth_cohort(self, simulation):
        """Test adding annual birth cohort."""
        simulation.add_annual_birth_cohort(2001)
        
        assert 2001 in simulation.cohorts
        cohort = simulation.cohorts[2001]
        assert cohort.birth_year == 2001
        # Should account for birth rate trend: 100 * (1 + 0.01)^1 = 101
        assert cohort.size == 101
    
    def test_simulate_year(self, simulation):
        """Test year simulation."""
        with patch.object(simulation, '_simulate_cohort_year') as mock_simulate:
            simulation.simulate_year(2001)
            
            # Should create new cohort for 2001
            assert 2001 in simulation.cohorts
            
            # Should call _simulate_cohort_year for the new cohort
            mock_simulate.assert_called_once()
    
    def test_check_natural_death(self, simulation):
        """Test natural death checking."""
        patient = Patient(id=1, age=80, gender='M', risk_factors={})
        
        # Mock mortality tables
        simulation.settings.mortality_tables = {
            'M': [0.001] * 100  # Low mortality for testing
        }
        
        # Test with low mortality
        with patch('numpy.random.random', return_value=0.5):
            result = simulation._check_natural_death(patient, 2020)
            assert result is False
        
        # Test with high mortality
        with patch('numpy.random.random', return_value=0.0001):
            result = simulation._check_natural_death(patient, 2020)
            assert result is True
    
    def test_generate_new_polyps(self, simulation):
        """Test new polyp generation."""
        patient = Patient(id=1, age=50, gender='M', risk_factors={})
        patient.individual_risk = 1.5
        
        initial_polyp_count = len(patient.polyps)
        
        with patch('numpy.random.poisson', return_value=2):
            simulation._generate_new_polyps(patient)
        
        assert len(patient.polyps) == initial_polyp_count + 2
    
    def test_check_cancer_death(self, simulation):
        """Test cancer death checking."""
        from cmost.models.cancer import Cancer

        patient = Patient(id=1, age=60, gender='M', risk_factors={})

        # Add advanced cancer (stage 10 = stage IV, which has high mortality)
        cancer = Cancer(id=1, location=1, stage=10, patient_id=1, patient_gender='M')
        patient.cancers.append(cancer)

        # Test with treatment (mortality rate = 0.7 * 0.6 = 0.42)
        cancer.treatment_applied = True
        with patch('numpy.random.random', return_value=0.5):  # > 0.42, should survive
            result = simulation._check_cancer_death(patient)
            assert result is False

        # Test without treatment (mortality rate = 0.7)
        cancer.treatment_applied = False
        with patch('numpy.random.random', return_value=0.3):  # < 0.7, should die
            result = simulation._check_cancer_death(patient)
            assert result is True
    
    def test_get_cohort_statistics(self, simulation):
        """Test cohort statistics calculation."""
        # Create test cohorts
        cohort1 = simulation.create_birth_cohort(2000, 100)
        cohort1.deaths_by_cause = {'natural': 5, 'cancer': 2, 'other': 1}
        cohort1.alive_patients = 92
        simulation.cohorts[2000] = cohort1
        
        cohort2 = simulation.create_birth_cohort(2001, 101)
        cohort2.deaths_by_cause = {'natural': 3, 'cancer': 1, 'other': 0}
        cohort2.alive_patients = 97
        simulation.cohorts[2001] = cohort2
        
        stats = simulation.get_cohort_statistics()
        
        assert stats['total_cohorts'] == 2
        assert stats['total_births'] == 201
        assert stats['total_alive'] == 189
        assert stats['total_deaths'] == 12
        assert stats['deaths_by_cause']['natural'] == 8
        assert stats['deaths_by_cause']['cancer'] == 3
        assert stats['deaths_by_cause']['other'] == 1
        assert 2000 in stats['cohorts_by_year']
        assert 2001 in stats['cohorts_by_year']
    
    def test_get_results(self, simulation):
        """Test results retrieval."""
        # Create test cohorts with patients
        cohort = simulation.create_birth_cohort(2000, 50)
        
        # Add some test data to patients
        for i, patient in enumerate(cohort.patients[:5]):
            from cmost.models.cancer import Cancer
            cancer = Cancer(id=i, location=1, stage=2, patient_id=patient.id, patient_gender=patient.gender)
            patient.cancers.append(cancer)
            
            patient.screening_history.append({
                'year': 2020,
                'test_type': 'colonoscopy',
                'findings': {}
            })
        
        simulation.cohorts[2000] = cohort
        
        results = simulation.get_results()
        
        assert 'total_patients' in results
        assert 'cancer_cases' in results
        assert 'deaths' in results
        assert 'screenings_performed' in results
        assert 'cohort_statistics' in results
        
        assert results['total_patients'] == 50
        assert results['cancer_cases'] == 5
        assert results['screenings_performed'] == 5
    
    def test_run_simulation(self, simulation):
        """Test running simulation for multiple years."""
        with patch.object(simulation, 'simulate_year') as mock_simulate:
            results = simulation.run(3)

            # Should call simulate_year for each year
            assert mock_simulate.call_count == 3

            # Should return cohort statistics (not full results)
            assert isinstance(results, dict)
            assert 'total_cohorts' in results
            assert 'total_births' in results
    
    def test_get_all_patients(self, simulation):
        """Test getting all patients from all cohorts."""
        cohort1 = simulation.create_birth_cohort(2000, 10)
        cohort2 = simulation.create_birth_cohort(2001, 15)
        
        simulation.cohorts[2000] = cohort1
        simulation.cohorts[2001] = cohort2
        
        all_patients = simulation.get_all_patients()
        
        assert len(all_patients) == 25
        assert all(isinstance(p, Patient) for p in all_patients)
    
    def test_import_existing_patients(self, simulation):
        """Test importing existing patients."""
        # Create some test patients
        patients = [
            Patient(id=1, age=30, gender='M', risk_factors={}),
            Patient(id=2, age=25, gender='F', risk_factors={})
        ]
        
        simulation.import_existing_patients(patients, 2020)
        
        # Should create cohorts based on birth years
        assert len(simulation.cohorts) >= 1
        
        # Check that patients are distributed to appropriate cohorts
        all_imported = simulation.get_all_patients()
        assert len(all_imported) >= 2


class TestBirthCohortIntegration:
    """Integration tests for birth cohort simulation."""
    
    def test_full_simulation_workflow(self):
        """Test complete simulation workflow."""
        settings = Settings()
        settings.set('Simulation.StartYear', 2000)
        settings.set('Simulation.EndYear', 2002)
        settings.set('BirthCohort.AnnualBirths', 50)
        settings.set('ModelParameters.male_proportion', 0.5)

        with patch('cmost.core.birth_cohort.PopulationGenerator'):
            simulation = BirthCohortSimulation(settings)

            # Run simulation
            results = simulation.run(3)

            # Verify results (run returns cohort statistics, not full results)
            assert isinstance(results, dict)
            assert 'total_births' in results
            assert len(simulation.cohorts) == 3  # 2000, 2001, 2002

            # Get full results using get_results method
            full_results = simulation.get_results()
            assert 'total_patients' in full_results
            assert full_results['total_patients'] > 0

            # Verify cohort progression
            for year, cohort in simulation.cohorts.items():
                assert cohort.birth_year == year
                assert cohort.size > 0
                assert len(cohort.patients) == cohort.size