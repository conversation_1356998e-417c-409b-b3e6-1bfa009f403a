"""
Simulation panel for the CMOST application.

This module implements the simulation control panel, providing
an interface for running and monitoring simulations.
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

from ..core.simulation import Simulation
from ..config.settings import settings, get_setting, set_setting


class SimulationPanel(ttk.Frame):
    """
    Simulation control panel for CMOST.
    
    This panel provides controls for running simulations and
    displaying real-time progress and preliminary results.
    """
    
    def __init__(self, parent):
        """
        Initialize the simulation panel.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent, padding=10)
        self.parent = parent
        
        # Simulation state
        self.simulation = None
        self.simulation_thread = None
        self.is_running = False
        self.start_time = None
        self.progress_value = 0
        
        # Create UI components
        self.create_control_frame()
        self.create_progress_frame()
        self.create_results_preview()
        
        # Pack the main frame
        self.pack(fill=tk.BOTH, expand=True)
    
    def create_control_frame(self):
        """Create the simulation control frame."""
        control_frame = ttk.LabelFrame(self, text="Simulation Control", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Simulation type
        ttk.Label(control_frame, text="Simulation Type:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.sim_type = ttk.Combobox(
            control_frame, 
            values=["Standard", "Batch", "Sensitivity Analysis"],
            width=20,
            state="readonly"
        )
        self.sim_type.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.sim_type.current(0)
        
        # Number of patients
        ttk.Label(control_frame, text="Number of Patients:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.num_patients = ttk.Combobox(
            control_frame,
            values=[str(n) for n in [10000, 25000, 50000, 100000, 1000000]],
            width=10
        )
        self.num_patients.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.num_patients.set(str(get_setting('NumberPatients', 10000)))
        
        # Random seed
        ttk.Label(control_frame, text="Random Seed:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.random_seed = ttk.Entry(control_frame, width=10)
        self.random_seed.grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)
        self.random_seed.insert(0, str(get_setting('Simulation.RandomSeed', 42)))
        
        # Multiprocessing
        self.enable_mp = tk.BooleanVar(value=get_setting('Simulation.EnableMultiprocessing', True))
        mp_check = ttk.Checkbutton(
            control_frame,
            text="Enable Multiprocessing",
            variable=self.enable_mp
        )
        mp_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(control_frame, text="Number of Processes:").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.num_processes = ttk.Spinbox(control_frame, from_=0, to=64, width=5)
        self.num_processes.grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)
        self.num_processes.set(str(get_setting('Simulation.NumProcesses', 0)))
        
        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="Start Simulation", command=self.start_simulation)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="Stop Simulation", command=self.stop_simulation, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.view_results_button = ttk.Button(button_frame, text="View Results", command=self.view_results, state=tk.DISABLED)
        self.view_results_button.pack(side=tk.LEFT, padx=5)
    
    def create_progress_frame(self):
        """Create the progress display frame."""
        progress_frame = ttk.LabelFrame(self, text="Simulation Progress", padding=10)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL, length=100, mode='determinate')
        self.progress.pack(fill=tk.X, padx=5, pady=5)
        
        # Status information
        info_frame = ttk.Frame(progress_frame)
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(info_frame, text="Status:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.status_label = ttk.Label(info_frame, text="Ready")
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(info_frame, text="Elapsed Time:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.time_label = ttk.Label(info_frame, text="00:00:00")
        self.time_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(info_frame, text="Patients Processed:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.patients_label = ttk.Label(info_frame, text="0 / 0")
        self.patients_label.grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(info_frame, text="Estimated Time Left:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.eta_label = ttk.Label(info_frame, text="--:--:--")
        self.eta_label.grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)
    
    def create_results_preview(self):
        """Create the results preview area."""
        preview_frame = ttk.LabelFrame(self, text="Results Preview", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create matplotlib figure for visualization
        self.fig, self.ax = plt.subplots(figsize=(8, 4), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, master=preview_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Initial plot
        self.update_preview_plot()
    
    def update_preview_plot(self, data=None):
        """
        Update the preview plot with simulation data.
        
        Args:
            data: Optional simulation data to display
        """
        self.ax.clear()
        
        if data is None:
            # Show placeholder
            self.ax.text(
                0.5, 0.5, 
                "Simulation results will appear here", 
                horizontalalignment='center',
                verticalalignment='center',
                transform=self.ax.transAxes,
                fontsize=12
            )
        else:
            # Plot actual data
            # This is a placeholder - actual implementation would depend on data structure
            x = np.arange(len(data))
            self.ax.bar(x, data)
            self.ax.set_xlabel('Age Group')
            self.ax.set_ylabel('Prevalence (%)')
            self.ax.set_title('Polyp Prevalence by Age')
        
        self.fig.tight_layout()
        self.canvas.draw()
    
    def start_simulation(self):
        """Start the simulation in a separate thread."""
        if self.is_running:
            return
        
        # Get simulation parameters
        try:
            num_patients = int(self.num_patients.get())
            random_seed = int(self.random_seed.get())
            enable_mp = self.enable_mp.get()
            num_processes = int(self.num_processes.get()) if enable_mp else 0
        except ValueError as e:
            messagebox.showerror("Invalid Input", f"Please enter valid numeric values: {str(e)}")
            return
        
        # Update settings
        set_setting('NumberPatients', num_patients)
        set_setting('Simulation.RandomSeed', random_seed)
        set_setting('Simulation.EnableMultiprocessing', enable_mp)
        set_setting('Simulation.NumProcesses', num_processes)
        
        # Update UI state
        self.is_running = True
        self.start_time = time.time()
        self.progress_value = 0
        self.progress['value'] = 0
        self.status_label.config(text="Initializing...")
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.view_results_button.config(state=tk.DISABLED)
        
        # Start simulation in a separate thread
        self.simulation_thread = threading.Thread(target=self._run_simulation)
        self.simulation_thread.daemon = True
        self.simulation_thread.start()
        
        # Start progress update
        self.after(100, self.update_progress)
    
    def _run_simulation(self):
        """Run the simulation (called in a separate thread)."""
        try:
            # Create and run simulation
            from ..core.simulation import Simulation
            self.simulation = Simulation(settings)
            
            # This is a placeholder - actual implementation would depend on Simulation class
            # self.simulation.run(
            #     num_patients=int(self.num_patients.get()),
            #     random_seed=int(self.random_seed.get()),
            #     enable_multiprocessing=self.enable_mp.get(),
            #     num_processes=int(self.num_processes.get()) if self.enable_mp.get() else 0,
            #     progress_callback=self._progress_callback
            # )
            
            # Simulate progress for demonstration
            total_steps = 100
            for i in range(total_steps + 1):
                if not self.is_running:
                    break
                self.progress_value = i / total_steps
                time.sleep(0.1)  # Simulate work
            
            if self.is_running:
                # Simulation completed successfully
                self.progress_value = 1.0
                
                # Generate dummy data for preview
                dummy_data = np.random.rand(10) * 100
                
                # Update UI in the main thread
                self.after(0, lambda: self.simulation_completed(dummy_data))
        
        except Exception as e:
            # Handle errors
            self.after(0, lambda: self.simulation_error(str(e)))
    
    def _progress_callback(self, progress_value):
        """Callback for simulation progress updates."""
        self.progress_value = progress_value
    
    def update_progress(self):
        """Update the progress display."""
        if not self.is_running:
            return
        
        # Update progress bar
        self.progress['value'] = self.progress_value * 100
        
        # Update status
        if self.progress_value < 1.0:
            self.status_label.config(text="Running...")
        else:
            self.status_label.config(text="Completed")
        
        # Update elapsed time
        elapsed = time.time() - self.start_time
        elapsed_str = time.strftime('%H:%M:%S', time.gmtime(elapsed))
        self.time_label.config(text=elapsed_str)
        
        # Update patients processed (placeholder)
        total_patients = int(self.num_patients.get())
        processed = int(self.progress_value * total_patients)
        self.patients_label.config(text=f"{processed:,} / {total_patients:,}")
        
        # Update ETA
        if self.progress_value > 0:
            remaining = elapsed / self.progress_value - elapsed
            eta_str = time.strftime('%H:%M:%S', time.gmtime(remaining))
            self.eta_label.config(text=eta_str)
        
        # Schedule next update if still running
        if self.is_running:
            self.after(100, self.update_progress)
    
    def stop_simulation(self):
        """Stop the current simulation."""
        if not self.is_running:
            return
        
        self.is_running = False
        self.status_label.config(text="Stopping...")
        
        # Wait for thread to finish
        if self.simulation_thread and self.simulation_thread.is_alive():
            self.simulation_thread.join(timeout=1.0)
        
        # Update UI
        self.status_label.config(text="Stopped")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
    
    def simulation_completed(self, data):
        """Handle simulation completion."""
        self.is_running = False
        self.status_label.config(text="Completed")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.view_results_button.config(state=tk.NORMAL)
        
        # Update preview with results
        self.update_preview_plot(data)
        
        # Show completion message
        elapsed = time.time() - self.start_time
        elapsed_str = time.strftime('%H:%M:%S', time.gmtime(elapsed))
        messagebox.showinfo(
            "Simulation Complete", 
            f"Simulation completed successfully in {elapsed_str}."
        )
    
    def simulation_error(self, error_message):
        """Handle simulation error."""
        self.is_running = False
        self.status_label.config(text="Error")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        # Show error message
        messagebox.showerror("Simulation Error", f"An error occurred during simulation:\n{error_message}")
    
    def view_results(self):
        """Open the results viewer."""
        try:
            # Import here to avoid circular imports
            from .results_view import ResultsView
            
            # Create a new window for results
            results_window = tk.Toplevel(self.parent)
            results_window.title("Simulation Results")
            results_window.geometry("800x600")
            
            # Create results view in the new window
            results_view = ResultsView(results_window, self.simulation)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open results view: {str(e)}")